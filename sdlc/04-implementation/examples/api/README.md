# VibeMatch API Examples

This directory contains comprehensive, runnable examples for all VibeMatch API operations. These examples are designed to help developers quickly integrate with our platform.

## Quick Start

1. **Get Your API Key**: Register at [VibeMatch Developer Portal](https://developers.vibematch.io)
2. **Choose Your Language**: We provide examples in TypeScript, cURL, and Python
3. **Start with Authentication**: Begin with the [authentication flow](../../../README.md)

## Example Categories

### 🔐 [Authentication](./authentication/)
- [User Registration](./authentication/01-user-registration.md) - Complete registration flow with validation
- [Login Flow](./authentication/02-login-flow.md) - Login with error handling
- [Token Management](./authentication/03-token-management.md) - Refresh tokens and session handling
- [Password Reset](./authentication/04-password-reset.md) - Complete password reset flow
- [Logout](./authentication/05-logout.md) - Proper session termination

### 👤 [User Management](./user-management/)
- [Profile Operations](./user-management/01-profile-operations.md) - View and update user profiles
- [Account Settings](./user-management/02-account-settings.md) - Manage preferences and settings
- [GDPR Compliance](./user-management/03-gdpr-compliance.md) - Data export and deletion

### 🤖 [Agent Operations](./agents/)
- [Agent Discovery](./agents/01-agent-discovery.md) - Search and filter agents
- [Create Agent Profile](./agents/02-create-agent.md) - Convert user to agent
- [Update Agent Info](./agents/03-update-agent.md) - Manage agent capabilities
- [Performance Metrics](./agents/04-performance-metrics.md) - Track agent performance

### 📋 [Task Management](./tasks/)
- [Create Task](./tasks/01-create-task.md) - Submit new task with validation
- [Task Lifecycle](./tasks/02-task-lifecycle.md) - Complete task flow
- [List Tasks](./tasks/03-list-tasks.md) - Query and filter tasks
- [Cancel Task](./tasks/04-cancel-task.md) - Cancellation and refunds

### 💳 [Credits & Billing](./credits/)
- [Check Balance](./credits/01-check-balance.md) - View credit balance
- [Transaction History](./credits/02-transaction-history.md) - List transactions with filtering
- [Purchase Credits](./credits/03-purchase-credits.md) - Buy credit packages
- [Refund Process](./credits/04-refund-process.md) - Request and track refunds

### 🔄 [Real-time Updates](./websockets/)
- [WebSocket Connection](./websockets/01-connection.md) - Establish WebSocket connection
- [Event Subscriptions](./websockets/02-subscriptions.md) - Subscribe to events
- [Task Updates](./websockets/03-task-updates.md) - Real-time task status

### 🎯 [Multi-Agent Orchestration](./orchestration/)
- [Create Orchestration](./orchestration/01-create-orchestration.md) - Define multi-agent workflows
- [Track Progress](./orchestration/02-track-progress.md) - Monitor orchestration status
- [Handle Results](./orchestration/03-handle-results.md) - Process orchestration outputs

### ⚠️ [Error Handling](./errors/)
- [Common Errors](./errors/01-common-errors.md) - Handle typical error scenarios
- [Rate Limiting](./errors/02-rate-limiting.md) - Deal with rate limits
- [Validation Errors](./errors/03-validation-errors.md) - Field validation handling
- [Network Errors](./errors/04-network-errors.md) - Retry strategies

### 📄 [Pagination](./pagination/)
- [Basic Pagination](./pagination/01-basic-pagination.md) - Page through results
- [Cursor Pagination](./pagination/02-cursor-pagination.md) - Efficient large datasets
- [Sorting & Filtering](./pagination/03-sorting-filtering.md) - Advanced queries

## Environment Setup

### TypeScript/Node.js
```bash
npm install axios @types/node typescript
npm install --save-dev @types/axios
```

### Python
```bash
pip install requests python-dotenv
```

## Base URLs

- **Production**: `https://api.vibematch.io/api/v1`
- **Staging**: `https://staging-api.vibematch.io/api/v1`
- **Development**: `http://localhost:3001/api/v1`

## Authentication

All API requests (except registration and login) require a Bearer token:

```typescript
headers: {
  'Authorization': `Bearer ${YOUR_API_TOKEN}`,
  'Content-Type': 'application/json'
}
```

## Rate Limits

- **Anonymous**: 10 requests/minute
- **Authenticated**: 100 requests/minute
- **Premium**: 1000 requests/minute

## Common Response Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (duplicate resource)
- `429` - Too Many Requests
- `500` - Internal Server Error

## Support

- **Documentation**: https://docs.vibematch.io
- **API Status**: https://status.vibematch.io
- **Support Email**: <EMAIL>
- **Developer Forum**: https://forum.vibematch.io

## Example Structure

Each example follows this format:

1. **Overview** - What the example demonstrates
2. **Prerequisites** - Required setup
3. **The Request** - Complete, runnable code
4. **Success Response** - Expected response
5. **Error Handling** - Common errors and solutions
6. **Common Issues** - Troubleshooting tips
7. **Next Steps** - Related operations

## Quick Links

- [Postman Collection](https://www.postman.com/vibematch/workspace/vibematch-api)
- [OpenAPI Specs](../../../03-specifications/openapi/)
- [API Changelog](https://docs.vibematch.io/changelog)