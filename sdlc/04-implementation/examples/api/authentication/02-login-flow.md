# Login Flow Example

## Overview
This example demonstrates the complete login flow including authentication, token handling, and common error scenarios.

## Prerequisites
- Registered user account
- Valid email and password
- Secure storage for tokens (localStorage, secure cookies, or keychain)

## The Request

### TypeScript/JavaScript
```typescript
import axios, { AxiosError } from 'axios';

const API_BASE_URL = 'https://api.vibematch.io/api/v1';

interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

interface AuthResponse {
  user: {
    id: string;
    email: string;
    displayName: string;
    role: string;
    emailVerified: boolean;
  };
  token: string;
  refreshToken: string;
  expiresIn: number;
  requiresMFA?: boolean;
}

class AuthService {
  private static instance: AuthService;
  private token: string | null = null;
  private refreshToken: string | null = null;
  private tokenExpiry: Date | null = null;

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await axios.post<AuthResponse>(
        `${API_BASE_URL}/auth/login`,
        credentials,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      const data = response.data;

      // Store tokens securely
      this.token = data.token;
      this.refreshToken = data.refreshToken;
      this.tokenExpiry = new Date(Date.now() + data.expiresIn * 1000);

      // Persist tokens based on rememberMe flag
      if (credentials.rememberMe) {
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('refreshToken', data.refreshToken);
        localStorage.setItem('tokenExpiry', this.tokenExpiry.toISOString());
      } else {
        sessionStorage.setItem('authToken', data.token);
        sessionStorage.setItem('refreshToken', data.refreshToken);
        sessionStorage.setItem('tokenExpiry', this.tokenExpiry.toISOString());
      }

      // Set default authorization header for future requests
      axios.defaults.headers.common['Authorization'] = `Bearer ${data.token}`;

      return data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        this.handleLoginError(error);
      }
      throw error;
    }
  }

  private handleLoginError(error: AxiosError<any>): void {
    const errorData = error.response?.data;
    
    switch (errorData?.code) {
      case 'INVALID_CREDENTIALS':
        console.error('Invalid email or password');
        break;
      case 'ACCOUNT_LOCKED':
        console.error('Account locked due to too many failed attempts');
        console.log(`Try again after: ${errorData.details.unlockTime}`);
        break;
      case 'EMAIL_NOT_VERIFIED':
        console.error('Please verify your email before logging in');
        break;
      case 'ACCOUNT_SUSPENDED':
        console.error('Account suspended. Contact support.');
        break;
      case 'MFA_REQUIRED':
        console.log('MFA required. Redirecting to MFA page...');
        // Handle MFA flow
        break;
      default:
        console.error('Login failed:', errorData?.message || 'Unknown error');
    }
  }

  getToken(): string | null {
    // Check if token is expired
    if (this.tokenExpiry && new Date() > this.tokenExpiry) {
      console.log('Token expired, refreshing...');
      this.refreshAccessToken();
    }
    return this.token;
  }

  async refreshAccessToken(): Promise<void> {
    // See token refresh example in 03-token-management.md
  }

  logout(): void {
    this.token = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    
    // Clear from storage
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('tokenExpiry');
    sessionStorage.clear();
    
    // Remove authorization header
    delete axios.defaults.headers.common['Authorization'];
  }
}

// Example usage
async function performLogin() {
  const authService = AuthService.getInstance();
  
  try {
    const result = await authService.login({
      email: '<EMAIL>',
      password: 'SecureP@ss123',
      rememberMe: true
    });

    console.log('Login successful!');
    console.log('User:', result.user);
    console.log('Token expires in:', result.expiresIn, 'seconds');

    if (result.requiresMFA) {
      console.log('MFA required - redirect to MFA page');
      // Handle MFA flow
    }

    // Now you can make authenticated requests
    // The token is automatically included in headers
  } catch (error) {
    console.error('Login failed:', error);
  }
}

performLogin();
```

### cURL
```bash
# Basic login
curl -X POST https://api.vibematch.io/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecureP@ss123",
    "rememberMe": false
  }'

# Login with remember me
curl -X POST https://api.vibematch.io/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecureP@ss123",
    "rememberMe": true
  }'
```

### Python
```python
import requests
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

class VibeMatchAuth:
    """Handle authentication for VibeMatch API"""
    
    def __init__(self, base_url: str = 'https://api.vibematch.io/api/v1'):
        self.base_url = base_url
        self.token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.token_expiry: Optional[datetime] = None
        self.session = requests.Session()
    
    def login(self, email: str, password: str, remember_me: bool = False) -> Dict[str, Any]:
        """
        Login to VibeMatch
        
        Args:
            email: User's email address
            password: User's password
            remember_me: Whether to extend session duration
            
        Returns:
            Authentication response with user data and tokens
        """
        endpoint = f"{self.base_url}/auth/login"
        
        payload = {
            'email': email,
            'password': password,
            'rememberMe': remember_me
        }
        
        try:
            response = self.session.post(
                endpoint,
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Store tokens
                self.token = data['token']
                self.refresh_token = data['refreshToken']
                self.token_expiry = datetime.now() + timedelta(seconds=data['expiresIn'])
                
                # Set authorization header for future requests
                self.session.headers['Authorization'] = f"Bearer {self.token}"
                
                # Save tokens to file if remember_me is True
                if remember_me:
                    self._save_tokens()
                
                print(f"Login successful! Welcome, {data['user']['displayName']}")
                return data
                
            else:
                error_data = response.json()
                self._handle_login_error(error_data)
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"Network error during login: {e}")
            return None
    
    def _handle_login_error(self, error_data: Dict[str, Any]):
        """Handle specific login errors"""
        error_code = error_data.get('code', 'UNKNOWN')
        message = error_data.get('message', 'Login failed')
        
        error_handlers = {
            'INVALID_CREDENTIALS': 'Invalid email or password. Please check your credentials.',
            'ACCOUNT_LOCKED': f"Account locked. {error_data.get('details', {}).get('message', '')}",
            'EMAIL_NOT_VERIFIED': 'Please verify your email address before logging in.',
            'ACCOUNT_SUSPENDED': 'Your account has been suspended. Please contact support.',
            'MFA_REQUIRED': 'Multi-factor authentication required.'
        }
        
        print(f"Login Error: {error_handlers.get(error_code, message)}")
        
        if error_code == 'ACCOUNT_LOCKED':
            unlock_time = error_data.get('details', {}).get('unlockTime')
            if unlock_time:
                print(f"Account will be unlocked at: {unlock_time}")
    
    def _save_tokens(self):
        """Save tokens to secure storage"""
        tokens = {
            'token': self.token,
            'refresh_token': self.refresh_token,
            'expiry': self.token_expiry.isoformat() if self.token_expiry else None
        }
        
        # In production, use secure storage like keyring
        with open('.vibematch_tokens.json', 'w') as f:
            json.dump(tokens, f)
        
        print("Tokens saved for future use")
    
    def load_saved_tokens(self) -> bool:
        """Load previously saved tokens"""
        try:
            with open('.vibematch_tokens.json', 'r') as f:
                tokens = json.load(f)
            
            self.token = tokens.get('token')
            self.refresh_token = tokens.get('refresh_token')
            
            if tokens.get('expiry'):
                self.token_expiry = datetime.fromisoformat(tokens['expiry'])
                
                # Check if token is still valid
                if self.token_expiry > datetime.now():
                    self.session.headers['Authorization'] = f"Bearer {self.token}"
                    return True
                else:
                    print("Saved token has expired")
            
            return False
            
        except FileNotFoundError:
            return False
    
    def make_authenticated_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make an authenticated API request"""
        if not self.token:
            raise ValueError("Not authenticated. Please login first.")
        
        # Check token expiry
        if self.token_expiry and datetime.now() > self.token_expiry:
            print("Token expired. Please refresh or login again.")
            # In production, implement automatic token refresh here
        
        url = f"{self.base_url}{endpoint}"
        return self.session.request(method, url, **kwargs)
    
    def logout(self):
        """Logout and clear stored tokens"""
        self.token = None
        self.refresh_token = None
        self.token_expiry = None
        self.session.headers.pop('Authorization', None)
        
        # Remove saved tokens
        try:
            import os
            os.remove('.vibematch_tokens.json')
        except FileNotFoundError:
            pass
        
        print("Logged out successfully")

# Example usage
if __name__ == "__main__":
    auth = VibeMatchAuth()
    
    # Try to load saved tokens first
    if auth.load_saved_tokens():
        print("Loaded saved authentication tokens")
    else:
        # Perform login
        result = auth.login(
            email="<EMAIL>",
            password="SecureP@ss123",
            remember_me=True
        )
        
        if result:
            print(f"User ID: {result['user']['id']}")
            print(f"Role: {result['user']['role']}")
            print(f"Token expires in: {result['expiresIn']} seconds")
            
            # Example authenticated request
            try:
                profile_response = auth.make_authenticated_request('GET', '/users/profile')
                if profile_response.status_code == 200:
                    print("Profile data:", profile_response.json())
            except Exception as e:
                print(f"Failed to fetch profile: {e}")
```

## Success Response
```json
{
  "user": {
    "id": "usr_1234567890abcdef",
    "email": "<EMAIL>",
    "displayName": "John Smith",
    "role": "VerifiedUser",
    "emailVerified": true,
    "profileComplete": true,
    "createdAt": "2024-01-15T10:30:00Z",
    "lastLoginAt": "2024-01-20T14:22:30Z"
  },
  "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjE2...",
  "refreshToken": "ref_abcdef1234567890...",
  "expiresIn": 3600,
  "requiresMFA": false
}
```

## Error Handling

### Invalid Credentials (401)
```json
{
  "code": "INVALID_CREDENTIALS",
  "message": "Invalid email or password",
  "timestamp": "2024-01-20T14:22:30Z",
  "path": "/api/v1/auth/login"
}
```

### Account Locked (423)
```json
{
  "code": "ACCOUNT_LOCKED",
  "message": "Account temporarily locked due to multiple failed login attempts",
  "details": {
    "attempts": 5,
    "unlockTime": "2024-01-20T14:52:30Z",
    "message": "Please try again in 30 minutes"
  }
}
```

### Email Not Verified (403)
```json
{
  "code": "EMAIL_NOT_VERIFIED",
  "message": "Email verification required",
  "details": {
    "email": "<EMAIL>",
    "resendUrl": "/api/v1/auth/resend-verification"
  }
}
```

### MFA Required (202)
```json
{
  "code": "MFA_REQUIRED",
  "message": "Multi-factor authentication required",
  "mfaToken": "mfa_token_abc123...",
  "methods": ["totp", "sms"],
  "expiresIn": 300
}
```

## Common Issues

### Issue 1: Account Locked After Failed Attempts
**Problem**: After 5 failed login attempts, account is locked for 30 minutes.

**Solution**: 
- Wait for the lockout period to expire
- Use password reset if you've forgotten your password
- Contact support if you suspect unauthorized access attempts

**Prevention**:
- Use a password manager
- Enable MFA for additional security
- Monitor login activity in your account settings

### Issue 2: Token Storage Security
**Problem**: Storing tokens insecurely can lead to unauthorized access.

**Best Practices**:
- **Web**: Use httpOnly secure cookies or encrypted localStorage
- **Mobile**: Use platform keychain/keystore
- **Desktop**: Use OS credential manager
- Never store tokens in plain text files
- Clear tokens on logout

### Issue 3: Handling Token Expiry
**Problem**: Tokens expire after 1 hour (3600 seconds).

**Solution**: Implement automatic token refresh:
```typescript
// Check token before each request
if (tokenExpiry && new Date() > tokenExpiry) {
  await refreshToken();
}
```

### Issue 4: Remember Me Implementation
**Problem**: Need to balance security with user convenience.

**Recommendations**:
- `rememberMe: false` - Session tokens (1 hour)
- `rememberMe: true` - Extended tokens (30 days)
- Always require re-authentication for sensitive operations
- Implement device tracking for security

## Security Best Practices

1. **Always use HTTPS** in production
2. **Implement rate limiting** on your client
3. **Store tokens securely** - never in plain text
4. **Clear tokens on logout** from all storage locations
5. **Implement token refresh** before expiry
6. **Monitor failed login attempts** and alert users
7. **Use MFA** for high-value accounts

## Session Management Tips

```typescript
// Implement session timeout warning
const WARNING_TIME = 5 * 60 * 1000; // 5 minutes before expiry

function setupSessionWarning(expiresIn: number) {
  const warningTime = (expiresIn * 1000) - WARNING_TIME;
  
  setTimeout(() => {
    if (confirm('Your session will expire soon. Stay logged in?')) {
      refreshToken();
    }
  }, warningTime);
}
```

## Next Steps

After successful login:
1. **Complete Profile** - Fill in any missing profile information
2. **Set Up MFA** - Enable multi-factor authentication for security
3. **Browse Agents** - Start exploring available agents
4. **Create First Task** - Submit your first task request
5. **Purchase Credits** - Add credits to your account

## Related Examples
- [Token Management](./03-token-management.md)
- [Password Reset](./04-password-reset.md)
- [MFA Setup](./06-mfa-setup.md)
- [Profile Operations](../user-management/01-profile-operations.md)