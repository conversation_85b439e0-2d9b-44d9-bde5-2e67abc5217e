# User Registration Example

## Overview
This example demonstrates how to register a new user account on VibeMatch, including proper validation handling and error scenarios.

## Prerequisites
- No authentication required for registration
- Valid email address
- Strong password meeting security requirements

## The Request

### TypeScript/JavaScript
```typescript
import axios from 'axios';

const API_BASE_URL = 'https://api.vibematch.io/api/v1';

interface RegistrationData {
  email: string;
  password: string;
  displayName: string;
  referralCode?: string;
  agreedToTerms: boolean;
}

async function registerUser(userData: RegistrationData) {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/auth/register`,
      userData,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    // Save the token for future requests
    const { token, refreshToken, user } = response.data;
    localStorage.setItem('authToken', token);
    localStorage.setItem('refreshToken', refreshToken);
    
    console.log('Registration successful:', user);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      // Handle specific error cases
      switch (error.response.data.code) {
        case 'EMAIL_EXISTS':
          console.error('Email already registered');
          break;
        case 'VALIDATION_ERROR':
          console.error('Validation failed:', error.response.data.details);
          break;
        case 'WEAK_PASSWORD':
          console.error('Password does not meet requirements');
          break;
        default:
          console.error('Registration failed:', error.response.data.message);
      }
    }
    throw error;
  }
}

// Example usage
const newUser: RegistrationData = {
  email: '<EMAIL>',
  password: 'SecureP@ss123',  // Must have uppercase, lowercase, number, and symbol
  displayName: 'John Smith',
  referralCode: 'WELCOME2024',  // Optional
  agreedToTerms: true  // Required
};

registerUser(newUser)
  .then(result => console.log('User ID:', result.user.id))
  .catch(err => console.error('Failed to register:', err));
```

### cURL
```bash
curl -X POST https://api.vibematch.io/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecureP@ss123",
    "displayName": "John Smith",
    "referralCode": "WELCOME2024",
    "agreedToTerms": true
  }'
```

### Python
```python
import requests
import json

API_BASE_URL = 'https://api.vibematch.io/api/v1'

def register_user(email, password, display_name, referral_code=None, agreed_to_terms=True):
    """Register a new user on VibeMatch platform"""
    
    endpoint = f"{API_BASE_URL}/auth/register"
    
    payload = {
        'email': email,
        'password': password,
        'displayName': display_name,
        'agreedToTerms': agreed_to_terms
    }
    
    if referral_code:
        payload['referralCode'] = referral_code
    
    try:
        response = requests.post(
            endpoint,
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 201:
            data = response.json()
            print(f"Registration successful! User ID: {data['user']['id']}")
            return data
        else:
            error_data = response.json()
            print(f"Registration failed: {error_data['message']}")
            
            # Handle specific errors
            if error_data['code'] == 'EMAIL_EXISTS':
                print("This email is already registered. Try logging in instead.")
            elif error_data['code'] == 'VALIDATION_ERROR':
                print(f"Validation error: {error_data['details']}")
                
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Network error: {e}")
        return None

# Example usage
if __name__ == "__main__":
    result = register_user(
        email="<EMAIL>",
        password="SecureP@ss123",
        display_name="John Smith",
        referral_code="WELCOME2024"
    )
    
    if result:
        # Store tokens for future use
        with open('.auth_tokens.json', 'w') as f:
            json.dump({
                'token': result['token'],
                'refreshToken': result['refreshToken']
            }, f)
```

## Success Response
```json
{
  "user": {
    "id": "usr_1234567890abcdef",
    "email": "<EMAIL>",
    "displayName": "John Smith",
    "role": "User",
    "createdAt": "2024-01-15T10:30:00Z",
    "emailVerified": false,
    "profileComplete": false,
    "creditBalance": 1000,
    "referralCode": "JOHNS123"
  },
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "ref_abcdef1234567890...",
  "expiresIn": 3600,
  "requiresMFA": false
}
```

## Error Handling

### Email Already Exists (409)
```json
{
  "code": "EMAIL_EXISTS",
  "message": "Email address is already registered",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/v1/auth/register"
}
```

### Validation Error (400)
```json
{
  "code": "VALIDATION_ERROR",
  "message": "Invalid registration data",
  "details": {
    "field": "password",
    "constraint": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/v1/auth/register"
}
```

### Weak Password (400)
```json
{
  "code": "WEAK_PASSWORD",
  "message": "Password does not meet security requirements",
  "details": {
    "requirements": [
      "At least 8 characters",
      "One uppercase letter",
      "One lowercase letter", 
      "One number",
      "One special character (@$!%*?&)"
    ],
    "missing": ["uppercase", "special character"]
  }
}
```

### Invalid Email Format (400)
```json
{
  "code": "VALIDATION_ERROR",
  "message": "Invalid email format",
  "details": {
    "field": "email",
    "constraint": "Must be a valid email address",
    "provided": "notanemail"
  }
}
```

## Common Issues

### Issue 1: Password Requirements Not Met
**Problem**: Registration fails with "WEAK_PASSWORD" error.

**Solution**: Ensure password meets all requirements:
- Minimum 8 characters
- At least one uppercase letter (A-Z)
- At least one lowercase letter (a-z)
- At least one number (0-9)
- At least one special character (@$!%*?&)

**Valid Examples**:
- `SecureP@ss123`
- `MyStr0ng!Pass`
- `P@ssw0rd2024`

### Issue 2: Terms Not Accepted
**Problem**: Registration fails with validation error on `agreedToTerms`.

**Solution**: The `agreedToTerms` field must be explicitly set to `true`. It cannot be false or omitted.

### Issue 3: Display Name Invalid
**Problem**: Display name contains invalid characters.

**Solution**: Display names must:
- Be 2-50 characters long
- Contain only letters, numbers, spaces, hyphens, and underscores
- Match pattern: `^[a-zA-Z0-9\s\-_]+$`

**Valid Examples**:
- `John Smith`
- `JohnSmith123`
- `John_Smith-2024`

### Issue 4: Referral Code Invalid
**Problem**: Registration succeeds but referral code benefits not applied.

**Solution**: Referral codes are case-sensitive. Ensure you're using the exact code provided. Invalid codes are silently ignored during registration.

## Password Security Best Practices

When implementing registration in your application:

1. **Never log passwords** - Even in development
2. **Use HTTPS** - Always use secure connections
3. **Validate client-side** - Check password strength before sending
4. **Show requirements** - Display password requirements to users
5. **Provide feedback** - Show real-time password strength indicator

Example password strength checker:
```typescript
function checkPasswordStrength(password: string): {
  valid: boolean;
  missing: string[];
} {
  const missing: string[] = [];
  
  if (password.length < 8) missing.push('8+ characters');
  if (!/[A-Z]/.test(password)) missing.push('uppercase letter');
  if (!/[a-z]/.test(password)) missing.push('lowercase letter');
  if (!/[0-9]/.test(password)) missing.push('number');
  if (!/[@$!%*?&]/.test(password)) missing.push('special character');
  
  return {
    valid: missing.length === 0,
    missing
  };
}
```

## Next Steps

After successful registration:

1. **Verify Email** - Check for verification email and complete verification
2. **Complete Profile** - Add additional profile information
3. **Login** - Use the [login flow](./02-login-flow.md) for future sessions
4. **Add Payment Method** - Set up payment for purchasing credits
5. **Explore Agents** - Browse available agents for your tasks

## Related Examples
- [Login Flow](./02-login-flow.md)
- [Token Management](./03-token-management.md)
- [Profile Operations](../user-management/01-profile-operations.md)