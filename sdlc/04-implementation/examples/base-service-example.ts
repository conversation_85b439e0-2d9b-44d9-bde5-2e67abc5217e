/**
 * Base Service Example
 * 
 * This is a template for creating new microservices in VibeMatch.
 * Copy this file and modify for your specific service needs.
 */

import express, { Application, Request, Response } from 'express';
import { createServer, Server } from 'http';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { Logger } from '@vibe-match/logger';
import { EventBus } from '@vibe-match/event-bus';
import { HealthCheck } from '@vibe-match/health';
import { errorHandler, notFoundHandler } from '@vibe-match/middleware';
import { initializeFirestore } from '@vibe-match/database';

export interface ServiceConfig {
  name: string;
  port: number;
  version: string;
  environment: 'development' | 'staging' | 'production';
}

export abstract class BaseService {
  protected readonly app: Application;
  protected readonly server: Server;
  protected readonly logger: Logger;
  protected readonly eventBus: EventBus;
  protected readonly config: ServiceConfig;
  protected db: FirebaseFirestore.Firestore;

  constructor(config: ServiceConfig) {
    this.config = config;
    this.app = express();
    this.server = createServer(this.app);
    this.logger = new Logger(config.name);
    this.eventBus = new EventBus(config.name);
  }

  /**
   * Initialize the service
   */
  async initialize(): Promise<void> {
    try {
      // Initialize database
      await this.initializeDatabase();

      // Setup middleware
      this.setupMiddleware();

      // Setup routes
      await this.setupRoutes();

      // Register event handlers
      await this.registerEventHandlers();

      // Setup error handling
      this.setupErrorHandling();

      // Start server
      await this.start();

      this.logger.info(`${this.config.name} service initialized successfully`);
    } catch (error) {
      this.logger.error('Failed to initialize service', { error });
      throw error;
    }
  }

  /**
   * Initialize database connection
   */
  protected async initializeDatabase(): Promise<void> {
    this.db = await initializeFirestore({
      projectId: process.env.GCP_PROJECT_ID,
      keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS,
    });
    this.logger.info('Database connection established');
  }

  /**
   * Setup Express middleware
   */
  protected setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet());
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
      credentials: true,
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Compression
    this.app.use(compression());

    // Request logging
    this.app.use((req: Request, res: Response, next) => {
      const start = Date.now();
      res.on('finish', () => {
        const duration = Date.now() - start;
        this.logger.info('Request processed', {
          method: req.method,
          path: req.path,
          status: res.statusCode,
          duration,
          ip: req.ip,
        });
      });
      next();
    });

    // Health check endpoint
    this.app.use('/health', new HealthCheck({
      service: this.config.name,
      version: this.config.version,
      checks: [
        { name: 'database', check: () => this.checkDatabase() },
        { name: 'eventBus', check: () => this.eventBus.isConnected() },
      ],
    }).router);
  }

  /**
   * Setup service-specific routes
   * Override this method in your service
   */
  protected abstract setupRoutes(): Promise<void>;

  /**
   * Register event handlers
   * Override this method in your service
   */
  protected abstract registerEventHandlers(): Promise<void>;

  /**
   * Setup error handling middleware
   */
  protected setupErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler(this.logger));
  }

  /**
   * Start the HTTP server
   */
  protected async start(): Promise<void> {
    return new Promise((resolve) => {
      this.server.listen(this.config.port, () => {
        this.logger.info(`Server started on port ${this.config.port}`);
        resolve();
      });
    });
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down service...');

    // Stop accepting new connections
    this.server.close();

    // Close event bus connections
    await this.eventBus.close();

    // Close database connections
    await this.db.terminate();

    this.logger.info('Service shut down successfully');
  }

  /**
   * Check database connectivity
   */
  protected async checkDatabase(): Promise<boolean> {
    try {
      await this.db.collection('_health').doc('check').get();
      return true;
    } catch (error) {
      this.logger.error('Database health check failed', { error });
      return false;
    }
  }
}

/**
 * Example implementation of a specific service
 */
export class ExampleService extends BaseService {
  private exampleRepository: ExampleRepository;

  constructor() {
    super({
      name: 'example-service',
      port: parseInt(process.env.PORT || '3000'),
      version: process.env.SERVICE_VERSION || '1.0.0',
      environment: (process.env.NODE_ENV as any) || 'development',
    });
  }

  protected async setupRoutes(): Promise<void> {
    // Initialize repositories
    this.exampleRepository = new ExampleRepository(this.db);

    // Setup route handlers
    const router = express.Router();

    // GET /api/examples
    router.get('/examples', async (req: Request, res: Response) => {
      try {
        const examples = await this.exampleRepository.findAll();
        res.json({ success: true, data: examples });
      } catch (error) {
        this.logger.error('Failed to fetch examples', { error });
        res.status(500).json({ success: false, error: 'Internal server error' });
      }
    });

    // POST /api/examples
    router.post('/examples', async (req: Request, res: Response) => {
      try {
        const example = await this.exampleRepository.create(req.body);
        
        // Publish event
        await this.eventBus.publish('example.created', {
          exampleId: example.id,
          userId: req.user?.id,
        });

        res.status(201).json({ success: true, data: example });
      } catch (error) {
        this.logger.error('Failed to create example', { error });
        res.status(500).json({ success: false, error: 'Internal server error' });
      }
    });

    // Mount router
    this.app.use('/api', router);
  }

  protected async registerEventHandlers(): Promise<void> {
    // Subscribe to relevant events
    await this.eventBus.subscribe('user.created', async (event) => {
      this.logger.info('User created event received', { event });
      // Handle user created event
    });

    await this.eventBus.subscribe('task.completed', async (event) => {
      this.logger.info('Task completed event received', { event });
      // Handle task completed event
    });
  }
}

// Repository example (usually in separate file)
class ExampleRepository {
  constructor(private db: FirebaseFirestore.Firestore) {}

  async findAll(): Promise<any[]> {
    const snapshot = await this.db.collection('examples').get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  async create(data: any): Promise<any> {
    const docRef = await this.db.collection('examples').add({
      ...data,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return { id: docRef.id, ...data };
  }
}

// Service entry point (usually in separate file: index.ts)
if (require.main === module) {
  const service = new ExampleService();
  
  service.initialize().catch((error) => {
    console.error('Failed to start service:', error);
    process.exit(1);
  });

  // Graceful shutdown handlers
  process.on('SIGTERM', async () => {
    await service.shutdown();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    await service.shutdown();
    process.exit(0);
  });
}