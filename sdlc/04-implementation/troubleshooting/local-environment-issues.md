# Local Environment Issues

> **Purpose**: Troubleshoot common local development environment problems  
> **Complexity**: 🟡 Medium

## Common Issues

### 1. Docker Compose Won't Start

#### Symptoms
```
ERROR: Cannot connect to the Docker daemon
docker-compose: command not found
Bind for 0.0.0.0:3000 failed: port is already allocated
```

#### Solutions

**Docker daemon not running:**
```bash
# macOS
open -a Docker

# Linux
sudo systemctl start docker

# Verify
docker ps
```

**Port conflicts:**
```bash
# Find process using port
lsof -i :3000

# Kill process
kill -9 <PID>

# Or change port in docker-compose.yml
# Change: ports: - "3000:3000"
# To: ports: - "3001:3000"
```

**Permission issues:**
```bash
# Add user to docker group
sudo usermod -aG docker $USER

# Logout and login again, or
newgrp docker
```

### 2. NPM Install Failures

#### Symptoms
```
npm ERR! code EACCES
npm ERR! syscall access
ENOSPC: System limit for number of file watchers reached
```

#### Solutions

**Permission errors:**
```bash
# Clean npm cache
npm cache clean --force

# Remove node_modules
rm -rf node_modules package-lock.json

# Reinstall with proper permissions
npm install
```

**File watcher limits (Linux):**
```bash
# Increase limit temporarily
sudo sysctl fs.inotify.max_user_watches=524288

# Permanent fix
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

**Wrong Node version:**
```bash
# Check required version
cat .nvmrc

# Install and use correct version
nvm install 20.11.0
nvm use 20.11.0

# Set as default
nvm alias default 20.11.0
```

### 3. Service Connection Issues

#### Symptoms
```
Error: connect ECONNREFUSED 127.0.0.1:6379
Failed to connect to database
Service 'api-gateway' is not reachable
```

#### Solutions

**Services not running:**
```bash
# Check service status
docker-compose ps

# Restart specific service
docker-compose restart api-gateway

# View service logs
docker-compose logs -f api-gateway
```

**Network issues:**
```bash
# Recreate network
docker network prune
docker-compose down
docker-compose up -d

# Verify network
docker network ls
docker network inspect vibe-match_default
```

**Environment variables:**
```bash
# Check if .env exists
ls -la .env

# Copy from example
cp .env.example .env

# Verify values
cat .env | grep -E "(DATABASE|REDIS|API)"
```

### 4. Build Failures

#### Symptoms
```
Module build failed: Error: Cannot find module
TypeScript error TS2307: Cannot find module
Docker build failed: no such file or directory
```

#### Solutions

**Missing dependencies:**
```bash
# Clean install
rm -rf node_modules
npm ci

# If using workspaces
npm install --workspaces
```

**TypeScript errors:**
```bash
# Rebuild TypeScript
npm run build:clean
npm run build

# Check tsconfig
npx tsc --showConfig
```

**Docker build cache:**
```bash
# Build without cache
docker-compose build --no-cache

# Or specific service
docker-compose build --no-cache api-gateway
```

### 5. Database Issues

#### Symptoms
```
Firestore emulator not running
Cannot connect to Firestore
Collection not found
```

#### Solutions

**Start Firestore emulator:**
```bash
# Install Firebase tools
npm install -g firebase-tools

# Start emulator
firebase emulators:start --only firestore

# Or via docker-compose
docker-compose up firestore-emulator
```

**Reset database:**
```bash
# Clear emulator data
rm -rf ./firebase-data

# Restart emulator
docker-compose restart firestore-emulator

# Run migrations
npm run db:seed
```

## Prevention Checklist

### Daily Startup
```bash
#!/bin/bash
# Save as: scripts/dev-start.sh

echo "🚀 Starting VibeMatch development environment..."

# 1. Check Docker
if ! docker info > /dev/null 2>&1; then
  echo "❌ Docker is not running"
  exit 1
fi

# 2. Clean start
docker-compose down
docker system prune -f

# 3. Check Node version
required_node=$(cat .nvmrc)
current_node=$(node -v)
if [[ "$current_node" != *"$required_node"* ]]; then
  echo "⚠️  Wrong Node version. Installing $required_node"
  nvm install $required_node
  nvm use $required_node
fi

# 4. Install dependencies
npm ci

# 5. Start services
docker-compose up -d

# 6. Wait for health
echo "⏳ Waiting for services..."
sleep 10

# 7. Run health check
npm run health:check

echo "✅ Development environment ready!"
```

### Environment Validation
```bash
# Check all requirements
npm run env:check

# This runs:
# - Docker version check
# - Node version check  
# - Required ports available
# - Environment variables set
# - Database connectivity
```

## Quick Recovery

### Nuclear Option
When everything else fails:
```bash
# WARNING: This will delete all local data
docker-compose down -v
docker system prune -af
rm -rf node_modules
rm -rf ~/.npm
npm cache clean --force
npm install
docker-compose up --build
```

---
**Document Owner**: Platform Team  
**Last Updated**: 2025-06-26  
**Parent**: [./](./)