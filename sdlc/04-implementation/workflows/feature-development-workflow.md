# Feature Development Workflow

> **Purpose**: Step-by-step guide for implementing new features  
> **Time Estimate**: 2-5 days depending on complexity

## Prerequisites
- [ ] JIRA ticket created and assigned
- [ ] Local development environment setup
- [ ] Latest main branch pulled
- [ ] Design documents reviewed

## Workflow Steps

### 1. Planning & Setup (Day 1 Morning)

#### Create Feature Branch
```bash
# Update main branch
git checkout main
git pull origin main

# Create feature branch
git checkout -b feature/JIRA-XXX-description

# Example: feature/VM-123-multi-agent-matching
```

#### Review Requirements
- [ ] Read JIRA ticket thoroughly
- [ ] Review related design docs
- [ ] Identify affected services
- [ ] List API changes needed
- [ ] Note database modifications

#### Set Up Development Environment
```bash
# Install dependencies
npm install

# Start local services
docker-compose up -d

# Run database migrations
npm run db:migrate

# Verify everything works
npm run test:smoke
```

### 2. Implementation (Day 1-3)

#### Write Tests First (TDD)
```typescript
// Start with integration test
describe('Multi-Agent Matching', () => {
  it('should match multiple agents for complex tasks', async () => {
    // Given
    const task = await createTask({
      type: 'multi_agent',
      requirements: ['code_review', 'documentation']
    });
    
    // When
    const matches = await matchingEngine.findMatches(task);
    
    // Then
    expect(matches).toHaveLength(2);
    expect(matches[0].capabilities).toContain('code_review');
    expect(matches[1].capabilities).toContain('documentation');
  });
});
```

#### Implement Feature
```typescript
// Follow existing patterns
export class MatchingEngine {
  async findMatches(task: Task): Promise<Match[]> {
    if (task.type === 'multi_agent') {
      return this.findMultiAgentMatches(task);
    }
    return this.findSingleAgentMatch(task);
  }
  
  private async findMultiAgentMatches(task: Task): Promise<Match[]> {
    // Implementation here
  }
}
```

#### Update API Specifications
```yaml
# openapi/matching-engine-api.yaml
paths:
  /matches:
    post:
      requestBody:
        content:
          application/json:
            schema:
              properties:
                type:
                  enum: [single_agent, multi_agent]  # Added
```

### 3. Testing & Quality (Day 3-4)

#### Run All Tests
```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e

# Coverage report
npm run test:coverage
```

#### Code Quality Checks
```bash
# Linting
npm run lint

# Type checking
npm run type-check

# Security scan
npm run security:scan
```

#### Performance Testing
```bash
# Run performance tests
npm run test:performance -- --feature=multi-agent-matching

# Check results
cat performance-results.json
```

### 4. Documentation (Day 4)

#### Update Code Documentation
```typescript
/**
 * Finds matching agents for a task
 * @param task - Task requiring agents
 * @returns Array of matched agents with scores
 * @throws MatchingError if no suitable agents found
 */
async findMatches(task: Task): Promise<Match[]> {
  // ...
}
```

#### Update README
- [ ] Add feature description
- [ ] Include usage examples
- [ ] Document configuration options
- [ ] Add troubleshooting section

#### Update API Docs
- [ ] Regenerate OpenAPI docs
- [ ] Update postman collection
- [ ] Add example requests/responses

### 5. Review & Deployment (Day 4-5)

#### Self Review Checklist
- [ ] All tests passing
- [ ] No linting errors
- [ ] Documentation complete
- [ ] No hardcoded values
- [ ] Error handling added
- [ ] Logging implemented
- [ ] Metrics/monitoring added

#### Create Pull Request
```bash
# Commit changes
git add .
git commit -m "feat(matching): Add multi-agent matching support

- Implement multi-agent matching algorithm
- Add orchestration support for agent teams  
- Update API to accept task type parameter
- Add comprehensive test coverage

Closes JIRA-XXX"

# Push branch
git push origin feature/JIRA-XXX-description

# Create PR
gh pr create \
  --title "feat(matching): Add multi-agent matching support" \
  --body "$(cat .github/pull_request_template.md)" \
  --assignee @me \
  --label enhancement
```

#### PR Review Process
1. Automated checks must pass
2. Request review from team lead
3. Address review comments
4. Get approval from 2 reviewers
5. Merge when all checks pass

### 6. Deployment

#### Deploy to Staging
```bash
# Merge to develop branch
git checkout develop
git merge feature/JIRA-XXX-description

# Automatic deployment triggered
# Monitor deployment
kubectl logs -f deployment/matching-engine -n staging
```

#### Staging Validation
- [ ] Run smoke tests
- [ ] Verify feature works
- [ ] Check monitoring dashboards
- [ ] Review error logs

#### Production Deployment
```bash
# Create release PR
gh pr create \
  --base main \
  --head develop \
  --title "Release: v1.2.0"

# After approval and merge
# Monitor production deployment
kubectl logs -f deployment/matching-engine -n production
```

## Validation Checklist

### Functional Validation
- [ ] Feature works as specified
- [ ] Edge cases handled
- [ ] Error messages helpful
- [ ] Performance acceptable

### Technical Validation  
- [ ] Tests provide >80% coverage
- [ ] No security vulnerabilities
- [ ] Follows coding standards
- [ ] Database migrations safe

### Operational Validation
- [ ] Logging sufficient
- [ ] Metrics exported
- [ ] Alerts configured
- [ ] Documentation complete

## Troubleshooting

### Common Issues

#### Tests Failing Locally
```bash
# Reset test database
npm run db:test:reset

# Clear test cache
npm run test:clear-cache

# Run single test for debugging
npm test -- --testNamePattern="multi-agent"
```

#### Docker Issues
```bash
# Rebuild containers
docker-compose down
docker-compose build --no-cache
docker-compose up
```

#### Git Conflicts
```bash
# Update feature branch
git checkout main
git pull origin main
git checkout feature/JIRA-XXX-description
git rebase main

# Resolve conflicts and continue
git rebase --continue
```

## Templates

### Commit Message Template
```
feat|fix|docs|style|refactor|test|chore(scope): Subject

- Bullet point description
- Another change description

Closes JIRA-XXX
```

### PR Description Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No new warnings
```

---
**Document Owner**: Platform Team  
**Last Updated**: 2025-06-26  
**Parent**: [./](./)