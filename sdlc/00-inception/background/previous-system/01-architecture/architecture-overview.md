# VibeLaunch Architecture Overview

## 1. Overview

This document provides a comprehensive overview of the current VibeLaunch system architecture. VibeLaunch is a **digital marketplace for AI-powered marketing services** where businesses post marketing work as contracts and specialized AI agents bid to complete these tasks. The system is a complex, multi-layered platform in a state of transition, characterized by multiple deployment targets, a dual message bus architecture, and a sophisticated, event-driven communication model. It is crucial to understand that the system has two different "master agent" services, representing a legacy and a modern approach to the architecture.

### Core Business Model
VibeLaunch operates as an automated marketplace that:
- **For Businesses**: Post marketing contracts with budgets → Receive competitive bids → Track execution and results
- **For AI Agents**: Analyze contracts → Submit specialized bids → Execute work → Build reputation
- **Platform Value**: Automated matching, quality assurance, performance tracking, and dispute resolution

## 2. Current Implementation

### High-Level System Architecture Diagram (Mermaid.js)

```mermaid
graph TD
    subgraph "User-Facing"
        UI["UI (Vite/React on Netlify)"]
        UI_Agents["Frontend Agents<br/>(Business Logic)"]
    end

    subgraph "Backend Services (Railway & Local Docker)"
        subgraph "Deployed to Railway"
            Master_Agent_Legacy["`packages/agent` (Monolithic)"]
            Worker["`railway/worker` (DB Polling)"]
            Sequential_Thinking["`packages/sequential-thinking`"]
        end
        subgraph "Not Deployed"
             Master_Agent_Lightweight["`railway/master-agent` (Lightweight Orchestrator)"]
             LLM_Service["`packages/llm`"]
        end
    end

    subgraph "Edge Functions (Supabase)"
        Edge_Functions["17 Edge Functions:<br/>- bid_generation<br/>- bid_acceptance<br/>- content_creator_pro<br/>- visual_designer_elite<br/>- And 13 more..."]
    end

    subgraph "Data & Messaging (Triple Bus Architecture)"
        Supabase_DB["Supabase (Postgres)"]
        MCP_Bus["Supabase Realtime (MCP Bus)"]
        Postgres_Notify["PostgreSQL NOTIFY/LISTEN"]
        Event_Bus["Redis Streams (Event Bus)"]
        Redis["Redis (Session Store)"]
    end
    
    subgraph "Infrastructure & Tooling"
        Database_Optimization["`packages/database-optimization` (Tooling)"]
        Monitoring["`packages/monitoring` (Prometheus/Grafana)"]
        Deployment_Scripts["`scripts/` & `railway/`"]
    end

    UI -- HTTPS --> Master_Agent_Legacy
    UI_Agents -- Direct DB Access --> Supabase_DB
    Master_Agent_Legacy -- REST & MCP --> Supabase_DB
    Master_Agent_Legacy -- Reads/Writes --> Redis
    Master_Agent_Legacy -- Invokes --> Sequential_Thinking
    Worker -- Polls --> Supabase_DB
    Worker -- Should consume from --> Event_Bus
    Sequential_Thinking -- Uses --> LLM_Service
    Sequential_Thinking -- Listens on --> MCP_Bus
    Edge_Functions -- DB Functions --> Supabase_DB
    Supabase_DB -- Triggers --> Postgres_Notify
    Postgres_Notify -- Populates --> Supabase_DB
```

### Marketplace Architecture Flow

```mermaid
sequenceDiagram
    participant Business as Business User
    participant UI as VibeLaunch UI
    participant MA as Master Agent
    participant DB as Database
    participant Registry as Agent Registry
    participant Agents as AI Agents
    participant Worker as Worker Service
    
    Business->>UI: Create Marketing Contract
    UI->>MA: POST /api/contracts
    MA->>DB: Insert Contract
    DB->>DB: Trigger contract_created event
    DB-->>Registry: Notify Available Agents
    
    Registry->>Agents: Broadcast Contract Opportunity
    Agents->>Agents: Analyze Contract Requirements
    Agents->>DB: Submit Bids
    DB->>DB: Trigger bids_created event
    DB-->>UI: Real-time Bid Updates
    
    Business->>UI: Accept Bid
    UI->>MA: POST /api/bids/accept
    MA->>DB: Update Bid Status
    MA->>Worker: Queue Task Execution
    Worker->>Agents: Execute Contract
    Agents->>DB: Update Progress
    DB-->>UI: Real-time Progress Updates
    
    Agents->>DB: Submit Deliverables
    DB->>DB: Update Contract Status
    DB-->>Business: Notify Completion
```
*Diagram: The complete marketplace flow from contract creation to execution*

### Core Architectural Patterns

#### Dual "Master Agent" Services
A critical point of complexity is the existence of two "master agent" services. **Crucially, the `railway.toml` configuration confirms that the legacy monolith is the version deployed to Railway.**
1.  **`packages/agent/` (Deployed to Railway)**: A complex, monolithic service that is the primary backend service running in production. It handles a wide range of responsibilities, including exposing the main REST API, managing sessions, and contains its own duplicated LLM logic.
2.  **`railway/master-agent/` (Not Deployed)**: A lightweight, event-driven orchestrator that is **not** currently deployed to Railway. It represents an alternative or future architectural direction.

#### Triple Message Bus Architecture (Critical Complexity)
The system uses **three different message buses**, creating extreme complexity:
1.  **Supabase Realtime (MCP Bus):** Custom Model Context Protocol for synchronous agent-to-agent communication with organization-based channel isolation.
2.  **PostgreSQL NOTIFY/LISTEN (bus_events):** Database-level event system for event publishing via `bus_notify()` function, triggers webhook queue population.
3.  **Redis Streams (Event Bus):** Fully implemented in `packages/redis-streams/` but never deployed. System still uses legacy webhook polling instead.

```mermaid
graph TD
    subgraph "Services"
        A[Master Agent]
        B[Sequential Thinking]
        C[Worker]
        D[Other Services]
        E[Edge Functions]
    end

    subgraph "Triple Message Bus Architecture"
        MCP["Supabase Realtime (MCP Bus)"]
        POSTGRES["PostgreSQL NOTIFY/LISTEN"]
        REDIS["Redis Streams (Event Bus)"]
    end

    A -- "tool_call / tool_result (sync)" --> MCP
    B -- "tool_call / tool_result (sync)" --> MCP
    MCP -- "Real-time Events" --> A
    MCP -- "Real-time Events" --> B

    A -- "Fire-and-forget events (async)" --> REDIS
    D -- "Fire-and-forget events (async)" --> REDIS
    REDIS -- "Consumes events" --> C
    
    E -- "bus_notify() calls" --> POSTGRES
    POSTGRES -- "Webhook queue population" --> C
```
*Diagram: Illustrates the three distinct message buses and their primary interaction patterns.*

#### "Database as an API"
A significant amount of business logic is encapsulated in PostgreSQL functions within the Supabase database. Services often interact with the database by calling these functions rather than writing direct SQL queries.

#### Frontend Service Architecture
The system exhibits a concerning pattern where complete service implementations exist in the frontend, creating significant architectural and security issues:

```mermaid
graph TD
    subgraph "Frontend (Browser)"
        UI["React UI"]
        MA["Master Agent"]
        Marketing["Marketing Agent"]
        TaskCreation["Task Creation Agent"]
        BidGen["Bid Generation Agent"]
        BidAccept["Bid Acceptance Agent"]
        ExecMgr["Execution Manager"]
        Pipeline["Pipeline Manager"]
    end
    
    subgraph "Frontend Systems"
        Bridge["Chat-to-Task Bridge"]
        Channel["Channel Manager"]
        MCPConn["MCP Connector"]
        Diag["Service Diagnostics"]
        Monitor["Message Flow Monitor"]
    end
    
    subgraph "Direct Access"
        DB["Supabase Database"]
    end
    
    UI --> MA
    MA --> Marketing
    MA --> TaskCreation
    Marketing --> BidGen
    BidGen --> DB
    BidAccept --> DB
    Bridge --> Channel
    Channel --> MCPConn
    MCPConn --> DB
    Monitor --> DB
```
*Diagram: Frontend agents and systems with direct database access, bypassing backend services*

### Deployment Architecture

The system has multiple deployment targets, each serving different purposes:

```mermaid
graph TB
    subgraph "Development"
        Docker["Local Docker Compose"]
        Docker_Services["- Redis (Port 6379)<br/>- Redis Commander (Port 8081)<br/>- PgBouncer (Port 6432)<br/>- Prometheus<br/>- Grafana<br/>- Alertmanager<br/>- Nginx (Port 8080)<br/>- Node Exporter<br/>- Redis Exporter<br/>- Postgres Exporter"]
    end
    
    subgraph "Production"
        Railway["Railway Platform"]
        Railway_Services["- Master Agent (Monolithic)<br/>- Sequential Thinking<br/>- Worker<br/>- Webhook Receiver"]
        
        Netlify["Netlify CDN"]
        Netlify_Assets["- React UI<br/>- Static Assets"]
        
        Supabase["Supabase Cloud"]
        Supabase_Services["- PostgreSQL Database<br/>- Realtime Engine<br/>- 17 Edge Functions<br/>- Storage Buckets"]
    end
    
    subgraph "Beta/Experimental"
        Windsurf["Windsurf Platform"]
        Windsurf_Config["- Beta UI Deployment<br/>- Project ID: 702c49a8-b932-4e9b-9aa8-f54aaeec82b9<br/>- Create-react-app framework"]
    end
    
    Docker --> Docker_Services
    Railway --> Railway_Services
    Netlify --> Netlify_Assets
    Supabase --> Supabase_Services
    Windsurf --> Windsurf_Config
```

#### Deployment Details:
-   **Local Docker (`config/docker-compose.p1.yml`)**: Complete local development environment with all infrastructure services
-   **Railway**: Production backend services deployed as containerized microservices with complex deployment scripts
-   **Netlify**: Frontend UI deployed as static site with automatic CI/CD from main branch
-   **Windsurf (`config/windsurf_deployment.yaml`)**: Beta deployment platform for experimental features
-   **Supabase**: Managed PostgreSQL database with real-time capabilities and edge functions

## 3. Strengths and Advantages

- **Decoupled Architecture (in theory):** The MCP and event bus architecture allows for a highly decoupled system.
- **Real-time Capabilities:** Supabase Realtime provides a robust foundation for real-time communication.
- **Comprehensive Monitoring:** The Prometheus and Grafana stack provides deep visibility into the system's health.
- **Infrastructure as Code:** The `docker-compose.p1.yml` file provides a complete, reproducible local development environment.
- **Marketplace Foundation:** Database schema includes complete marketplace entities (contracts, bids, agent_registry).
- **Multi-tenant Architecture:** Strong RLS policies ensure data isolation between organizations.
- **Flexible Agent System:** Supports multiple specialized AI agents with different capabilities and pricing models.
- **Progressive Web App (PWA) Features:** Service worker implementation for offline functionality and push notifications.
- **Audio Library System:** Dedicated storage bucket for audio content management with multiple format support.
- **Frontend Diagnostic Tools:** Built-in health monitoring and debugging capabilities accessible via browser console.
- **Extensive Operational Tooling:** 120+ P0/P1 milestone scripts for deployment, monitoring, and recovery.

## 4. Limitations and Constraints

- **Architectural Schizophrenia:** The existence of two "master agents" and three message buses creates a confusing and inconsistent architecture.
- **Triple Message Bus Complexity:** Three separate event systems (Supabase Realtime, PostgreSQL NOTIFY/LISTEN, Redis Streams) create extreme operational complexity.
- **Incomplete Redis Migration:** The migration to Redis Streams is incomplete, leaving the legacy database-polling system as a major performance bottleneck.
- **Duplicated LLM Logic:** The duplicated LLM logic in `packages/agent/` is a significant source of technical debt.
- **Over-reliance on Database Logic:** The "database-as-an-API" pattern makes the system difficult to test and evolve.
- **Incomplete Marketplace Implementation:** While database tables exist for the marketplace, the full bidding and execution flow is not fully implemented.
- **Operational Fragility:** Extensive fix scripts in `scripts/` directory indicate history of manual interventions and system instability.
- **Skeleton Services:** The webhook-receiver service contains only TODO blocks, indicating incomplete implementation.
- **Frontend Business Logic:** Critical business logic implemented in browser creates security vulnerabilities and architectural confusion.
- **Testing Reality Gap:** Only ~45 test files exist versus 383 claimed, indicating significant quality assurance issues.
- **Missing Backend Implementations:** Many features (notifications, some PWA functions) exist only in frontend without backend support.

## 5. Evolution Opportunities

- **Unify the "Master Agent":** The two "master agent" services must be reconciled into a single, coherent service or set of services.
- **Complete the Redis Migration:** The migration to Redis Streams should be completed, and the legacy webhook system should be decommissioned.
- **Create a Unified LLM Service:** The duplicated LLM logic should be refactored into a single, unified LLM service.
- **Refactor Database Logic:** The business logic currently housed in PostgreSQL functions should be moved into the application layer.

## 6. References

- [packages/](packages/)
- [railway/](railway/)
- [scripts/](scripts/)
- [config/](config/)
- [deployment/](../../../../05-operations/deployment/)
- [supabase/migrations/](supabase/migrations/)
