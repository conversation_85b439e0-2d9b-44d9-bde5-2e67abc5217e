# SDLC Templates

> **Purpose**: Reusable templates for SDLC documentation  
> **Audience**: All team members creating documentation

## Overview
Collection of templates ensuring consistent, high-quality documentation across the VibeMatch project. Use these templates as starting points for new documents.

## Template Categories
### Architecture Templates
- Architecture Decision Record (ADR)
- Service Design Document
- API Specification
- Integration Design

### Requirements Templates
- User Story Template
- Requirements Specification
- Use Case Template
- Acceptance Criteria

### Testing Templates
- Test Case Template
- Test Plan Template
- Bug Report Template
- Performance Test Report

### Operations Templates
- Runbook Template
- Incident Report
- Post-Mortem Template
- Deployment Checklist

### Documentation Templates
- README Template
- Technical Guide
- User Guide
- API Documentation

## Using Templates
1. **Select** appropriate template
2. **Copy** to your target location
3. **Fill in** all sections
4. **Remove** any unused sections
5. **Review** for completeness

## Template Standards
### Required Sections
- Purpose statement
- Target audience
- Overview/summary
- Main content
- Related links

### Metadata Footer
```markdown
---
**Owner**: [Team Name]  
**Last Updated**: YYYY-MM-DD  
**Version**: X.Y  
**Status**: [Draft | Review | Approved]
```

## Contributing Templates
To add a new template:
1. Create in appropriate category
2. Include example content
3. Add clear instructions
4. Test with real use case
5. Submit PR for review

## Most Used Templates
1. [User Story Template](./user-story-template.md)
2. [API Spec Template](./api-spec-template.md)
3. [Test Case Template](../06-quality/testing/test-case-template.md)
4. [README Template](./readme-template.md)
5. [Runbook Template](./runbook-template.md)

---
**Section Owner**: Documentation Team  
**Last Updated**: 2025-06-26  
**Parent**: [SDLC Root](../)