#!/usr/bin/env python3
"""
Find and report broken links in markdown files
"""
import os
import re
from pathlib import Path
from collections import defaultdict

SDLC_ROOT = Path("/Users/<USER>/Documents/GitHub/vibe-match/sdlc")

def extract_links(file_path, content):
    """Extract all markdown links from content"""
    links = []
    # Match [text](link) pattern
    pattern = r'\[([^\]]+)\]\(([^)]+)\)'
    for match in re.finditer(pattern, content):
        link_text = match.group(1)
        link_url = match.group(2)
        links.append({
            'text': link_text,
            'url': link_url,
            'line': content[:match.start()].count('\n') + 1
        })
    return links

def resolve_link(source_file, link_url):
    """Resolve a link relative to source file"""
    # Skip external links
    if link_url.startswith(('http://', 'https://', 'mailto:', '#')):
        return None, "external"
    
    # Handle anchor-only links
    if link_url.startswith('#'):
        return None, "anchor"
    
    # Split link and anchor
    if '#' in link_url:
        link_path, anchor = link_url.split('#', 1)
    else:
        link_path = link_url
        anchor = None
    
    # Resolve path
    source_dir = Path(source_file).parent
    if link_path.startswith('/'):
        # Absolute path from SDLC root
        target_path = SDLC_ROOT / link_path[1:]
    else:
        # Relative path
        target_path = (source_dir / link_path).resolve()
    
    return target_path, anchor

def check_links():
    """Check all links in all markdown files"""
    broken_links = defaultdict(list)
    all_files = list(SDLC_ROOT.rglob('*.md'))
    
    print(f"Checking {len(all_files)} markdown files...")
    
    for file_path in all_files:
        # Skip archive and backup directories
        if 'archive' in file_path.parts or 'backup' in file_path.parts:
            continue
            
        try:
            content = file_path.read_text()
            links = extract_links(file_path, content)
            
            for link in links:
                target_path, anchor = resolve_link(file_path, link['url'])
                
                if target_path is None:
                    continue  # External or anchor-only link
                
                if not target_path.exists():
                    broken_links[str(file_path.relative_to(SDLC_ROOT))].append({
                        'line': link['line'],
                        'text': link['text'],
                        'url': link['url'],
                        'expected_path': str(target_path),
                        'exists': False
                    })
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
    
    return broken_links

def suggest_fix(broken_link, source_file):
    """Suggest a fix for a broken link"""
    url = broken_link['url']
    source_path = Path(source_file)
    
    # Common patterns to fix
    if 'api-examples' in url:
        suggestion = url.replace('api-examples', 'examples')
    elif 'event-catalog.md' in url and '/events/' in url:
        suggestion = url  # This should exist
    else:
        # Try to find similar file
        filename = Path(url).name
        possible_files = list(SDLC_ROOT.rglob(filename))
        if possible_files:
            # Find the closest match
            target = possible_files[0]
            # Calculate relative path
            try:
                suggestion = os.path.relpath(target, source_path.parent)
            except:
                suggestion = None
        else:
            suggestion = None
    
    return suggestion

def main():
    """Main function"""
    broken_links = check_links()
    
    if not broken_links:
        print("\n✅ No broken links found!")
        return
    
    print(f"\n❌ Found broken links in {len(broken_links)} files:\n")
    
    total_broken = 0
    fixes = []
    
    for file_path, links in sorted(broken_links.items()):
        print(f"\n📄 {file_path}")
        for link in links:
            total_broken += 1
            print(f"  Line {link['line']}: [{link['text']}]({link['url']})")
            
            suggestion = suggest_fix(link, SDLC_ROOT / file_path)
            if suggestion and suggestion != link['url']:
                print(f"    → Suggested fix: {suggestion}")
                fixes.append({
                    'file': file_path,
                    'line': link['line'],
                    'old': link['url'],
                    'new': suggestion
                })
    
    print(f"\n📊 Summary:")
    print(f"  - Total broken links: {total_broken}")
    print(f"  - Files affected: {len(broken_links)}")
    print(f"  - Suggested fixes: {len(fixes)}")
    
    # Save fixes to file
    if fixes:
        fixes_file = SDLC_ROOT / "suggested-fixes.txt"
        with open(fixes_file, 'w') as f:
            f.write("# Suggested Link Fixes\n\n")
            for fix in fixes:
                f.write(f"{fix['file']}:{fix['line']}\n")
                f.write(f"  Old: {fix['old']}\n")
                f.write(f"  New: {fix['new']}\n\n")
        print(f"\n💡 Suggested fixes saved to: {fixes_file}")

if __name__ == "__main__":
    main()