#!/usr/bin/env python3
"""
Fix broken links in markdown files based on analysis
"""
import os
import re
from pathlib import Path
from collections import defaultdict
import shutil

SDLC_ROOT = Path("/Users/<USER>/Documents/GitHub/vibe-match/sdlc")

# Manual fixes for known patterns
LINK_FIXES = {
    # Fix absolute paths to relative
    '/sdlc/06-quality/testing/test-specifications/': '../../06-quality/testing/test-specifications',
    '/sdlc/07-governance/audit-trail/requirements/': '../../07-governance/audit-trail/requirements/',
    '/sdlc/01-architecture/decisions/': '../../01-architecture/decisions',
    
    # Fix paths with extra directories
    './security/': '',  # Remove ./security/ prefix in security files
    
    # Fix moved directories
    'deployment/': '../../../../05-operations/deployment',
    './api-documentation.md': '../../00-inception/background/previous-system/02-apis-and-data/api-documentation.md',
    './test-case-template.md': '../06-quality/testing/test-case-template.md',
}

def backup_file(file_path):
    """Create backup of file before modifying"""
    backup_path = file_path.with_suffix(file_path.suffix + '.backup')
    shutil.copy2(file_path, backup_path)
    return backup_path

def fix_links_in_file(file_path):
    """Fix broken links in a single file"""
    try:
        content = file_path.read_text()
        original_content = content
        
        # Apply known fixes
        for old_pattern, new_pattern in LINK_FIXES.items():
            if old_pattern in content:
                content = content.replace(old_pattern, new_pattern)
                print(f"  Fixed: {old_pattern} -> {new_pattern}")
        
        # Fix specific patterns based on file location
        if 'security' in str(file_path) and './security/' in content:
            # In security files, remove ./security/ prefix
            content = re.sub(r'\./security/([^)]+\.md)', r'\1', content)
            print(f"  Fixed: Removed ./security/ prefixes")
        
        if '00-inception/background/economic-theory' in str(file_path):
            # Fix relative paths that go up too many directories
            content = re.sub(r'\.\./(\.\./)+(0[1-5]-[^/]+/)', r'../\3', content)
            print(f"  Fixed: Corrected relative paths in economic theory docs")
        
        # Only write if changes were made
        if content != original_content:
            backup_file(file_path)
            file_path.write_text(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"  Error processing {file_path}: {e}")
        return False

def fix_specific_files():
    """Fix specific files with known issues"""
    fixes = {
        # Fix the SUPPORTING_DOCUMENTS.md file
        SDLC_ROOT / "00-inception/background/economic-theory/10-LEADERSHIP-PRESENTATION/SUPPORTING_DOCUMENTS.md": [
            ("../../05-GOVERNANCE-SYSTEM/", "../05-GOVERNANCE-SYSTEM/"),
            ("../../01-ECONOMIC-FOUNDATION/", "../01-ECONOMIC-FOUNDATION/"),
            ("../../02-CURRENCY-SYSTEM/", "../02-CURRENCY-SYSTEM/"),
            ("../../03-MARKET-INFRASTRUCTURE/", "../03-MARKET-INFRASTRUCTURE/"),
            ("../../04-FINANCIAL-ECOSYSTEM/", "../04-FINANCIAL-ECOSYSTEM/"),
        ],
        
        # Fix security README
        SDLC_ROOT / "00-inception/background/previous-system/05-security/SECURITY.md": [
            ("./security/current-vulnerabilities.md", "current-vulnerabilities.md"),
            ("./security/remediation-plan.md", "remediation-plan.md"),
            ("./security/greenfield-security-requirements.md", "greenfield-security-requirements.md"),
        ],
        
        # Fix requirements README
        SDLC_ROOT / "03-specifications/requirements/README.md": [
            ("/sdlc/06-quality/testing/test-specifications/", "../../06-quality/testing/test-specifications"),
            ("/sdlc/07-governance/audit-trail/requirements/", "../../07-governance/audit-trail/requirements/"),
            ("/sdlc/01-architecture/decisions/", "../../01-architecture/decisions"),
        ],
    }
    
    for file_path, replacements in fixes.items():
        if file_path.exists():
            print(f"\nFixing {file_path.relative_to(SDLC_ROOT)}:")
            content = file_path.read_text()
            original_content = content
            
            for old, new in replacements:
                if old in content:
                    content = content.replace(old, new)
                    print(f"  Replaced: {old} -> {new}")
            
            if content != original_content:
                backup_file(file_path)
                file_path.write_text(content)
                print(f"  ✅ File updated")

def check_and_create_missing_files():
    """Check for commonly missing files and create them if needed"""
    missing_files = [
        # Common missing README files
        (SDLC_ROOT / "01-architecture/patterns/README.md", "# Architecture Patterns\n\nCollection of architectural patterns used in VibeMatch.\n"),
        (SDLC_ROOT / "01-architecture/diagrams/README.md", "# Architecture Diagrams\n\nVisual representations of VibeMatch architecture.\n"),
        (SDLC_ROOT / "02-standards/api/error-handling.md", "# API Error Handling Standards\n\nStandardized error handling for all VibeMatch APIs.\n"),
        (SDLC_ROOT / "02-standards/api/api-versioning.md", "# API Versioning Standards\n\nVersioning strategy for VibeMatch APIs.\n"),
        (SDLC_ROOT / "02-standards/api/pagination-standards.md", "# API Pagination Standards\n\nPagination patterns for list endpoints.\n"),
    ]
    
    for file_path, default_content in missing_files:
        if not file_path.exists():
            print(f"\nCreating missing file: {file_path.relative_to(SDLC_ROOT)}")
            file_path.parent.mkdir(parents=True, exist_ok=True)
            file_path.write_text(default_content)

def main():
    """Main function"""
    print("🔧 Fixing broken links in SDLC documentation...")
    print("=" * 60)
    
    # First, fix specific known issues
    fix_specific_files()
    
    # Create commonly missing files
    check_and_create_missing_files()
    
    # Fix links in high-priority files
    priority_files = [
        "README.md",
        "QUICK-LINKS.md",
        "*/README.md",
        "01-architecture/system-design.md",
        "03-specifications/api-catalog.md",
        "04-implementation/local-setup.md",
    ]
    
    fixed_count = 0
    
    for pattern in priority_files:
        for file_path in SDLC_ROOT.rglob(pattern):
            if 'archive' in file_path.parts or 'backup' in file_path.parts:
                continue
            
            print(f"\nChecking {file_path.relative_to(SDLC_ROOT)}...")
            if fix_links_in_file(file_path):
                fixed_count += 1
    
    print(f"\n✅ Fixed links in {fixed_count} files")
    print("\n💡 Backup files created with .backup extension")
    print("\n📋 Next steps:")
    print("  1. Review the changes")
    print("  2. Run the link checker again to verify fixes")
    print("  3. Remove .backup files when satisfied")

if __name__ == "__main__":
    main()