# Database Specifications

> **Purpose**: Database schema, indexes, and data models  
> **Audience**: Backend developers and database administrators

## Overview
Database design documentation for VibeMatch's Firestore NoSQL database, including collection structures, indexing strategies, and data modeling patterns.

## Collection Structure
### Core Collections
- **users** - User profiles and preferences
- **agents** - Agent profiles and capabilities
- **tasks** - Task definitions and status
- **credits** - Transaction history
- **orchestrations** - Multi-agent workflows

### Supporting Collections
- **sessions** - Active user sessions
- **events** - Event sourcing store
- **audit_logs** - Compliance logging
- **analytics** - Aggregated metrics

## Data Modeling Patterns
### Document Structure
```typescript
// Example: User document
{
  id: "user_123",
  email: "<EMAIL>",
  profile: {
    name: "<PERSON>",
    avatar: "https://...",
    timezone: "UTC"
  },
  roles: ["user"],
  credits: {
    balance: 1000,
    reserved: 50
  },
  created: Timestamp,
  updated: Timestamp
}
```

### Subcollections
- User → Sessions
- Agent → Ratings
- Task → Comments
- Orchestration → Steps

## Indexing Strategy
### Composite Indexes
- (status, created) for task queries
- (agentId, rating) for agent ranking
- (userId, created) for user history

### Single Field Indexes
- email (users)
- status (tasks)
- type (agents)

## Data Consistency
### Patterns
- Optimistic locking with version fields
- Transactions for credit operations
- Batch writes for related updates
- Eventual consistency for analytics

### Security Rules
- Row-level security
- Field-level access control
- Rate limiting rules
- Validation rules

---
**Section Owner**: Database Team  
**Last Updated**: 2025-06-26  
**Parent**: [Specifications](../)