# Event Versioning Strategy

> **Purpose**: Define versioning approach for event schemas and backward compatibility  
> **Audience**: All developers, architects, platform engineers  
> **Version**: 1.0.0  
> **Last Updated**: 2025-06-26

## Overview

Event versioning is critical for maintaining backward compatibility while evolving VibeMatch's event-driven architecture. This document establishes the official versioning strategy, migration patterns, and compatibility guidelines.

## Versioning Principles

### 1. Semantic Versioning
We use semantic versioning (SemVer) for event schemas:

```
MAJOR.MINOR.PATCH
```

- **MAJOR**: Breaking changes that require consumer updates
- **MINOR**: Backward-compatible additions (new optional fields)
- **PATCH**: Bug fixes and clarifications (no schema changes)

### 2. Backward Compatibility First
- New versions must be backward compatible when possible
- Breaking changes require careful migration planning
- Consumers should handle unknown fields gracefully

### 3. Schema Evolution Over Time
- Start simple, evolve gradually
- Add optional fields before making them required
- Deprecate fields before removing them

## Version Identification

### Event Schema Version
Every event includes a version field in its metadata:

```json
{
  "eventId": "evt_1234567890abcdef",
  "type": "user.created",
  "version": "1.2.0",
  "timestamp": "2025-06-26T10:30:00Z",
  "data": {
    // Event payload
  }
}
```

### Schema File Versioning
Schema files include version in their `$id`:

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "$id": "https://vibe-match.com/schemas/events/user/v1.2.0",
  "title": "User Events Schema v1.2.0"
}
```

## Change Types and Versioning

### PATCH Changes (1.0.0 → 1.0.1)
**Safe changes that don't affect schema validation:**
- Documentation updates
- Example corrections
- Description clarifications
- Comment additions

```json
// v1.0.0
{
  "userId": {
    "type": "string",
    "description": "User ID"
  }
}

// v1.0.1
{
  "userId": {
    "type": "string",
    "description": "Unique user identifier in format usr_XXXXXXXXXXXXXXXXXXXX"
  }
}
```

### MINOR Changes (1.0.0 → 1.1.0)
**Backward-compatible additions:**
- Adding optional fields
- Adding new enum values
- Relaxing validation constraints
- Adding new event types to existing schema

```json
// v1.0.0
{
  "data": {
    "type": "object",
    "required": ["userId", "email"],
    "properties": {
      "userId": {"type": "string"},
      "email": {"type": "string"}
    }
  }
}

// v1.1.0 - Added optional displayName
{
  "data": {
    "type": "object",
    "required": ["userId", "email"],
    "properties": {
      "userId": {"type": "string"},
      "email": {"type": "string"},
      "displayName": {"type": "string"}  // New optional field
    }
  }
}
```

### MAJOR Changes (1.0.0 → 2.0.0)
**Breaking changes that require consumer updates:**
- Removing fields
- Making optional fields required
- Changing field types
- Renaming fields
- Removing enum values
- Changing validation constraints (more restrictive)

```json
// v1.0.0
{
  "data": {
    "type": "object",
    "required": ["userId"],
    "properties": {
      "userId": {"type": "string"},
      "userType": {"enum": ["customer", "agent", "admin"]}
    }
  }
}

// v2.0.0 - Breaking changes
{
  "data": {
    "type": "object",
    "required": ["userId", "email"],  // email now required
    "properties": {
      "userId": {"type": "string"},
      "email": {"type": "string"},    // New required field
      "role": {"enum": ["user", "agent_owner", "admin"]}  // Renamed and changed enum
    }
  }
}
```

## Migration Strategies

### 1. Dual Publishing (Recommended)
During major version transitions, publish both versions:

```typescript
// Publisher supports both versions
async function publishUserCreated(userData: UserData) {
  // Publish v1 for backward compatibility
  await eventBus.publish('user.created', {
    version: '1.0.0',
    data: transformToV1(userData)
  });
  
  // Publish v2 for new consumers
  await eventBus.publish('user.created', {
    version: '2.0.0',
    data: transformToV2(userData)
  });
}
```

### 2. Schema Translation
Consumers can translate between versions:

```typescript
class EventConsumer {
  async handleUserCreated(event: UserCreatedEvent) {
    // Normalize to latest version
    const normalizedEvent = this.translateToLatest(event);
    await this.processUserCreated(normalizedEvent);
  }
  
  private translateToLatest(event: UserCreatedEvent): UserCreatedEventV2 {
    switch (event.version) {
      case '1.0.0':
        return this.translateV1ToV2(event);
      case '2.0.0':
        return event;
      default:
        throw new Error(`Unsupported version: ${event.version}`);
    }
  }
}
```

### 3. Gradual Migration
For complex migrations, use a phased approach:

```mermaid
gantt
    title Event Schema Migration Timeline
    dateFormat  YYYY-MM-DD
    section Phase 1
    Dual Publishing     :2025-07-01, 30d
    Consumer Updates    :2025-07-01, 45d
    section Phase 2
    Monitor Usage       :2025-08-01, 30d
    Deprecation Warnings:2025-08-15, 30d
    section Phase 3
    Remove Old Version  :2025-09-15, 7d
    Cleanup             :2025-09-22, 7d
```

## Compatibility Guidelines

### For Publishers
1. **Always include version** in event metadata
2. **Support multiple versions** during transitions
3. **Validate against schema** before publishing
4. **Monitor consumer adoption** of new versions

```typescript
interface EventPublisher {
  publish<T>(eventType: string, data: T, version: string): Promise<void>;
  publishMultiVersion<T>(eventType: string, data: T, versions: string[]): Promise<void>;
}
```

### For Consumers
1. **Handle unknown fields gracefully**
2. **Validate event version** before processing
3. **Implement version translation** when needed
4. **Fail gracefully** on unsupported versions

```typescript
interface EventConsumer {
  getSupportedVersions(): string[];
  canHandle(event: Event): boolean;
  handle(event: Event): Promise<void>;
}
```

## Schema Registry

### Centralized Schema Management
All event schemas are stored in a centralized registry:

```
/schemas/
├── user-events/
│   ├── v1.0.0.json
│   ├── v1.1.0.json
│   └── v2.0.0.json
├── task-events/
│   ├── v1.0.0.json
│   └── v1.1.0.json
└── billing-events/
    ├── v1.0.0.json
    └── v1.2.0.json
```

### Schema Validation Service
```typescript
class SchemaRegistry {
  async validateEvent(eventType: string, version: string, data: any): Promise<boolean> {
    const schema = await this.getSchema(eventType, version);
    return this.validator.validate(schema, data);
  }
  
  async getLatestVersion(eventType: string): Promise<string> {
    const versions = await this.getVersions(eventType);
    return versions.sort(semver.compare).pop();
  }
  
  async isVersionSupported(eventType: string, version: string): Promise<boolean> {
    const supportedVersions = await this.getSupportedVersions(eventType);
    return supportedVersions.includes(version);
  }
}
```

## Deprecation Process

### 1. Deprecation Notice
Mark fields/versions as deprecated in schema:

```json
{
  "properties": {
    "oldField": {
      "type": "string",
      "deprecated": true,
      "description": "DEPRECATED: Use newField instead. Will be removed in v3.0.0"
    },
    "newField": {
      "type": "string",
      "description": "Replacement for oldField"
    }
  }
}
```

### 2. Migration Timeline
Standard deprecation timeline:
- **Month 1**: Announce deprecation, publish new version
- **Month 2-3**: Dual support, migration warnings
- **Month 4**: Remove deprecated version

### 3. Communication
- Update documentation immediately
- Send notifications to service owners
- Monitor usage metrics
- Provide migration guides

## Version Compatibility Matrix

### Supported Versions
| Event Type | Current | Supported | Deprecated | EOL |
|------------|---------|-----------|------------|-----|
| user.created | 2.1.0 | 2.0.0, 2.1.0 | 1.x.x | 2025-12-31 |
| task.created | 1.2.0 | 1.1.0, 1.2.0 | 1.0.0 | 2025-09-30 |
| billing.payment_processed | 1.0.0 | 1.0.0 | - | - |

### Consumer Compatibility
| Service | user.created | task.created | billing.* |
|---------|--------------|--------------|-----------|
| Analytics | 2.0.0+ | 1.1.0+ | 1.0.0+ |
| Notification | 1.0.0+ | 1.0.0+ | 1.0.0+ |
| Billing | 2.1.0 | 1.2.0 | 1.0.0 |

## Testing Strategy

### Schema Validation Tests
```typescript
describe('Event Schema Validation', () => {
  test('v1.0.0 events validate correctly', () => {
    const event = createUserCreatedEventV1();
    expect(validateSchema('user.created', '1.0.0', event)).toBe(true);
  });
  
  test('v2.0.0 events validate correctly', () => {
    const event = createUserCreatedEventV2();
    expect(validateSchema('user.created', '2.0.0', event)).toBe(true);
  });
  
  test('v1.0.0 events fail v2.0.0 validation', () => {
    const event = createUserCreatedEventV1();
    expect(validateSchema('user.created', '2.0.0', event)).toBe(false);
  });
});
```

### Backward Compatibility Tests
```typescript
describe('Backward Compatibility', () => {
  test('consumers handle unknown fields', () => {
    const eventWithExtraFields = {
      ...createUserCreatedEventV1(),
      data: {
        ...createUserCreatedEventV1().data,
        futureField: 'unknown'
      }
    };
    
    expect(() => processUserCreated(eventWithExtraFields)).not.toThrow();
  });
});
```

## Monitoring and Metrics

### Version Usage Tracking
```typescript
interface VersionMetrics {
  eventType: string;
  version: string;
  publishCount: number;
  consumerCount: number;
  lastSeen: Date;
}
```

### Alerts and Notifications
- **Deprecated version usage**: Alert when deprecated versions are still being used
- **Schema validation failures**: Monitor validation error rates
- **Version adoption**: Track migration progress

## Best Practices

### For Schema Design
1. **Start with required fields only**
2. **Add optional fields in minor versions**
3. **Use clear, descriptive field names**
4. **Include comprehensive documentation**
5. **Plan for future extensibility**

### For Version Management
1. **Version everything from day one**
2. **Maintain compatibility matrices**
3. **Automate schema validation**
4. **Monitor version usage**
5. **Communicate changes early**

### For Migration Planning
1. **Plan breaking changes carefully**
2. **Provide migration tools**
3. **Support multiple versions temporarily**
4. **Monitor migration progress**
5. **Clean up deprecated versions**

## Tools and Automation

### Schema Generation
```bash
# Generate TypeScript types from JSON Schema
npm run generate-types

# Validate all schemas
npm run validate-schemas

# Check backward compatibility
npm run check-compatibility
```

### CI/CD Integration
```yaml
# .github/workflows/schema-validation.yml
name: Schema Validation
on: [push, pull_request]
jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Validate Schemas
        run: npm run validate-schemas
      - name: Check Compatibility
        run: npm run check-compatibility
```

---
**Document Owner**: Architecture Team  
**Review Cycle**: Quarterly  
**Next Review**: 2025-09-26
