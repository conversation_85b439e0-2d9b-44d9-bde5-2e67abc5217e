{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://vibe-match.com/schemas/events/user/v1.0.0", "title": "User Events Schema", "description": "Event schemas for user-related operations in VibeMatch", "definitions": {"userId": {"type": "string", "pattern": "^usr_[a-zA-Z0-9]{20}$", "description": "Unique user identifier"}, "email": {"type": "string", "format": "email", "description": "User email address"}, "timestamp": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp"}, "metadata": {"type": "object", "properties": {"ip": {"type": "string"}, "userAgent": {"type": "string"}, "sessionId": {"type": "string"}}}}, "oneOf": [{"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "user.created"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["userId", "email"], "properties": {"userId": {"$ref": "#/definitions/userId"}, "email": {"$ref": "#/definitions/email"}, "displayName": {"type": "string"}, "role": {"enum": ["user", "agent", "admin"]}, "metadata": {"$ref": "#/definitions/metadata"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "user.updated"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["userId", "changes"], "properties": {"userId": {"$ref": "#/definitions/userId"}, "changes": {"type": "object", "properties": {"displayName": {"type": "string"}, "email": {"$ref": "#/definitions/email"}, "preferences": {"type": "object"}}}, "metadata": {"$ref": "#/definitions/metadata"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "user.deleted"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["userId", "reason"], "properties": {"userId": {"$ref": "#/definitions/userId"}, "reason": {"enum": ["user_request", "admin_action", "policy_violation"]}, "deletedBy": {"$ref": "#/definitions/userId"}, "metadata": {"$ref": "#/definitions/metadata"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "user.login"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["userId", "method"], "properties": {"userId": {"$ref": "#/definitions/userId"}, "method": {"enum": ["password", "google", "mfa"]}, "success": {"type": "boolean"}, "metadata": {"$ref": "#/definitions/metadata"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "user.logout"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["userId"], "properties": {"userId": {"$ref": "#/definitions/userId"}, "reason": {"enum": ["user_action", "session_expired", "forced"]}, "metadata": {"$ref": "#/definitions/metadata"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "user.suspended"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["userId", "reason", "suspendedBy"], "properties": {"userId": {"$ref": "#/definitions/userId"}, "reason": {"type": "string"}, "suspendedBy": {"$ref": "#/definitions/userId"}, "suspendedUntil": {"$ref": "#/definitions/timestamp"}, "metadata": {"$ref": "#/definitions/metadata"}}}}}]}