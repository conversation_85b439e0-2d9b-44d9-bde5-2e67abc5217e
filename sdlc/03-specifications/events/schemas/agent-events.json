{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://vibe-match.com/schemas/events/agent/v1.0.0", "title": "Agent <PERSON> Schema", "description": "Event schemas for agent-related operations in VibeMatch", "definitions": {"agentId": {"type": "string", "pattern": "^agt_[a-zA-Z0-9]{20}$", "description": "Unique agent identifier"}, "userId": {"type": "string", "pattern": "^usr_[a-zA-Z0-9]{20}$", "description": "User identifier (agent owner)"}, "taskId": {"type": "string", "pattern": "^tsk_[a-zA-Z0-9]{20}$", "description": "Task identifier"}, "timestamp": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp"}, "capabilities": {"type": "array", "items": {"type": "string", "enum": ["content_creation", "code_assistance", "data_analysis", "creative_design", "research"]}}, "qualityScore": {"type": "number", "minimum": 0, "maximum": 1, "description": "Agent quality score (0-1)"}}, "oneOf": [{"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "agent.created"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["agentId", "userId", "name", "capabilities"], "properties": {"agentId": {"$ref": "#/definitions/agentId"}, "userId": {"$ref": "#/definitions/userId"}, "name": {"type": "string"}, "description": {"type": "string"}, "capabilities": {"$ref": "#/definitions/capabilities"}, "hourlyRate": {"type": "number", "minimum": 0}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "agent.updated"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["agentId", "changes"], "properties": {"agentId": {"$ref": "#/definitions/agentId"}, "changes": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "capabilities": {"$ref": "#/definitions/capabilities"}, "hourlyRate": {"type": "number"}, "availability": {"type": "boolean"}}}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "agent.matched"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["agentId", "taskId", "matchScore"], "properties": {"agentId": {"$ref": "#/definitions/agentId"}, "taskId": {"$ref": "#/definitions/taskId"}, "matchScore": {"type": "number", "minimum": 0, "maximum": 1}, "matchReason": {"type": "string"}, "isTeamMember": {"type": "boolean", "default": false}, "teamRole": {"type": "string"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "agent.task_started"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["agentId", "taskId"], "properties": {"agentId": {"$ref": "#/definitions/agentId"}, "taskId": {"$ref": "#/definitions/taskId"}, "estimatedDuration": {"type": "integer", "description": "Estimated duration in minutes"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "agent.task_completed"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["agentId", "taskId", "duration", "result"], "properties": {"agentId": {"$ref": "#/definitions/agentId"}, "taskId": {"$ref": "#/definitions/taskId"}, "duration": {"type": "integer", "description": "Actual duration in minutes"}, "result": {"type": "object", "properties": {"success": {"type": "boolean"}, "output": {"type": "string"}, "artifacts": {"type": "array", "items": {"type": "string"}}}}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "agent.performance_updated"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["agentId", "metrics"], "properties": {"agentId": {"$ref": "#/definitions/agentId"}, "metrics": {"type": "object", "properties": {"qualityScore": {"$ref": "#/definitions/qualityScore"}, "completionRate": {"type": "number", "minimum": 0, "maximum": 1}, "avgResponseTime": {"type": "integer", "description": "Average response time in seconds"}, "totalTasks": {"type": "integer"}, "successfulTasks": {"type": "integer"}}}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "agent.verified"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["agentId", "verifiedBy", "verificationLevel"], "properties": {"agentId": {"$ref": "#/definitions/agentId"}, "verifiedBy": {"$ref": "#/definitions/userId"}, "verificationLevel": {"enum": ["basic", "professional", "expert"]}, "certifications": {"type": "array", "items": {"type": "string"}}}}}}]}