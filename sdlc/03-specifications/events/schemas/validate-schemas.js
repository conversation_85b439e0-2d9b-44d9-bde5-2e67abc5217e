#!/usr/bin/env node

/**
 * Schema Validation Script for VibeMatch Event Schemas
 * 
 * This script validates:
 * 1. JSON Schema syntax correctness
 * 2. JSON Schema Draft-07 compliance
 * 3. Test examples against schemas
 * 4. Schema consistency across files
 */

const fs = require('fs');
const path = require('path');
const Ajv = require('ajv');
const addFormats = require('ajv-formats');

// Initialize AJV with Draft-07 support
const ajv = new Ajv({ 
  strict: false,
  allErrors: true,
  verbose: true
});
addFormats(ajv);

// Schema files to validate
const SCHEMA_FILES = [
  'user-events.json',
  'agent-events.json',
  'task-events.json',
  'billing-events.json',
  'matching-events.json',
  'credit-events.json'
];

// Test examples file
const TEST_EXAMPLES_FILE = 'test-examples.json';

class SchemaValidator {
  constructor() {
    this.schemas = new Map();
    this.testExamples = null;
    this.errors = [];
    this.warnings = [];
  }

  async validate() {
    console.log('🔍 Starting VibeMatch Event Schema Validation...\n');

    try {
      // Step 1: Load and validate schema files
      await this.loadSchemas();
      
      // Step 2: Validate schema syntax
      await this.validateSchemaSyntax();
      
      // Step 3: Load test examples
      await this.loadTestExamples();
      
      // Step 4: Validate examples against schemas
      await this.validateExamples();
      
      // Step 5: Check schema consistency
      await this.checkSchemaConsistency();
      
      // Step 6: Report results
      this.reportResults();
      
    } catch (error) {
      console.error('❌ Validation failed:', error.message);
      process.exit(1);
    }
  }

  async loadSchemas() {
    console.log('📁 Loading schema files...');
    
    for (const filename of SCHEMA_FILES) {
      const filepath = path.join(__dirname, filename);
      
      if (!fs.existsSync(filepath)) {
        this.errors.push(`Schema file not found: ${filename}`);
        continue;
      }
      
      try {
        const content = fs.readFileSync(filepath, 'utf8');
        const schema = JSON.parse(content);
        this.schemas.set(filename, schema);
        console.log(`  ✅ Loaded ${filename}`);
      } catch (error) {
        this.errors.push(`Failed to parse ${filename}: ${error.message}`);
      }
    }
    
    console.log(`📁 Loaded ${this.schemas.size} schema files\n`);
  }

  async validateSchemaSyntax() {
    console.log('🔧 Validating schema syntax...');
    
    for (const [filename, schema] of this.schemas) {
      try {
        // Check required JSON Schema properties
        this.validateSchemaStructure(filename, schema);
        
        // Validate with AJV
        const isValid = ajv.validateSchema(schema);
        if (!isValid) {
          this.errors.push(`Schema validation failed for ${filename}: ${ajv.errorsText(ajv.errors)}`);
        } else {
          console.log(`  ✅ ${filename} syntax valid`);
        }
        
      } catch (error) {
        this.errors.push(`Schema validation error for ${filename}: ${error.message}`);
      }
    }
    
    console.log('🔧 Schema syntax validation complete\n');
  }

  validateSchemaStructure(filename, schema) {
    // Check required top-level properties
    const requiredProps = ['$schema', '$id', 'title', 'description'];
    for (const prop of requiredProps) {
      if (!schema[prop]) {
        this.warnings.push(`${filename}: Missing recommended property '${prop}'`);
      }
    }

    // Check $schema version
    if (schema.$schema && !schema.$schema.includes('draft-07')) {
      this.warnings.push(`${filename}: Not using JSON Schema Draft-07`);
    }

    // Check $id format
    if (schema.$id && !schema.$id.startsWith('https://vibe-match.com/schemas/')) {
      this.warnings.push(`${filename}: $id should use vibe-match.com domain`);
    }

    // Validate event type structure for event schemas
    if (schema.oneOf) {
      for (const eventSchema of schema.oneOf) {
        if (eventSchema.properties?.type?.const) {
          const eventType = eventSchema.properties.type.const;
          if (!this.isValidEventName(eventType)) {
            this.errors.push(`${filename}: Invalid event name '${eventType}'`);
          }
        }
      }
    }
  }

  isValidEventName(eventName) {
    // Check event naming convention: domain.action
    const pattern = /^[a-z]+\.[a-z_]+$/;
    return pattern.test(eventName);
  }

  async loadTestExamples() {
    console.log('📋 Loading test examples...');
    
    const filepath = path.join(__dirname, TEST_EXAMPLES_FILE);
    
    if (!fs.existsSync(filepath)) {
      this.warnings.push(`Test examples file not found: ${TEST_EXAMPLES_FILE}`);
      return;
    }
    
    try {
      const content = fs.readFileSync(filepath, 'utf8');
      this.testExamples = JSON.parse(content);
      console.log('  ✅ Test examples loaded\n');
    } catch (error) {
      this.errors.push(`Failed to load test examples: ${error.message}`);
    }
  }

  async validateExamples() {
    if (!this.testExamples) {
      console.log('⚠️  Skipping example validation (no test examples)\n');
      return;
    }

    console.log('🧪 Validating examples against schemas...');

    // Validate matching events examples
    await this.validateEventExamples('matching-events.json', this.testExamples.examples['matching-events']);
    
    // Validate credit events examples
    await this.validateEventExamples('credit-events.json', this.testExamples.examples['credit-events']);

    console.log('🧪 Example validation complete\n');
  }

  async validateEventExamples(schemaFilename, examples) {
    const schema = this.schemas.get(schemaFilename);
    if (!schema) {
      this.errors.push(`Schema not found for examples: ${schemaFilename}`);
      return;
    }

    const validate = ajv.compile(schema);

    for (const [eventType, example] of Object.entries(examples)) {
      try {
        const isValid = validate(example);
        if (isValid) {
          console.log(`  ✅ ${eventType} example valid`);
        } else {
          this.errors.push(`Example validation failed for ${eventType}: ${ajv.errorsText(validate.errors)}`);
        }
      } catch (error) {
        this.errors.push(`Example validation error for ${eventType}: ${error.message}`);
      }
    }
  }

  async checkSchemaConsistency() {
    console.log('🔄 Checking schema consistency...');

    // Check consistent field definitions across schemas
    this.checkConsistentFieldDefinitions();
    
    // Check consistent event structure
    this.checkConsistentEventStructure();
    
    // Check consistent naming patterns
    this.checkConsistentNaming();

    console.log('🔄 Schema consistency check complete\n');
  }

  checkConsistentFieldDefinitions() {
    const commonFields = new Map();
    
    // Collect common field definitions
    for (const [filename, schema] of this.schemas) {
      if (schema.definitions) {
        for (const [fieldName, definition] of Object.entries(schema.definitions)) {
          if (!commonFields.has(fieldName)) {
            commonFields.set(fieldName, []);
          }
          commonFields.get(fieldName).push({ filename, definition });
        }
      }
    }

    // Check for inconsistencies
    for (const [fieldName, occurrences] of commonFields) {
      if (occurrences.length > 1) {
        const firstDef = JSON.stringify(occurrences[0].definition);
        for (let i = 1; i < occurrences.length; i++) {
          const currentDef = JSON.stringify(occurrences[i].definition);
          if (firstDef !== currentDef) {
            this.warnings.push(`Inconsistent definition for '${fieldName}' between ${occurrences[0].filename} and ${occurrences[i].filename}`);
          }
        }
      }
    }
  }

  checkConsistentEventStructure() {
    // Check that all events have consistent base structure
    const requiredEventFields = ['eventId', 'type', 'timestamp', 'version', 'source', 'data'];
    
    for (const [filename, schema] of this.schemas) {
      if (schema.oneOf) {
        for (const eventSchema of schema.oneOf) {
          if (eventSchema.properties) {
            for (const field of requiredEventFields) {
              if (!eventSchema.properties[field] && !eventSchema.required?.includes(field)) {
                this.warnings.push(`${filename}: Event missing standard field '${field}'`);
              }
            }
          }
        }
      }
    }
  }

  checkConsistentNaming() {
    // Check that event names follow consistent patterns
    const eventNames = [];
    
    for (const [filename, schema] of this.schemas) {
      if (schema.oneOf) {
        for (const eventSchema of schema.oneOf) {
          if (eventSchema.properties?.type?.const) {
            eventNames.push({
              filename,
              eventName: eventSchema.properties.type.const
            });
          }
        }
      }
    }

    // Check domain consistency
    const domains = new Set();
    for (const { eventName } of eventNames) {
      const domain = eventName.split('.')[0];
      domains.add(domain);
    }

    console.log(`  📊 Found ${eventNames.length} events across ${domains.size} domains`);
  }

  reportResults() {
    console.log('📊 Validation Results');
    console.log('='.repeat(50));
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('🎉 All schemas are valid! No errors or warnings found.');
    } else {
      if (this.errors.length > 0) {
        console.log(`❌ Errors (${this.errors.length}):`);
        this.errors.forEach((error, index) => {
          console.log(`  ${index + 1}. ${error}`);
        });
        console.log();
      }

      if (this.warnings.length > 0) {
        console.log(`⚠️  Warnings (${this.warnings.length}):`);
        this.warnings.forEach((warning, index) => {
          console.log(`  ${index + 1}. ${warning}`);
        });
        console.log();
      }
    }

    // Summary
    console.log('📈 Summary:');
    console.log(`  Schemas validated: ${this.schemas.size}`);
    console.log(`  Errors: ${this.errors.length}`);
    console.log(`  Warnings: ${this.warnings.length}`);
    console.log(`  Status: ${this.errors.length === 0 ? '✅ PASS' : '❌ FAIL'}`);

    // Exit with appropriate code
    if (this.errors.length > 0) {
      process.exit(1);
    }
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new SchemaValidator();
  validator.validate().catch(error => {
    console.error('Validation script failed:', error);
    process.exit(1);
  });
}

module.exports = SchemaValidator;
