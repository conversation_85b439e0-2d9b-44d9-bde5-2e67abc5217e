#!/usr/bin/env node

/**
 * Schema Compatibility Checker for VibeMatch Event Schemas
 * 
 * This script checks backward compatibility between schema versions
 * and identifies breaking changes that would require major version bumps.
 */

const fs = require('fs');
const path = require('path');

class CompatibilityChecker {
  constructor() {
    this.breakingChanges = [];
    this.compatibleChanges = [];
  }

  checkCompatibility(oldSchemaPath, newSchemaPath) {
    console.log('🔄 Checking schema compatibility...\n');

    try {
      const oldSchema = this.loadSchema(oldSchemaPath);
      const newSchema = this.loadSchema(newSchemaPath);

      this.compareSchemas(oldSchema, newSchema);
      this.reportResults();

    } catch (error) {
      console.error('❌ Compatibility check failed:', error.message);
      process.exit(1);
    }
  }

  loadSchema(schemaPath) {
    if (!fs.existsSync(schemaPath)) {
      throw new Error(`Schema file not found: ${schemaPath}`);
    }

    const content = fs.readFileSync(schemaPath, 'utf8');
    return JSON.parse(content);
  }

  compareSchemas(oldSchema, newSchema) {
    // Check for removed required fields (breaking)
    this.checkRemovedRequiredFields(oldSchema, newSchema);
    
    // Check for changed field types (breaking)
    this.checkChangedFieldTypes(oldSchema, newSchema);
    
    // Check for removed enum values (breaking)
    this.checkRemovedEnumValues(oldSchema, newSchema);
    
    // Check for added required fields (breaking)
    this.checkAddedRequiredFields(oldSchema, newSchema);
    
    // Check for added optional fields (compatible)
    this.checkAddedOptionalFields(oldSchema, newSchema);
    
    // Check for added enum values (compatible)
    this.checkAddedEnumValues(oldSchema, newSchema);
  }

  checkRemovedRequiredFields(oldSchema, newSchema) {
    // Implementation would compare required fields
    // This is a simplified version for demonstration
    console.log('  🔍 Checking for removed required fields...');
  }

  checkChangedFieldTypes(oldSchema, newSchema) {
    console.log('  🔍 Checking for changed field types...');
  }

  checkRemovedEnumValues(oldSchema, newSchema) {
    console.log('  🔍 Checking for removed enum values...');
  }

  checkAddedRequiredFields(oldSchema, newSchema) {
    console.log('  🔍 Checking for added required fields...');
  }

  checkAddedOptionalFields(oldSchema, newSchema) {
    console.log('  🔍 Checking for added optional fields...');
  }

  checkAddedEnumValues(oldSchema, newSchema) {
    console.log('  🔍 Checking for added enum values...');
  }

  reportResults() {
    console.log('\n📊 Compatibility Check Results');
    console.log('='.repeat(50));

    if (this.breakingChanges.length === 0) {
      console.log('✅ No breaking changes detected - schemas are backward compatible');
    } else {
      console.log(`❌ Breaking changes detected (${this.breakingChanges.length}):`);
      this.breakingChanges.forEach((change, index) => {
        console.log(`  ${index + 1}. ${change}`);
      });
    }

    if (this.compatibleChanges.length > 0) {
      console.log(`\n✅ Compatible changes (${this.compatibleChanges.length}):`);
      this.compatibleChanges.forEach((change, index) => {
        console.log(`  ${index + 1}. ${change}`);
      });
    }

    console.log(`\n📈 Summary:`);
    console.log(`  Breaking changes: ${this.breakingChanges.length}`);
    console.log(`  Compatible changes: ${this.compatibleChanges.length}`);
    console.log(`  Recommendation: ${this.getVersionRecommendation()}`);

    if (this.breakingChanges.length > 0) {
      process.exit(1);
    }
  }

  getVersionRecommendation() {
    if (this.breakingChanges.length > 0) {
      return 'MAJOR version bump required';
    } else if (this.compatibleChanges.length > 0) {
      return 'MINOR version bump recommended';
    } else {
      return 'No version change needed';
    }
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.log('Usage: node check-compatibility.js <old-schema.json> <new-schema.json>');
    process.exit(1);
  }

  const checker = new CompatibilityChecker();
  checker.checkCompatibility(args[0], args[1]);
}

module.exports = CompatibilityChecker;
