# Event Schemas

> **Purpose**: Detailed event payload definitions  
> **Audience**: Developers implementing event producers and consumers

## Overview
JSON Schema definitions for all events in the VibeMatch system. These schemas ensure consistency across services and enable validation of event payloads.

## Schema Organization
### By Domain
- **user-events.json** - User lifecycle events
- **agent-events.json** - Agent management events
- **task-events.json** - Task workflow events
- **credit-events.json** - Financial transaction events
- **orchestration-events.json** - Multi-agent coordination

### By Type
- **domain-events/** - Business logic events
- **integration-events/** - External system events
- **system-events/** - Infrastructure events

## Schema Standards
### Base Event Schema
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "required": ["eventId", "eventType", "timestamp", "version"],
  "properties": {
    "eventId": { "type": "string", "format": "uuid" },
    "eventType": { "type": "string" },
    "timestamp": { "type": "string", "format": "date-time" },
    "version": { "type": "string", "pattern": "^\\d+\\.\\d+$" }
  }
}
```

### Event Examples
#### TaskCreated Event
```json
{
  "eventId": "550e8400-e29b-41d4-a716-************",
  "eventType": "TaskCreated",
  "timestamp": "2025-06-26T10:00:00Z",
  "version": "1.0",
  "payload": {
    "taskId": "task_123",
    "userId": "user_456",
    "title": "Generate marketing copy",
    "requirements": {
      "agentType": "content-creator",
      "estimatedCredits": 100
    }
  }
}
```

## Validation
- All events validated against schemas
- Schema validation in producers
- Runtime validation in consumers
- Schema evolution rules enforced

## Versioning Strategy
- Backward compatible changes allowed
- New optional fields OK
- Breaking changes require new version
- Deprecation notices required

---
**Section Owner**: Architecture Team  
**Last Updated**: 2025-06-26  
**Parent**: [Events](../)