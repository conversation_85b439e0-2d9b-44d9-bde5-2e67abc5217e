{"description": "Test examples for validating event schemas", "examples": {"matching-events": {"match.found": {"eventId": "evt_1234567890abcdef", "type": "match.found", "timestamp": "2025-06-26T14:30:00Z", "version": "1.0.0", "source": {"service": "matching-engine", "instance": "matching-pod-abc123"}, "data": {"matchId": "match_9876543210fedcba", "taskId": "task_1234567890abcdef", "userId": "usr_1234567890abcdef12", "matches": [{"agentId": "agent_1122334455667788", "score": 0.95, "estimatedCost": 150, "estimatedDuration": 120, "capabilities": ["content-creation", "seo-optimization"], "availability": "immediate", "qualityScore": 4.8, "completionRate": 0.98}], "matchingCriteria": {"requiredCapabilities": ["content-creation"], "maxBudget": 200, "urgency": "normal", "qualityThreshold": 4.0}, "expiresAt": "2025-06-26T16:30:00Z", "algorithmVersion": "v2.1.0"}, "metadata": {"correlationId": "corr_1234567890", "userId": "usr_1234567890abcdef12"}}, "match.accepted": {"eventId": "evt_2345678901bcdefg", "type": "match.accepted", "timestamp": "2025-06-26T14:45:00Z", "version": "1.0.0", "source": {"service": "task-management", "instance": "task-pod-def456"}, "data": {"matchId": "match_9876543210fedcba", "taskId": "task_1234567890abcdef", "userId": "usr_1234567890abcdef12", "selectedAgent": {"agentId": "agent_1122334455667788", "score": 0.95, "estimatedCost": 150, "estimatedDuration": 120, "capabilities": ["content-creation", "seo-optimization"], "availability": "immediate", "acceptedAt": "2025-06-26T14:45:00Z"}, "rejectedAgents": [{"agentId": "agent_2233445566778899", "reason": "higher_cost"}], "selectionReason": "best_score_and_availability"}, "metadata": {"correlationId": "corr_1234567890", "causationId": "evt_1234567890abcdef", "userId": "usr_1234567890abcdef12"}}, "match.rejected": {"eventId": "evt_3456789012cdefgh", "type": "match.rejected", "timestamp": "2025-06-26T15:00:00Z", "version": "1.0.0", "source": {"service": "task-management", "instance": "task-pod-def456"}, "data": {"matchId": "match_9876543210fedcba", "taskId": "task_1234567890abcdef", "userId": "usr_1234567890abcdef12", "rejectedAgents": [{"agentId": "agent_1122334455667788", "reason": "too_expensive"}, {"agentId": "agent_2233445566778899", "reason": "insufficient_experience"}], "feedback": {"preferredBudget": 100, "requiredExperience": "5+ years", "additionalRequirements": "Must have portfolio examples"}, "requestNewMatches": true}, "metadata": {"correlationId": "corr_1234567890", "causationId": "evt_1234567890abcdef", "userId": "usr_1234567890abcdef12"}}}, "credit-events": {"credit.added": {"eventId": "evt_4567890123defghi", "type": "credit.added", "timestamp": "2025-06-26T16:15:00Z", "version": "1.0.0", "source": {"service": "credit-system", "instance": "credit-pod-ghi789"}, "data": {"transactionId": "txn_1234567890abcdef", "userId": "usr_1234567890abcdef12", "amount": 1000, "source": "purchase", "sourceDetails": {"paymentId": "pay_9876543210fedcba", "packageType": "standard", "promoCode": "WELCOME20", "bonusCredits": 200}, "balanceBefore": 150, "balanceAfter": 1150, "expiresAt": "2026-06-26T16:15:00Z", "metadata": {"currency": "USD", "exchangeRate": 0.01, "purchaseAmount": 99.99}}, "metadata": {"correlationId": "corr_2345678901", "causationId": "evt_billing_purchase_123", "userId": "usr_1234567890abcdef12"}}, "credit.reserved": {"eventId": "evt_5678901234<PERSON><PERSON>ij", "type": "credit.reserved", "timestamp": "2025-06-26T16:30:00Z", "version": "1.0.0", "source": {"service": "credit-system", "instance": "credit-pod-ghi789"}, "data": {"reservationId": "res_1234567890abcdef", "userId": "usr_1234567890abcdef12", "taskId": "task_1234567890abcdef", "agentId": "agent_1122334455667788", "amount": 150, "estimatedAmount": 150, "maxAmount": 200, "balanceBefore": 1150, "availableAfter": 1000, "reservedUntil": "2025-06-26T20:30:00Z", "purpose": "task_execution", "metadata": {"taskType": "content-creation", "urgency": "normal", "qualityLevel": "premium"}}, "metadata": {"correlationId": "corr_3456789012", "causationId": "evt_match_accepted_456", "userId": "usr_1234567890abcdef12"}}, "credit.deducted": {"eventId": "evt_6789012345fghijk", "type": "credit.deducted", "timestamp": "2025-06-26T18:45:00Z", "version": "1.0.0", "source": {"service": "credit-system", "instance": "credit-pod-ghi789"}, "data": {"transactionId": "txn_2345678901bcdefg", "reservationId": "res_1234567890abcdef", "userId": "usr_1234567890abcdef12", "taskId": "task_1234567890abcdef", "agentId": "agent_1122334455667788", "amount": 125, "reservedAmount": 150, "refundedAmount": 25, "balanceBefore": 1000, "balanceAfter": 875, "deductionReason": "task_completed", "taskDetails": {"duration": 90, "quality": "excellent", "deliverables": 3, "userRating": 5}, "agentEarnings": {"amount": 100, "commission": 20, "bonusEarnings": 5}}, "metadata": {"correlationId": "corr_3456789012", "causationId": "evt_task_completed_789", "userId": "usr_1234567890abcdef12"}}}}}