{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://vibe-match.com/schemas/events/matching/v1.0.0", "title": "Matching Engine Events Schema", "description": "Event schemas for matching engine operations in VibeMatch", "definitions": {"matchId": {"type": "string", "pattern": "^match_[a-zA-Z0-9]{16}$", "description": "Unique match identifier"}, "taskId": {"type": "string", "pattern": "^task_[a-zA-Z0-9]{16}$", "description": "Unique task identifier"}, "agentId": {"type": "string", "pattern": "^agent_[a-zA-Z0-9]{16}$", "description": "Unique agent identifier"}, "userId": {"type": "string", "pattern": "^usr_[a-zA-Z0-9]{20}$", "description": "Unique user identifier"}, "timestamp": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp"}, "eventMetadata": {"type": "object", "required": ["eventId", "type", "timestamp", "version", "source"], "properties": {"eventId": {"type": "string", "pattern": "^evt_[a-zA-Z0-9]{16}$", "description": "Unique event identifier"}, "type": {"type": "string", "enum": ["match.found", "match.accepted", "match.rejected", "match.expired", "match.algorithm_updated"]}, "timestamp": {"$ref": "#/definitions/timestamp"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Event schema version"}, "source": {"type": "object", "required": ["service", "instance"], "properties": {"service": {"type": "string", "enum": ["matching-engine", "task-management"]}, "instance": {"type": "string", "description": "Service instance identifier"}}}}}, "agentMatch": {"type": "object", "required": ["agentId", "score", "estimatedCost", "estimatedDuration", "capabilities", "availability"], "properties": {"agentId": {"$ref": "#/definitions/agentId"}, "score": {"type": "number", "minimum": 0, "maximum": 1, "description": "Matching score between 0 and 1"}, "estimatedCost": {"type": "integer", "minimum": 1, "description": "Estimated cost in credits"}, "estimatedDuration": {"type": "integer", "minimum": 1, "description": "Estimated duration in minutes"}, "capabilities": {"type": "array", "items": {"type": "string"}, "minItems": 1, "description": "Agent capabilities"}, "availability": {"type": "string", "enum": ["immediate", "within-1-hour", "within-2-hours", "within-4-hours", "within-24-hours"], "description": "Agent availability"}, "qualityScore": {"type": "number", "minimum": 0, "maximum": 5, "description": "Agent quality rating"}, "completionRate": {"type": "number", "minimum": 0, "maximum": 1, "description": "Agent task completion rate"}}}, "matchingCriteria": {"type": "object", "required": ["requiredCapabilities"], "properties": {"requiredCapabilities": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "maxBudget": {"type": "integer", "minimum": 1}, "urgency": {"type": "string", "enum": ["low", "normal", "high", "urgent"]}, "qualityThreshold": {"type": "number", "minimum": 0, "maximum": 5}, "preferredAvailability": {"type": "string", "enum": ["immediate", "within-1-hour", "within-2-hours", "within-4-hours", "within-24-hours"]}}}}, "oneOf": [{"type": "object", "required": ["eventId", "type", "timestamp", "version", "source", "data", "metadata"], "properties": {"eventId": {"$ref": "#/definitions/eventMetadata/properties/eventId"}, "type": {"const": "match.found"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "version": {"$ref": "#/definitions/eventMetadata/properties/version"}, "source": {"$ref": "#/definitions/eventMetadata/properties/source"}, "data": {"type": "object", "required": ["matchId", "taskId", "userId", "matches", "matchingCriteria", "expiresAt"], "properties": {"matchId": {"$ref": "#/definitions/matchId"}, "taskId": {"$ref": "#/definitions/taskId"}, "userId": {"$ref": "#/definitions/userId"}, "matches": {"type": "array", "items": {"$ref": "#/definitions/agentMatch"}, "minItems": 1, "maxItems": 10}, "matchingCriteria": {"$ref": "#/definitions/matchingCriteria"}, "expiresAt": {"$ref": "#/definitions/timestamp"}, "algorithmVersion": {"type": "string", "description": "Version of matching algorithm used"}}}, "metadata": {"type": "object", "required": ["correlationId", "userId"], "properties": {"correlationId": {"type": "string", "description": "Request correlation ID"}, "causationId": {"type": "string", "description": "Previous event that caused this one"}, "userId": {"$ref": "#/definitions/userId"}}}}}, {"type": "object", "required": ["eventId", "type", "timestamp", "version", "source", "data", "metadata"], "properties": {"eventId": {"$ref": "#/definitions/eventMetadata/properties/eventId"}, "type": {"const": "match.accepted"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "version": {"$ref": "#/definitions/eventMetadata/properties/version"}, "source": {"$ref": "#/definitions/eventMetadata/properties/source"}, "data": {"type": "object", "required": ["matchId", "taskId", "userId", "selectedAgent"], "properties": {"matchId": {"$ref": "#/definitions/matchId"}, "taskId": {"$ref": "#/definitions/taskId"}, "userId": {"$ref": "#/definitions/userId"}, "selectedAgent": {"allOf": [{"$ref": "#/definitions/agentMatch"}, {"type": "object", "required": ["acceptedAt"], "properties": {"acceptedAt": {"$ref": "#/definitions/timestamp"}}}]}, "rejectedAgents": {"type": "array", "items": {"type": "object", "required": ["agentId", "reason"], "properties": {"agentId": {"$ref": "#/definitions/agentId"}, "reason": {"type": "string", "enum": ["higher_cost", "longer_duration", "lower_quality", "unavailable", "other"]}}}}, "selectionReason": {"type": "string", "enum": ["best_score", "lowest_cost", "fastest_delivery", "highest_quality", "best_availability", "user_preference"]}}}, "metadata": {"type": "object", "required": ["correlationId", "causationId", "userId"], "properties": {"correlationId": {"type": "string"}, "causationId": {"type": "string"}, "userId": {"$ref": "#/definitions/userId"}}}}}, {"type": "object", "required": ["eventId", "type", "timestamp", "version", "source", "data", "metadata"], "properties": {"eventId": {"$ref": "#/definitions/eventMetadata/properties/eventId"}, "type": {"const": "match.rejected"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "version": {"$ref": "#/definitions/eventMetadata/properties/version"}, "source": {"$ref": "#/definitions/eventMetadata/properties/source"}, "data": {"type": "object", "required": ["matchId", "taskId", "userId", "rejectedAgents"], "properties": {"matchId": {"$ref": "#/definitions/matchId"}, "taskId": {"$ref": "#/definitions/taskId"}, "userId": {"$ref": "#/definitions/userId"}, "rejectedAgents": {"type": "array", "items": {"type": "object", "required": ["agentId", "reason"], "properties": {"agentId": {"$ref": "#/definitions/agentId"}, "reason": {"type": "string", "enum": ["too_expensive", "insufficient_experience", "poor_quality", "unavailable", "wrong_capabilities", "other"]}}}, "minItems": 1}, "feedback": {"type": "object", "properties": {"preferredBudget": {"type": "integer", "minimum": 1}, "requiredExperience": {"type": "string"}, "additionalRequirements": {"type": "string"}, "qualityExpectations": {"type": "string"}}}, "requestNewMatches": {"type": "boolean", "description": "Whether user wants new matches"}}}, "metadata": {"type": "object", "required": ["correlationId", "causationId", "userId"], "properties": {"correlationId": {"type": "string"}, "causationId": {"type": "string"}, "userId": {"$ref": "#/definitions/userId"}}}}}]}