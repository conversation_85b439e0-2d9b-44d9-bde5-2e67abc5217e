{"name": "@vibe-match/event-schemas", "version": "1.0.0", "description": "JSON Schema definitions and validation for VibeMatch events", "main": "validate-schemas.js", "scripts": {"validate": "node validate-schemas.js", "validate:watch": "nodemon validate-schemas.js", "test": "npm run validate", "lint:schemas": "ajv compile -s '*.json' --strict=false", "format:schemas": "prettier --write '*.json'", "check:compatibility": "node check-compatibility.js"}, "dependencies": {"ajv": "^8.12.0", "ajv-formats": "^2.1.1"}, "devDependencies": {"nodemon": "^3.0.1", "prettier": "^3.0.0"}, "keywords": ["vibe-match", "events", "json-schema", "validation", "event-driven-architecture"], "author": "VibeMatch Platform Team", "license": "UNLICENSED", "private": true, "engines": {"node": ">=16.0.0"}}