{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://vibe-match.com/schemas/events/credit/v1.0.0", "title": "Credit System Events Schema", "description": "Event schemas for credit system operations in VibeMatch", "definitions": {"transactionId": {"type": "string", "pattern": "^txn_[a-zA-Z0-9]{16}$", "description": "Unique transaction identifier"}, "reservationId": {"type": "string", "pattern": "^res_[a-zA-Z0-9]{16}$", "description": "Unique reservation identifier"}, "userId": {"type": "string", "pattern": "^usr_[a-zA-Z0-9]{20}$", "description": "Unique user identifier"}, "taskId": {"type": "string", "pattern": "^task_[a-zA-Z0-9]{16}$", "description": "Unique task identifier"}, "agentId": {"type": "string", "pattern": "^agent_[a-zA-Z0-9]{16}$", "description": "Unique agent identifier"}, "timestamp": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp"}, "creditAmount": {"type": "integer", "minimum": 1, "description": "Credit amount (positive integer)"}, "eventMetadata": {"type": "object", "required": ["eventId", "type", "timestamp", "version", "source"], "properties": {"eventId": {"type": "string", "pattern": "^evt_[a-zA-Z0-9]{16}$", "description": "Unique event identifier"}, "type": {"type": "string", "enum": ["credit.added", "credit.reserved", "credit.deducted", "credit.refunded", "credit.expired", "credit.transferred", "credit.balance_low"]}, "timestamp": {"$ref": "#/definitions/timestamp"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Event schema version"}, "source": {"type": "object", "required": ["service", "instance"], "properties": {"service": {"type": "string", "const": "credit-system"}, "instance": {"type": "string", "description": "Service instance identifier"}}}}}}, "oneOf": [{"type": "object", "required": ["eventId", "type", "timestamp", "version", "source", "data", "metadata"], "properties": {"eventId": {"$ref": "#/definitions/eventMetadata/properties/eventId"}, "type": {"const": "credit.added"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "version": {"$ref": "#/definitions/eventMetadata/properties/version"}, "source": {"$ref": "#/definitions/eventMetadata/properties/source"}, "data": {"type": "object", "required": ["transactionId", "userId", "amount", "source", "balanceBefore", "balanceAfter"], "properties": {"transactionId": {"$ref": "#/definitions/transactionId"}, "userId": {"$ref": "#/definitions/userId"}, "amount": {"$ref": "#/definitions/creditAmount"}, "source": {"type": "string", "enum": ["purchase", "bonus", "refund", "promotion", "admin_adjustment", "referral"]}, "sourceDetails": {"type": "object", "properties": {"paymentId": {"type": "string"}, "packageType": {"type": "string", "enum": ["starter", "standard", "premium", "enterprise"]}, "promoCode": {"type": "string"}, "bonusCredits": {"type": "integer", "minimum": 0}, "referralUserId": {"$ref": "#/definitions/userId"}}}, "balanceBefore": {"type": "integer", "minimum": 0}, "balanceAfter": {"type": "integer", "minimum": 0}, "expiresAt": {"$ref": "#/definitions/timestamp"}, "metadata": {"type": "object", "properties": {"currency": {"type": "string", "enum": ["USD", "EUR", "GBP"]}, "exchangeRate": {"type": "number", "minimum": 0}, "purchaseAmount": {"type": "number", "minimum": 0}}}}}, "metadata": {"type": "object", "required": ["correlationId", "userId"], "properties": {"correlationId": {"type": "string"}, "causationId": {"type": "string"}, "userId": {"$ref": "#/definitions/userId"}}}}}, {"type": "object", "required": ["eventId", "type", "timestamp", "version", "source", "data", "metadata"], "properties": {"eventId": {"$ref": "#/definitions/eventMetadata/properties/eventId"}, "type": {"const": "credit.reserved"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "version": {"$ref": "#/definitions/eventMetadata/properties/version"}, "source": {"$ref": "#/definitions/eventMetadata/properties/source"}, "data": {"type": "object", "required": ["reservationId", "userId", "taskId", "agentId", "amount", "balanceBefore", "availableAfter", "reservedUntil", "purpose"], "properties": {"reservationId": {"$ref": "#/definitions/reservationId"}, "userId": {"$ref": "#/definitions/userId"}, "taskId": {"$ref": "#/definitions/taskId"}, "agentId": {"$ref": "#/definitions/agentId"}, "amount": {"$ref": "#/definitions/creditAmount"}, "estimatedAmount": {"$ref": "#/definitions/creditAmount"}, "maxAmount": {"$ref": "#/definitions/creditAmount"}, "balanceBefore": {"type": "integer", "minimum": 0}, "availableAfter": {"type": "integer", "minimum": 0}, "reservedUntil": {"$ref": "#/definitions/timestamp"}, "purpose": {"type": "string", "enum": ["task_execution", "agent_booking", "premium_feature", "subscription"]}, "metadata": {"type": "object", "properties": {"taskType": {"type": "string"}, "urgency": {"type": "string", "enum": ["low", "normal", "high", "urgent"]}, "qualityLevel": {"type": "string", "enum": ["basic", "standard", "premium", "enterprise"]}}}}}, "metadata": {"type": "object", "required": ["correlationId", "causationId", "userId"], "properties": {"correlationId": {"type": "string"}, "causationId": {"type": "string"}, "userId": {"$ref": "#/definitions/userId"}}}}}, {"type": "object", "required": ["eventId", "type", "timestamp", "version", "source", "data", "metadata"], "properties": {"eventId": {"$ref": "#/definitions/eventMetadata/properties/eventId"}, "type": {"const": "credit.deducted"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "version": {"$ref": "#/definitions/eventMetadata/properties/version"}, "source": {"$ref": "#/definitions/eventMetadata/properties/source"}, "data": {"type": "object", "required": ["transactionId", "userId", "taskId", "agentId", "amount", "balanceBefore", "balanceAfter", "deductionReason"], "properties": {"transactionId": {"$ref": "#/definitions/transactionId"}, "reservationId": {"$ref": "#/definitions/reservationId"}, "userId": {"$ref": "#/definitions/userId"}, "taskId": {"$ref": "#/definitions/taskId"}, "agentId": {"$ref": "#/definitions/agentId"}, "amount": {"$ref": "#/definitions/creditAmount"}, "reservedAmount": {"type": "integer", "minimum": 0}, "refundedAmount": {"type": "integer", "minimum": 0}, "balanceBefore": {"type": "integer", "minimum": 0}, "balanceAfter": {"type": "integer", "minimum": 0}, "deductionReason": {"type": "string", "enum": ["task_completed", "task_partial", "subscription_fee", "premium_feature", "penalty"]}, "taskDetails": {"type": "object", "properties": {"duration": {"type": "integer", "minimum": 1}, "quality": {"type": "string", "enum": ["poor", "fair", "good", "excellent"]}, "deliverables": {"type": "integer", "minimum": 0}, "userRating": {"type": "integer", "minimum": 1, "maximum": 5}}}, "agentEarnings": {"type": "object", "properties": {"amount": {"type": "integer", "minimum": 0}, "commission": {"type": "integer", "minimum": 0}, "bonusEarnings": {"type": "integer", "minimum": 0}}}}}, "metadata": {"type": "object", "required": ["correlationId", "causationId", "userId"], "properties": {"correlationId": {"type": "string"}, "causationId": {"type": "string"}, "userId": {"$ref": "#/definitions/userId"}}}}}]}