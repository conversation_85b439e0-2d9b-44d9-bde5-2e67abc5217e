{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://vibe-match.com/schemas/events/task/v1.0.0", "title": "Task Events Schema", "description": "Event schemas for task-related operations in VibeMatch", "definitions": {"taskId": {"type": "string", "pattern": "^tsk_[a-zA-Z0-9]{20}$", "description": "Unique task identifier"}, "userId": {"type": "string", "pattern": "^usr_[a-zA-Z0-9]{20}$", "description": "User identifier"}, "agentId": {"type": "string", "pattern": "^agt_[a-zA-Z0-9]{20}$", "description": "Agent identifier"}, "orchestrationId": {"type": "string", "pattern": "^orch_[a-zA-Z0-9]{20}$", "description": "Orchestration identifier for multi-agent tasks"}, "timestamp": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp"}, "taskStatus": {"type": "string", "enum": ["created", "pending", "matched", "in_progress", "completed", "failed", "cancelled"]}, "taskType": {"type": "string", "enum": ["single_agent", "multi_agent", "orchestrated"]}}, "oneOf": [{"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "task.created"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["taskId", "userId", "title", "type"], "properties": {"taskId": {"$ref": "#/definitions/taskId"}, "userId": {"$ref": "#/definitions/userId"}, "title": {"type": "string"}, "description": {"type": "string"}, "type": {"$ref": "#/definitions/taskType"}, "requirements": {"type": "array", "items": {"type": "string"}}, "budget": {"type": "number", "minimum": 0}, "deadline": {"$ref": "#/definitions/timestamp"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "task.matched"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["taskId", "matches"], "properties": {"taskId": {"$ref": "#/definitions/taskId"}, "orchestrationId": {"$ref": "#/definitions/orchestrationId"}, "matches": {"type": "array", "items": {"type": "object", "required": ["agentId", "matchScore"], "properties": {"agentId": {"$ref": "#/definitions/agentId"}, "matchScore": {"type": "number", "minimum": 0, "maximum": 1}, "role": {"type": "string"}}}}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "task.started"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["taskId", "agentId"], "properties": {"taskId": {"$ref": "#/definitions/taskId"}, "agentId": {"$ref": "#/definitions/agentId"}, "orchestrationId": {"$ref": "#/definitions/orchestrationId"}, "startTime": {"$ref": "#/definitions/timestamp"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "task.progress_updated"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["taskId", "progress"], "properties": {"taskId": {"$ref": "#/definitions/taskId"}, "agentId": {"$ref": "#/definitions/agentId"}, "progress": {"type": "number", "minimum": 0, "maximum": 100}, "milestone": {"type": "string"}, "update": {"type": "string"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "task.completed"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["taskId", "result"], "properties": {"taskId": {"$ref": "#/definitions/taskId"}, "agentId": {"$ref": "#/definitions/agentId"}, "orchestrationId": {"$ref": "#/definitions/orchestrationId"}, "result": {"type": "object", "properties": {"success": {"type": "boolean"}, "deliverables": {"type": "array", "items": {"type": "string"}}, "summary": {"type": "string"}}}, "duration": {"type": "integer", "description": "Duration in minutes"}, "creditsUsed": {"type": "number"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "task.failed"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["taskId", "reason"], "properties": {"taskId": {"$ref": "#/definitions/taskId"}, "agentId": {"$ref": "#/definitions/agentId"}, "reason": {"type": "string"}, "error": {"type": "string"}, "canRetry": {"type": "boolean"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "task.cancelled"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["taskId", "cancelledBy", "reason"], "properties": {"taskId": {"$ref": "#/definitions/taskId"}, "cancelledBy": {"$ref": "#/definitions/userId"}, "reason": {"type": "string"}, "refundAmount": {"type": "number"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "task.orchestration_step"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["taskId", "orchestrationId", "step"], "properties": {"taskId": {"$ref": "#/definitions/taskId"}, "orchestrationId": {"$ref": "#/definitions/orchestrationId"}, "step": {"type": "object", "properties": {"number": {"type": "integer"}, "name": {"type": "string"}, "agentId": {"$ref": "#/definitions/agentId"}, "status": {"enum": ["pending", "running", "completed", "failed"]}, "input": {"type": "object"}, "output": {"type": "object"}}}}}}}]}