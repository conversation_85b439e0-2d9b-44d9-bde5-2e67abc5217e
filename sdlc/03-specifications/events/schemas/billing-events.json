{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://vibe-match.com/schemas/events/billing/v1.0.0", "title": "Billing Events Schema", "description": "Event schemas for billing and payment operations in VibeMatch", "definitions": {"transactionId": {"type": "string", "pattern": "^txn_[a-zA-Z0-9]{20}$", "description": "Unique transaction identifier"}, "userId": {"type": "string", "pattern": "^usr_[a-zA-Z0-9]{20}$", "description": "User identifier"}, "agentId": {"type": "string", "pattern": "^agt_[a-zA-Z0-9]{20}$", "description": "Agent identifier"}, "taskId": {"type": "string", "pattern": "^tsk_[a-zA-Z0-9]{20}$", "description": "Task identifier"}, "timestamp": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp"}, "amount": {"type": "number", "minimum": 0, "description": "Amount in credits or USD"}, "currency": {"type": "string", "enum": ["CREDITS", "USD"], "description": "Transaction currency"}}, "oneOf": [{"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "billing.credits_purchased"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["transactionId", "userId", "amount", "credits"], "properties": {"transactionId": {"$ref": "#/definitions/transactionId"}, "userId": {"$ref": "#/definitions/userId"}, "amount": {"$ref": "#/definitions/amount"}, "currency": {"const": "USD"}, "credits": {"type": "integer", "minimum": 1}, "paymentMethod": {"enum": ["card", "bank", "paypal"]}, "promoCode": {"type": "string"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "billing.credits_deducted"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["transactionId", "userId", "credits", "taskId"], "properties": {"transactionId": {"$ref": "#/definitions/transactionId"}, "userId": {"$ref": "#/definitions/userId"}, "credits": {"type": "integer", "minimum": 1}, "taskId": {"$ref": "#/definitions/taskId"}, "reason": {"type": "string"}, "remainingBalance": {"type": "integer", "minimum": 0}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "billing.credits_refunded"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["transactionId", "originalTransactionId", "userId", "credits"], "properties": {"transactionId": {"$ref": "#/definitions/transactionId"}, "originalTransactionId": {"$ref": "#/definitions/transactionId"}, "userId": {"$ref": "#/definitions/userId"}, "credits": {"type": "integer", "minimum": 1}, "reason": {"type": "string"}, "taskId": {"$ref": "#/definitions/taskId"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "billing.agent_payout_initiated"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["transactionId", "agentId", "amount", "period"], "properties": {"transactionId": {"$ref": "#/definitions/transactionId"}, "agentId": {"$ref": "#/definitions/agentId"}, "userId": {"$ref": "#/definitions/userId"}, "amount": {"$ref": "#/definitions/amount"}, "currency": {"const": "USD"}, "period": {"type": "object", "properties": {"from": {"$ref": "#/definitions/timestamp"}, "to": {"$ref": "#/definitions/timestamp"}}}, "taskCount": {"type": "integer"}, "platformFee": {"$ref": "#/definitions/amount"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "billing.agent_payout_completed"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["transactionId", "payoutId", "agentId", "status"], "properties": {"transactionId": {"$ref": "#/definitions/transactionId"}, "payoutId": {"type": "string"}, "agentId": {"$ref": "#/definitions/agentId"}, "status": {"enum": ["success", "failed", "pending"]}, "paymentMethod": {"enum": ["bank_transfer", "paypal", "stripe"]}, "failureReason": {"type": "string"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "billing.payment_failed"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["transactionId", "userId", "amount", "reason"], "properties": {"transactionId": {"$ref": "#/definitions/transactionId"}, "userId": {"$ref": "#/definitions/userId"}, "amount": {"$ref": "#/definitions/amount"}, "currency": {"$ref": "#/definitions/currency"}, "reason": {"type": "string"}, "errorCode": {"type": "string"}, "retryable": {"type": "boolean"}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "billing.fraud_detected"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["transactionId", "userId", "riskScore", "action"], "properties": {"transactionId": {"$ref": "#/definitions/transactionId"}, "userId": {"$ref": "#/definitions/userId"}, "riskScore": {"type": "number", "minimum": 0, "maximum": 1}, "indicators": {"type": "array", "items": {"type": "string"}}, "action": {"enum": ["blocked", "flagged", "manual_review"]}}}}}, {"type": "object", "required": ["type", "timestamp", "data"], "properties": {"type": {"const": "billing.subscription_created"}, "timestamp": {"$ref": "#/definitions/timestamp"}, "data": {"type": "object", "required": ["subscriptionId", "userId", "plan", "credits"], "properties": {"subscriptionId": {"type": "string"}, "userId": {"$ref": "#/definitions/userId"}, "plan": {"enum": ["starter", "professional", "enterprise"]}, "credits": {"type": "integer", "description": "Monthly credit allocation"}, "price": {"$ref": "#/definitions/amount"}, "interval": {"enum": ["monthly", "yearly"]}}}}}]}