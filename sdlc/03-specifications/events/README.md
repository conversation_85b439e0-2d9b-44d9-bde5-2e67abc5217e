# VibeMatch Event Catalog

> **Purpose**: Central hub for all event-driven architecture documentation  
> **Audience**: All developers, architects, QA engineers  
> **Version**: 1.0.0  
> **Last Updated**: 2025-06-26

## 🚀 Quick Start

### For Developers
- **Publishing Events**: [Publisher Guide](./implementation/publisher-guide.md)
- **Consuming Events**: [Subscriber Guide](./implementation/subscriber-guide.md)
- **Event Schemas**: [Schema Directory](./schemas/)
- **Event Flows**: [Flow Diagrams](./flows/)

### For Architects
- **Complete Event Catalog**: [event-catalog.md](./event-catalog.md)
- **Naming Conventions**: [event-naming-conventions.md](./event-naming-conventions.md)
- **Versioning Strategy**: [event-versioning-strategy.md](./event-versioning-strategy.md)

## 📋 Event Catalog Overview

VibeMatch uses Google Cloud Pub/Sub for asynchronous communication between its 11 microservices. This catalog documents all events, their schemas, flows, and implementation patterns.

### Event Categories

| Category | Events | Description |
|----------|--------|-------------|
| **User Events** | 8 events | User lifecycle and profile management |
| **Agent Events** | 10 events | AI agent operations and state management |
| **Task Events** | 12 events | Task lifecycle and orchestration |
| **Matching Events** | 5 events | Agent-task matching and selection |
| **Credit Events** | 7 events | Credit balance management and transactions |
| **Billing Events** | 8 events | Payment processing and financial transactions |
| **System Events** | 6 events | Platform health and operational monitoring |

**Total Events Documented**: 56 events

## 📁 Directory Structure

```
/events/
├── README.md                           # This file - main index
├── event-catalog.md                    # Complete event documentation
├── event-naming-conventions.md         # Naming standards and rules
├── event-versioning-strategy.md        # Version management approach
├── schemas/                            # JSON Schema definitions
│   ├── README.md                       # Schema documentation
│   ├── user-events.json               # User domain events
│   ├── agent-events.json              # Agent domain events
│   ├── task-events.json               # Task domain events
│   ├── matching-events.json           # Matching engine events
│   ├── credit-events.json             # Credit system events
│   └── billing-events.json            # Billing domain events
├── flows/                              # Event flow diagrams
│   ├── README.md                       # Flow documentation
│   ├── task-completion-flow.md         # Task completion sequence
│   ├── payment-flow.md                 # Payment processing flow
│   ├── multi-agent-orchestration-flow.md # Multi-agent coordination
│   └── agent-matching-flow.md          # Agent selection process
└── implementation/                     # Implementation guides
    ├── publisher-guide.md              # Event publishing guide
    └── subscriber-guide.md             # Event consumption guide
```

## 🎯 Core Events by Domain

### User Domain Events
```
user.created              # New user registration
user.updated              # Profile information changed
user.deleted              # Account permanently removed
user.suspended            # Account temporarily disabled
user.reactivated          # Suspended account restored
user.email_verified       # Email verification completed
user.password_changed     # Password updated
user.preferences_updated  # User settings modified
```

### Agent Domain Events
```
agent.created             # New AI agent registered
agent.updated             # Agent profile modified
agent.approved            # Agent approved for marketplace
agent.rejected            # Agent review failed
agent.suspended           # Agent temporarily disabled
agent.reactivated         # Agent suspension lifted
agent.rated               # User rated agent performance
agent.performance_updated # Performance metrics updated
agent.capabilities_updated # Agent skills modified
agent.availability_changed # Availability status updated
```

### Task Domain Events
```
task.created              # New task submitted
task.assigned             # Agent assigned to task
task.started              # Agent begins work
task.progress_updated     # Milestone or progress report
task.completed            # Task finished successfully
task.cancelled            # Task cancelled by user
task.failed               # Task execution failed
task.disputed             # Task result disputed
task.rated                # User rated task completion
task.deadline_extended    # Task deadline modified
task.requirements_updated # Task specifications changed
task.orchestration_started # Multi-agent task begins
```

### Matching Engine Events
```
match.found               # Suitable agents identified
match.accepted            # User selected an agent
match.rejected            # User rejected all matches
match.expired             # Match offer timed out
match.algorithm_updated   # Matching logic improved
```

### Credit System Events
```
credit.added              # Credits added to account
credit.deducted           # Credits removed from account
credit.reserved           # Credits held for pending task
credit.released           # Reserved credits returned
credit.expired            # Credits reached expiration
credit.transferred        # Credits moved between accounts
credit.balance_low        # Balance below threshold
```

### Billing Domain Events
```
billing.payment_processed    # Payment completed successfully
billing.payment_failed       # Payment processing failed
billing.invoice_generated    # Invoice created
billing.invoice_sent         # Invoice delivered to user
billing.agent_payout_initiated # Agent payment started
billing.agent_payout_completed # Agent payment finished
billing.refund_processed     # Refund completed
billing.chargeback_received  # Payment dispute filed
```

## 🔄 Key Event Flows

### 1. Task Completion Flow
**Trigger**: Agent completes a task  
**Key Events**: `task.completed` → `credit.deducted` → `billing.agent_payout_initiated`  
**Documentation**: [task-completion-flow.md](./flows/task-completion-flow.md)

### 2. Payment Processing Flow
**Trigger**: User purchases credits  
**Key Events**: `billing.payment_processed` → `credit.added` → `billing.invoice_generated`  
**Documentation**: [payment-flow.md](./flows/payment-flow.md)

### 3. Agent Matching Flow
**Trigger**: User creates a task  
**Key Events**: `task.created` → `match.found` → `match.accepted` → `credit.reserved`  
**Documentation**: [agent-matching-flow.md](./flows/agent-matching-flow.md)

### 4. Multi-Agent Orchestration Flow
**Trigger**: Complex task requiring multiple agents  
**Key Events**: `task.created` → `task.orchestration_started` → `task.subtask_completed` → `task.orchestration_completed`  
**Documentation**: [multi-agent-orchestration-flow.md](./flows/multi-agent-orchestration-flow.md)

## 📊 Event Metrics and Monitoring

### High-Volume Events (>100/day)
- `user.authenticated` (~2000/day)
- `task.created` (~300/day)
- `task.completed` (~200/day)
- `match.found` (~250/day)
- `credit.deducted` (~200/day)
- `billing.payment_processed` (~150/day)

### Critical Events (Business Impact)
- `billing.payment_failed` - Immediate alert required
- `task.failed` - Affects user satisfaction
- `system.error` - Platform stability
- `credit.balance_low` - User experience impact

### Event Processing SLAs
- **Event Publishing**: < 5 seconds
- **Event Processing**: < 30 seconds
- **Critical Event Alerts**: < 2 minutes
- **Event Replay**: < 1 hour

## 🛠️ Development Guidelines

### Publishing Events
1. **Use the EventPublisher class** from the publisher guide
2. **Validate events** against JSON schemas before publishing
3. **Include correlation IDs** for tracing
4. **Use ordering keys** for related events
5. **Handle publishing failures** with retry logic

### Consuming Events
1. **Use the EventSubscriber class** from the subscriber guide
2. **Make handlers idempotent** - safe to execute multiple times
3. **Handle duplicate events** gracefully
4. **Implement proper error handling** with retries
5. **Use dead letter queues** for failed messages

### Schema Evolution
1. **Follow semantic versioning** for schema changes
2. **Maintain backward compatibility** when possible
3. **Use dual publishing** during major version transitions
4. **Deprecate fields** before removing them
5. **Document all changes** in the event catalog

## 🔍 Schema Validation

All events must validate against their JSON schemas:

```bash
# Validate event against schema
npm run validate-event --type=user.created --version=2.1.0 --data=event.json

# Validate all schemas
npm run validate-schemas

# Check backward compatibility
npm run check-compatibility --from=1.0.0 --to=2.0.0
```

## 📈 Usage Examples

### Publishing a User Created Event
```typescript
import { UserEventPublisher } from '@vibe-match/events';

const publisher = new UserEventPublisher();

await publisher.publishUserCreated({
  userId: 'usr_1234567890abcdef12',
  email: '<EMAIL>',
  displayName: 'John Doe',
  userType: 'customer',
  registrationMethod: 'google'
});
```

### Subscribing to Task Events
```typescript
import { TaskEventSubscriber } from '@vibe-match/events';

const subscriber = new TaskEventSubscriber();

subscriber.registerHandler('task.completed', async (event) => {
  console.log(`Task ${event.data.taskId} completed by ${event.data.agentId}`);
  await processTaskCompletion(event.data);
});

await subscriber.start();
```

## 🚨 Troubleshooting

### Common Issues

1. **Event Validation Failures**
   - Check event data against JSON schema
   - Verify required fields are present
   - Ensure field types match schema

2. **Publishing Failures**
   - Check Google Cloud Pub/Sub connectivity
   - Verify topic exists and permissions
   - Review retry logic and error handling

3. **Subscription Lag**
   - Monitor subscription metrics
   - Scale subscriber instances
   - Optimize message processing

4. **Schema Compatibility Issues**
   - Use schema validation tools
   - Follow versioning guidelines
   - Test with backward compatibility checks

### Support Contacts

- **Event Architecture**: <EMAIL>
- **Platform Issues**: <EMAIL>
- **Schema Questions**: <EMAIL>

## 📚 Additional Resources

### External Documentation
- [Google Cloud Pub/Sub Documentation](https://cloud.google.com/pubsub/docs)
- [JSON Schema Specification](https://json-schema.org/)
- [Event-Driven Architecture Patterns](https://microservices.io/patterns/data/event-driven-architecture.html)

### Internal Resources
- [Service Map](../../01-architecture/service-map.md)
- [API Catalog](../api-catalog.md)
- [Local Development Setup](../../04-implementation/local-setup.md)

---

## 📝 Contributing

### Adding New Events
1. **Design the event** following naming conventions
2. **Create JSON schema** with proper validation
3. **Document in event catalog** with examples
4. **Add to appropriate flow diagrams**
5. **Update this README** with new event

### Updating Existing Events
1. **Follow versioning strategy** for schema changes
2. **Update documentation** immediately
3. **Coordinate with consuming services**
4. **Test backward compatibility**
5. **Monitor migration progress**

### Review Process
All changes to event schemas and documentation require:
- Architecture team review
- Platform team approval
- QA validation
- Documentation updates

---
**Catalog Owner**: Architecture Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26  
**Version**: 1.0.0
