# Event Publisher Implementation Guide

> **Purpose**: Comprehensive guide for publishing events in VibeMatch  
> **Audience**: Backend developers implementing event publishers  
> **Version**: 1.0.0  
> **Last Updated**: 2025-06-26

## Overview

This guide provides practical implementation details for publishing events to Google Cloud Pub/Sub in VibeMatch's event-driven architecture. It includes setup instructions, code examples, best practices, and error handling patterns.

## Prerequisites

### Dependencies
```json
{
  "dependencies": {
    "@google-cloud/pubsub": "^4.0.0",
    "uuid": "^9.0.0",
    "joi": "^17.9.0"
  },
  "devDependencies": {
    "@types/uuid": "^9.0.0"
  }
}
```

### Environment Variables
```bash
# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT_ID=vibe-match-prod
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Service Configuration
SERVICE_NAME=user-management
SERVICE_VERSION=1.2.0
ENVIRONMENT=production

# Pub/Sub Configuration
PUBSUB_TOPIC_PREFIX=vibe-match
PUBSUB_ENABLE_ORDERING=true
PUBSUB_MAX_MESSAGES=1000
```

## Basic Setup

### 1. Initialize Pub/Sub Client
```typescript
import { PubSub } from '@google-cloud/pubsub';
import { v4 as uuidv4 } from 'uuid';

export class EventPublisher {
  private pubsub: PubSub;
  private projectId: string;
  private serviceName: string;
  private serviceVersion: string;

  constructor() {
    this.pubsub = new PubSub({
      projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
    });
    this.projectId = process.env.GOOGLE_CLOUD_PROJECT_ID!;
    this.serviceName = process.env.SERVICE_NAME!;
    this.serviceVersion = process.env.SERVICE_VERSION!;
  }

  async initialize(): Promise<void> {
    // Verify connection and create topics if needed
    await this.ensureTopicsExist();
  }

  private async ensureTopicsExist(): Promise<void> {
    const requiredTopics = this.getRequiredTopics();
    
    for (const topicName of requiredTopics) {
      try {
        const topic = this.pubsub.topic(topicName);
        const [exists] = await topic.exists();
        
        if (!exists) {
          await topic.create();
          console.log(`Created topic: ${topicName}`);
        }
      } catch (error) {
        console.error(`Failed to ensure topic exists: ${topicName}`, error);
        throw error;
      }
    }
  }

  private getRequiredTopics(): string[] {
    // Return topics this service publishes to
    return [
      'vibe-match-user-events',
      'vibe-match-task-events',
      'vibe-match-system-events'
    ];
  }
}
```

### 2. Event Base Structure
```typescript
interface BaseEvent {
  eventId: string;
  type: string;
  timestamp: string;
  version: string;
  source: {
    service: string;
    instance: string;
    version: string;
  };
  data: Record<string, any>;
  metadata?: {
    correlationId?: string;
    causationId?: string;
    userId?: string;
    traceId?: string;
  };
}

interface PublishOptions {
  orderingKey?: string;
  attributes?: Record<string, string>;
  correlationId?: string;
  causationId?: string;
  userId?: string;
}
```

## Publishing Events

### 1. Basic Event Publishing
```typescript
export class EventPublisher {
  async publish(
    eventType: string,
    data: Record<string, any>,
    options: PublishOptions = {}
  ): Promise<string> {
    const event = this.createEvent(eventType, data, options);
    
    // Validate event before publishing
    await this.validateEvent(event);
    
    // Determine topic name
    const topicName = this.getTopicName(eventType);
    
    // Publish to Pub/Sub
    const messageId = await this.publishToPubSub(topicName, event, options);
    
    // Log successful publication
    this.logEventPublished(event, messageId);
    
    return messageId;
  }

  private createEvent(
    eventType: string,
    data: Record<string, any>,
    options: PublishOptions
  ): BaseEvent {
    return {
      eventId: uuidv4(),
      type: eventType,
      timestamp: new Date().toISOString(),
      version: this.getEventVersion(eventType),
      source: {
        service: this.serviceName,
        instance: process.env.HOSTNAME || 'unknown',
        version: this.serviceVersion
      },
      data,
      metadata: {
        correlationId: options.correlationId || this.generateCorrelationId(),
        causationId: options.causationId,
        userId: options.userId,
        traceId: this.getCurrentTraceId()
      }
    };
  }

  private async publishToPubSub(
    topicName: string,
    event: BaseEvent,
    options: PublishOptions
  ): Promise<string> {
    const topic = this.pubsub.topic(topicName);
    const messageBuffer = Buffer.from(JSON.stringify(event));
    
    const publishOptions: any = {
      attributes: {
        eventType: event.type,
        version: event.version,
        source: event.source.service,
        ...options.attributes
      }
    };

    // Add ordering key if specified
    if (options.orderingKey) {
      publishOptions.orderingKey = options.orderingKey;
    }

    try {
      const messageId = await topic.publishMessage({
        data: messageBuffer,
        ...publishOptions
      });
      
      return messageId;
    } catch (error) {
      console.error('Failed to publish event:', error);
      throw new EventPublishError(`Failed to publish ${event.type}`, error);
    }
  }

  private getTopicName(eventType: string): string {
    const domain = eventType.split('.')[0];
    return `${process.env.PUBSUB_TOPIC_PREFIX}-${domain}-events`;
  }

  private getEventVersion(eventType: string): string {
    // Return appropriate version for event type
    const versionMap: Record<string, string> = {
      'user.created': '2.1.0',
      'user.updated': '2.0.0',
      'task.created': '1.2.0',
      'task.completed': '1.1.0',
      // Add more mappings as needed
    };
    
    return versionMap[eventType] || '1.0.0';
  }
}
```

### 2. Domain-Specific Publishers
```typescript
export class UserEventPublisher extends EventPublisher {
  async publishUserCreated(userData: {
    userId: string;
    email: string;
    displayName?: string;
    userType: 'customer' | 'agent_owner' | 'admin';
    registrationMethod?: string;
    referralCode?: string;
  }): Promise<string> {
    return this.publish('user.created', userData, {
      orderingKey: userData.userId, // Ensure user events are ordered
      userId: userData.userId
    });
  }

  async publishUserUpdated(
    userId: string,
    changes: Record<string, any>,
    previousValues?: Record<string, any>
  ): Promise<string> {
    return this.publish('user.updated', {
      userId,
      changes,
      previousValues
    }, {
      orderingKey: userId,
      userId
    });
  }

  async publishUserDeleted(
    userId: string,
    deletionReason: string
  ): Promise<string> {
    return this.publish('user.deleted', {
      userId,
      deletionReason,
      deletedAt: new Date().toISOString()
    }, {
      orderingKey: userId,
      userId
    });
  }
}

export class TaskEventPublisher extends EventPublisher {
  async publishTaskCreated(taskData: {
    taskId: string;
    userId: string;
    title: string;
    description: string;
    requirements: Record<string, any>;
    estimatedBudget: number;
    deadline?: string;
  }): Promise<string> {
    return this.publish('task.created', taskData, {
      orderingKey: taskData.taskId,
      userId: taskData.userId
    });
  }

  async publishTaskCompleted(completionData: {
    taskId: string;
    userId: string;
    agentId: string;
    completedAt: string;
    duration: number;
    deliverables: Array<{
      type: string;
      url: string;
      name: string;
    }>;
    actualCost: number;
  }): Promise<string> {
    return this.publish('task.completed', completionData, {
      orderingKey: completionData.taskId,
      userId: completionData.userId
    });
  }
}
```

## Advanced Features

### 1. Batch Publishing
```typescript
export class BatchEventPublisher extends EventPublisher {
  private batchQueue: Array<{
    eventType: string;
    data: Record<string, any>;
    options: PublishOptions;
  }> = [];
  
  private batchTimer?: NodeJS.Timeout;
  private readonly BATCH_SIZE = 100;
  private readonly BATCH_TIMEOUT = 5000; // 5 seconds

  async publishBatch(
    eventType: string,
    data: Record<string, any>,
    options: PublishOptions = {}
  ): Promise<void> {
    this.batchQueue.push({ eventType, data, options });
    
    if (this.batchQueue.length >= this.BATCH_SIZE) {
      await this.flushBatch();
    } else if (!this.batchTimer) {
      this.batchTimer = setTimeout(() => this.flushBatch(), this.BATCH_TIMEOUT);
    }
  }

  private async flushBatch(): Promise<void> {
    if (this.batchQueue.length === 0) return;
    
    const batch = this.batchQueue.splice(0);
    
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = undefined;
    }

    // Group by topic for efficient publishing
    const topicGroups = this.groupByTopic(batch);
    
    const publishPromises = Object.entries(topicGroups).map(
      ([topicName, events]) => this.publishTopicBatch(topicName, events)
    );

    await Promise.all(publishPromises);
  }

  private groupByTopic(
    batch: Array<{ eventType: string; data: Record<string, any>; options: PublishOptions }>
  ): Record<string, typeof batch> {
    return batch.reduce((groups, item) => {
      const topicName = this.getTopicName(item.eventType);
      if (!groups[topicName]) {
        groups[topicName] = [];
      }
      groups[topicName].push(item);
      return groups;
    }, {} as Record<string, typeof batch>);
  }

  private async publishTopicBatch(
    topicName: string,
    events: Array<{ eventType: string; data: Record<string, any>; options: PublishOptions }>
  ): Promise<void> {
    const topic = this.pubsub.topic(topicName);
    
    const messages = events.map(({ eventType, data, options }) => {
      const event = this.createEvent(eventType, data, options);
      return {
        data: Buffer.from(JSON.stringify(event)),
        attributes: {
          eventType: event.type,
          version: event.version,
          source: event.source.service
        },
        orderingKey: options.orderingKey
      };
    });

    try {
      await topic.publishMessage(messages);
      console.log(`Published batch of ${messages.length} events to ${topicName}`);
    } catch (error) {
      console.error(`Failed to publish batch to ${topicName}:`, error);
      throw error;
    }
  }
}
```

### 2. Event Validation
```typescript
import Joi from 'joi';

export class EventValidator {
  private schemas: Map<string, Joi.Schema> = new Map();

  constructor() {
    this.initializeSchemas();
  }

  private initializeSchemas(): void {
    // User event schemas
    this.schemas.set('user.created', Joi.object({
      userId: Joi.string().pattern(/^usr_[a-zA-Z0-9]{20}$/).required(),
      email: Joi.string().email().required(),
      displayName: Joi.string().optional(),
      userType: Joi.string().valid('customer', 'agent_owner', 'admin').required(),
      registrationMethod: Joi.string().optional(),
      referralCode: Joi.string().optional()
    }));

    this.schemas.set('user.updated', Joi.object({
      userId: Joi.string().pattern(/^usr_[a-zA-Z0-9]{20}$/).required(),
      changes: Joi.object().required(),
      previousValues: Joi.object().optional()
    }));

    // Task event schemas
    this.schemas.set('task.created', Joi.object({
      taskId: Joi.string().pattern(/^task_[a-zA-Z0-9]{16}$/).required(),
      userId: Joi.string().pattern(/^usr_[a-zA-Z0-9]{20}$/).required(),
      title: Joi.string().min(1).max(200).required(),
      description: Joi.string().min(1).max(5000).required(),
      requirements: Joi.object().required(),
      estimatedBudget: Joi.number().positive().required(),
      deadline: Joi.string().isoDate().optional()
    }));
  }

  async validate(eventType: string, data: Record<string, any>): Promise<void> {
    const schema = this.schemas.get(eventType);
    
    if (!schema) {
      console.warn(`No validation schema found for event type: ${eventType}`);
      return;
    }

    try {
      await schema.validateAsync(data);
    } catch (error) {
      throw new EventValidationError(`Validation failed for ${eventType}`, error);
    }
  }
}
```

## Error Handling

### 1. Custom Error Classes
```typescript
export class EventPublishError extends Error {
  constructor(message: string, public cause?: Error) {
    super(message);
    this.name = 'EventPublishError';
  }
}

export class EventValidationError extends Error {
  constructor(message: string, public validationError?: any) {
    super(message);
    this.name = 'EventValidationError';
  }
}
```

### 2. Retry Logic
```typescript
export class ResilientEventPublisher extends EventPublisher {
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY_MS = 1000;

  async publishWithRetry(
    eventType: string,
    data: Record<string, any>,
    options: PublishOptions = {}
  ): Promise<string> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        return await this.publish(eventType, data, options);
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === this.MAX_RETRIES) {
          break;
        }
        
        const delay = this.RETRY_DELAY_MS * Math.pow(2, attempt - 1);
        console.warn(`Publish attempt ${attempt} failed, retrying in ${delay}ms:`, error);
        
        await this.sleep(delay);
      }
    }
    
    throw new EventPublishError(
      `Failed to publish ${eventType} after ${this.MAX_RETRIES} attempts`,
      lastError!
    );
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

## Testing

### 1. Unit Tests
```typescript
import { EventPublisher } from './event-publisher';

describe('EventPublisher', () => {
  let publisher: EventPublisher;
  let mockPubSub: jest.Mocked<PubSub>;

  beforeEach(() => {
    mockPubSub = {
      topic: jest.fn().mockReturnValue({
        publishMessage: jest.fn().mockResolvedValue('message-id-123')
      })
    } as any;
    
    publisher = new EventPublisher();
    (publisher as any).pubsub = mockPubSub;
  });

  test('publishes event successfully', async () => {
    const messageId = await publisher.publish('user.created', {
      userId: 'usr_12345678901234567890',
      email: '<EMAIL>',
      userType: 'customer'
    });

    expect(messageId).toBe('message-id-123');
    expect(mockPubSub.topic).toHaveBeenCalledWith('vibe-match-user-events');
  });

  test('validates event data before publishing', async () => {
    await expect(
      publisher.publish('user.created', {
        userId: 'invalid-id',
        email: 'invalid-email'
      })
    ).rejects.toThrow(EventValidationError);
  });
});
```

### 2. Integration Tests
```typescript
describe('EventPublisher Integration', () => {
  let publisher: EventPublisher;

  beforeAll(async () => {
    publisher = new EventPublisher();
    await publisher.initialize();
  });

  test('publishes and receives event end-to-end', async () => {
    const testData = {
      userId: 'usr_12345678901234567890',
      email: '<EMAIL>',
      userType: 'customer' as const
    };

    const messageId = await publisher.publish('user.created', testData);
    expect(messageId).toBeTruthy();

    // Verify event was published (would need subscriber to verify)
  });
});
```

## Best Practices

### 1. Event Design
- **Include all necessary context** in event data
- **Use consistent field naming** across events
- **Avoid sensitive data** in event payloads
- **Design for idempotency** when possible

### 2. Performance
- **Use ordering keys** for related events
- **Batch events** when publishing many at once
- **Monitor topic throughput** and scale accordingly
- **Use appropriate message attributes** for filtering

### 3. Reliability
- **Implement retry logic** with exponential backoff
- **Validate events** before publishing
- **Monitor publishing success rates**
- **Handle partial failures** gracefully

### 4. Observability
- **Log all published events** with correlation IDs
- **Track publishing metrics** (success rate, latency)
- **Set up alerts** for publishing failures
- **Include tracing information** in events

---
**Guide Owner**: Platform Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26
