# Event Subscriber Implementation Guide

> **Purpose**: Comprehensive guide for consuming events in VibeMatch  
> **Audience**: Backend developers implementing event subscribers  
> **Version**: 1.0.0  
> **Last Updated**: 2025-06-26

## Overview

This guide provides practical implementation details for subscribing to events from Google Cloud Pub/Sub in VibeMatch's event-driven architecture. It covers setup, message handling, error recovery, and best practices.

## Prerequisites

### Dependencies
```json
{
  "dependencies": {
    "@google-cloud/pubsub": "^4.0.0",
    "uuid": "^9.0.0",
    "joi": "^17.9.0"
  },
  "devDependencies": {
    "@types/uuid": "^9.0.0"
  }
}
```

### Environment Variables
```bash
# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT_ID=vibe-match-prod
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Service Configuration
SERVICE_NAME=analytics-service
SERVICE_VERSION=1.1.0
ENVIRONMENT=production

# Subscription Configuration
PUBSUB_SUBSCRIPTION_PREFIX=vibe-match
PUBSUB_MAX_MESSAGES=100
PUBSUB_ACK_DEADLINE=60
PUBSUB_MESSAGE_RETENTION=604800  # 7 days
```

## Basic Setup

### 1. Initialize Subscriber
```typescript
import { PubSub, Subscription, Message } from '@google-cloud/pubsub';

export class EventSubscriber {
  private pubsub: PubSub;
  private subscriptions: Map<string, Subscription> = new Map();
  private handlers: Map<string, EventHandler[]> = new Map();
  private isRunning = false;

  constructor() {
    this.pubsub = new PubSub({
      projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
    });
  }

  async initialize(): Promise<void> {
    await this.createSubscriptions();
    await this.setupMessageHandlers();
  }

  async start(): Promise<void> {
    if (this.isRunning) return;
    
    this.isRunning = true;
    console.log('Starting event subscriber...');
    
    for (const [subscriptionName, subscription] of this.subscriptions) {
      this.startListening(subscriptionName, subscription);
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    console.log('Stopping event subscriber...');
    
    const closePromises = Array.from(this.subscriptions.values()).map(
      subscription => subscription.close()
    );
    
    await Promise.all(closePromises);
  }

  private async createSubscriptions(): Promise<void> {
    const subscriptionConfigs = this.getSubscriptionConfigs();
    
    for (const config of subscriptionConfigs) {
      await this.ensureSubscriptionExists(config);
    }
  }

  private getSubscriptionConfigs(): SubscriptionConfig[] {
    return [
      {
        topicName: 'vibe-match-user-events',
        subscriptionName: `${process.env.PUBSUB_SUBSCRIPTION_PREFIX}-user-events-${process.env.SERVICE_NAME}`,
        options: {
          ackDeadlineSeconds: 60,
          messageRetentionDuration: { seconds: 604800 },
          enableMessageOrdering: true
        }
      },
      {
        topicName: 'vibe-match-task-events',
        subscriptionName: `${process.env.PUBSUB_SUBSCRIPTION_PREFIX}-task-events-${process.env.SERVICE_NAME}`,
        options: {
          ackDeadlineSeconds: 60,
          messageRetentionDuration: { seconds: 604800 },
          enableMessageOrdering: true
        }
      }
    ];
  }

  private async ensureSubscriptionExists(config: SubscriptionConfig): Promise<void> {
    const topic = this.pubsub.topic(config.topicName);
    const subscription = topic.subscription(config.subscriptionName);
    
    const [exists] = await subscription.exists();
    
    if (!exists) {
      await subscription.create(config.options);
      console.log(`Created subscription: ${config.subscriptionName}`);
    }
    
    this.subscriptions.set(config.subscriptionName, subscription);
  }
}

interface SubscriptionConfig {
  topicName: string;
  subscriptionName: string;
  options: any;
}

interface EventHandler {
  eventType: string;
  handler: (event: any) => Promise<void>;
  options?: HandlerOptions;
}

interface HandlerOptions {
  retryAttempts?: number;
  retryDelay?: number;
  deadLetterTopic?: string;
}
```

### 2. Event Handler Registration
```typescript
export class EventSubscriber {
  registerHandler(
    eventType: string,
    handler: (event: any) => Promise<void>,
    options: HandlerOptions = {}
  ): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }
    
    this.handlers.get(eventType)!.push({
      eventType,
      handler,
      options
    });
  }

  private startListening(subscriptionName: string, subscription: Subscription): void {
    subscription.on('message', async (message: Message) => {
      await this.handleMessage(message);
    });

    subscription.on('error', (error: Error) => {
      console.error(`Subscription ${subscriptionName} error:`, error);
    });

    // Configure flow control
    subscription.setOptions({
      flowControlSettings: {
        maxMessages: parseInt(process.env.PUBSUB_MAX_MESSAGES || '100'),
        allowExcessMessages: false
      }
    });
  }

  private async handleMessage(message: Message): Promise<void> {
    try {
      const event = this.parseMessage(message);
      const handlers = this.handlers.get(event.type) || [];
      
      if (handlers.length === 0) {
        console.warn(`No handlers registered for event type: ${event.type}`);
        message.ack();
        return;
      }

      // Process all handlers for this event type
      await Promise.all(
        handlers.map(({ handler, options }) => 
          this.executeHandler(handler, event, message, options)
        )
      );

      message.ack();
    } catch (error) {
      console.error('Failed to handle message:', error);
      message.nack();
    }
  }

  private parseMessage(message: Message): any {
    try {
      const eventData = JSON.parse(message.data.toString());
      
      // Add message metadata
      eventData._metadata = {
        messageId: message.id,
        publishTime: message.publishTime,
        attributes: message.attributes,
        deliveryAttempt: message.deliveryAttempt
      };
      
      return eventData;
    } catch (error) {
      throw new Error(`Failed to parse message: ${error}`);
    }
  }

  private async executeHandler(
    handler: (event: any) => Promise<void>,
    event: any,
    message: Message,
    options: HandlerOptions = {}
  ): Promise<void> {
    const maxRetries = options.retryAttempts || 3;
    const retryDelay = options.retryDelay || 1000;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await handler(event);
        return; // Success
      } catch (error) {
        console.error(`Handler attempt ${attempt} failed:`, error);
        
        if (attempt === maxRetries) {
          // Send to dead letter queue if configured
          if (options.deadLetterTopic) {
            await this.sendToDeadLetter(event, options.deadLetterTopic, error);
          }
          throw error;
        }
        
        // Wait before retry
        await this.sleep(retryDelay * attempt);
      }
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async sendToDeadLetter(
    event: any,
    deadLetterTopic: string,
    error: Error
  ): Promise<void> {
    try {
      const topic = this.pubsub.topic(deadLetterTopic);
      const deadLetterEvent = {
        ...event,
        _deadLetter: {
          originalTopic: event._metadata?.attributes?.topic,
          error: error.message,
          timestamp: new Date().toISOString(),
          service: process.env.SERVICE_NAME
        }
      };
      
      await topic.publishMessage({
        data: Buffer.from(JSON.stringify(deadLetterEvent))
      });
    } catch (dlqError) {
      console.error('Failed to send to dead letter queue:', dlqError);
    }
  }
}
```

## Domain-Specific Subscribers

### 1. User Event Subscriber
```typescript
export class UserEventSubscriber extends EventSubscriber {
  constructor(
    private userService: UserService,
    private analyticsService: AnalyticsService,
    private notificationService: NotificationService
  ) {
    super();
    this.registerHandlers();
  }

  private registerHandlers(): void {
    this.registerHandler('user.created', this.handleUserCreated.bind(this));
    this.registerHandler('user.updated', this.handleUserUpdated.bind(this));
    this.registerHandler('user.deleted', this.handleUserDeleted.bind(this));
  }

  private async handleUserCreated(event: UserCreatedEvent): Promise<void> {
    console.log(`Processing user.created event for user: ${event.data.userId}`);
    
    // Update analytics
    await this.analyticsService.trackUserRegistration({
      userId: event.data.userId,
      userType: event.data.userType,
      registrationMethod: event.data.registrationMethod,
      timestamp: event.timestamp
    });

    // Send welcome notification
    await this.notificationService.sendWelcomeEmail({
      userId: event.data.userId,
      email: event.data.email,
      displayName: event.data.displayName
    });

    // Initialize user preferences
    await this.userService.initializeDefaultPreferences(event.data.userId);
  }

  private async handleUserUpdated(event: UserUpdatedEvent): Promise<void> {
    console.log(`Processing user.updated event for user: ${event.data.userId}`);
    
    // Update search index if profile fields changed
    const profileFields = ['displayName', 'bio', 'skills', 'location'];
    const hasProfileChanges = Object.keys(event.data.changes).some(
      field => profileFields.includes(field)
    );
    
    if (hasProfileChanges) {
      await this.userService.updateSearchIndex(
        event.data.userId,
        event.data.changes
      );
    }

    // Track profile completion
    await this.analyticsService.trackProfileUpdate({
      userId: event.data.userId,
      changedFields: Object.keys(event.data.changes),
      timestamp: event.timestamp
    });
  }

  private async handleUserDeleted(event: UserDeletedEvent): Promise<void> {
    console.log(`Processing user.deleted event for user: ${event.data.userId}`);
    
    // Clean up user data
    await this.userService.cleanupUserData(event.data.userId);
    
    // Update analytics
    await this.analyticsService.trackUserDeletion({
      userId: event.data.userId,
      deletionReason: event.data.deletionReason,
      timestamp: event.timestamp
    });

    // Cancel any pending notifications
    await this.notificationService.cancelUserNotifications(event.data.userId);
  }
}
```

### 2. Task Event Subscriber
```typescript
export class TaskEventSubscriber extends EventSubscriber {
  constructor(
    private taskService: TaskService,
    private matchingService: MatchingService,
    private creditService: CreditService,
    private billingService: BillingService
  ) {
    super();
    this.registerHandlers();
  }

  private registerHandlers(): void {
    this.registerHandler('task.created', this.handleTaskCreated.bind(this));
    this.registerHandler('task.completed', this.handleTaskCompleted.bind(this));
    this.registerHandler('match.accepted', this.handleMatchAccepted.bind(this));
  }

  private async handleTaskCreated(event: TaskCreatedEvent): Promise<void> {
    console.log(`Processing task.created event for task: ${event.data.taskId}`);
    
    // Validate user has sufficient credits
    const userBalance = await this.creditService.getUserBalance(event.data.userId);
    if (userBalance < event.data.estimatedBudget) {
      throw new Error(`Insufficient credits for task ${event.data.taskId}`);
    }

    // Start matching process
    await this.matchingService.findMatches({
      taskId: event.data.taskId,
      requirements: event.data.requirements,
      budget: event.data.estimatedBudget,
      deadline: event.data.deadline
    });
  }

  private async handleTaskCompleted(event: TaskCompletedEvent): Promise<void> {
    console.log(`Processing task.completed event for task: ${event.data.taskId}`);
    
    // Process final billing
    await this.billingService.processTaskCompletion({
      taskId: event.data.taskId,
      userId: event.data.userId,
      agentId: event.data.agentId,
      actualCost: event.data.actualCost,
      duration: event.data.duration
    });

    // Update task metrics
    await this.taskService.updateCompletionMetrics({
      taskId: event.data.taskId,
      completionTime: event.data.duration,
      quality: event.data.quality
    });
  }

  private async handleMatchAccepted(event: MatchAcceptedEvent): Promise<void> {
    console.log(`Processing match.accepted event for task: ${event.data.taskId}`);
    
    // Reserve credits for the task
    await this.creditService.reserveCredits({
      userId: event.data.userId,
      taskId: event.data.taskId,
      agentId: event.data.selectedAgent.agentId,
      amount: event.data.selectedAgent.estimatedCost
    });

    // Notify agent of assignment
    await this.taskService.assignTaskToAgent({
      taskId: event.data.taskId,
      agentId: event.data.selectedAgent.agentId,
      estimatedCost: event.data.selectedAgent.estimatedCost
    });
  }
}
```

## Advanced Features

### 1. Message Filtering
```typescript
export class FilteredEventSubscriber extends EventSubscriber {
  registerFilteredHandler(
    eventType: string,
    filter: (event: any) => boolean,
    handler: (event: any) => Promise<void>,
    options: HandlerOptions = {}
  ): void {
    const wrappedHandler = async (event: any) => {
      if (filter(event)) {
        await handler(event);
      }
    };
    
    this.registerHandler(eventType, wrappedHandler, options);
  }
}

// Usage example
const subscriber = new FilteredEventSubscriber();

// Only handle user creation events for premium users
subscriber.registerFilteredHandler(
  'user.created',
  (event) => event.data.userType === 'premium',
  async (event) => {
    await setupPremiumFeatures(event.data.userId);
  }
);

// Only handle high-value task completions
subscriber.registerFilteredHandler(
  'task.completed',
  (event) => event.data.actualCost > 500,
  async (event) => {
    await sendHighValueCompletionAlert(event);
  }
);
```

### 2. Event Aggregation
```typescript
export class AggregatingEventSubscriber extends EventSubscriber {
  private aggregationWindows: Map<string, EventWindow> = new Map();

  registerAggregatedHandler(
    eventType: string,
    windowSize: number, // milliseconds
    handler: (events: any[]) => Promise<void>
  ): void {
    this.registerHandler(eventType, async (event) => {
      await this.addToWindow(eventType, event, windowSize, handler);
    });
  }

  private async addToWindow(
    eventType: string,
    event: any,
    windowSize: number,
    handler: (events: any[]) => Promise<void>
  ): Promise<void> {
    const windowKey = `${eventType}-${Math.floor(Date.now() / windowSize)}`;
    
    if (!this.aggregationWindows.has(windowKey)) {
      this.aggregationWindows.set(windowKey, {
        events: [],
        timer: setTimeout(async () => {
          const window = this.aggregationWindows.get(windowKey);
          if (window) {
            await handler(window.events);
            this.aggregationWindows.delete(windowKey);
          }
        }, windowSize)
      });
    }
    
    this.aggregationWindows.get(windowKey)!.events.push(event);
  }
}

interface EventWindow {
  events: any[];
  timer: NodeJS.Timeout;
}

// Usage example
const subscriber = new AggregatingEventSubscriber();

// Aggregate user registrations every 5 minutes
subscriber.registerAggregatedHandler(
  'user.created',
  5 * 60 * 1000, // 5 minutes
  async (events) => {
    const registrationCount = events.length;
    const userTypes = events.map(e => e.data.userType);
    
    await analyticsService.recordRegistrationBatch({
      count: registrationCount,
      userTypes,
      timestamp: new Date()
    });
  }
);
```

## Error Handling and Resilience

### 1. Circuit Breaker Pattern
```typescript
export class CircuitBreakerEventSubscriber extends EventSubscriber {
  private circuitBreakers: Map<string, CircuitBreaker> = new Map();

  registerHandlerWithCircuitBreaker(
    eventType: string,
    handler: (event: any) => Promise<void>,
    options: CircuitBreakerOptions = {}
  ): void {
    const breaker = new CircuitBreaker(handler, {
      timeout: options.timeout || 30000,
      errorThresholdPercentage: options.errorThreshold || 50,
      resetTimeout: options.resetTimeout || 60000
    });

    this.circuitBreakers.set(eventType, breaker);
    this.registerHandler(eventType, breaker.fire.bind(breaker));
  }
}

interface CircuitBreakerOptions {
  timeout?: number;
  errorThreshold?: number;
  resetTimeout?: number;
}

class CircuitBreaker {
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private failures = 0;
  private lastFailureTime = 0;
  private successCount = 0;

  constructor(
    private handler: (event: any) => Promise<void>,
    private options: Required<CircuitBreakerOptions>
  ) {}

  async fire(event: any): Promise<void> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.options.resetTimeout) {
        this.state = 'HALF_OPEN';
        this.successCount = 0;
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await Promise.race([
        this.handler(event),
        this.timeout()
      ]);

      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private async timeout(): Promise<never> {
    await new Promise(resolve => setTimeout(resolve, this.options.timeout));
    throw new Error('Handler timeout');
  }

  private onSuccess(): void {
    this.failures = 0;
    
    if (this.state === 'HALF_OPEN') {
      this.successCount++;
      if (this.successCount >= 3) {
        this.state = 'CLOSED';
      }
    }
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    const failureRate = this.failures / (this.failures + this.successCount);
    if (failureRate >= this.options.errorThreshold / 100) {
      this.state = 'OPEN';
    }
  }
}
```

## Testing

### 1. Unit Tests
```typescript
import { EventSubscriber } from './event-subscriber';

describe('EventSubscriber', () => {
  let subscriber: EventSubscriber;
  let mockHandler: jest.Mock;

  beforeEach(() => {
    subscriber = new EventSubscriber();
    mockHandler = jest.fn().mockResolvedValue(undefined);
  });

  test('registers and executes handler', async () => {
    subscriber.registerHandler('user.created', mockHandler);
    
    const mockMessage = {
      data: Buffer.from(JSON.stringify({
        type: 'user.created',
        data: { userId: 'usr_123', email: '<EMAIL>' }
      })),
      ack: jest.fn(),
      nack: jest.fn()
    };

    await (subscriber as any).handleMessage(mockMessage);

    expect(mockHandler).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'user.created',
        data: { userId: 'usr_123', email: '<EMAIL>' }
      })
    );
    expect(mockMessage.ack).toHaveBeenCalled();
  });

  test('handles handler errors with retry', async () => {
    mockHandler
      .mockRejectedValueOnce(new Error('First attempt'))
      .mockRejectedValueOnce(new Error('Second attempt'))
      .mockResolvedValueOnce(undefined);

    subscriber.registerHandler('user.created', mockHandler, {
      retryAttempts: 3,
      retryDelay: 100
    });

    const mockMessage = {
      data: Buffer.from(JSON.stringify({
        type: 'user.created',
        data: { userId: 'usr_123' }
      })),
      ack: jest.fn(),
      nack: jest.fn()
    };

    await (subscriber as any).handleMessage(mockMessage);

    expect(mockHandler).toHaveBeenCalledTimes(3);
    expect(mockMessage.ack).toHaveBeenCalled();
  });
});
```

## Best Practices

### 1. Handler Design
- **Keep handlers idempotent** - safe to execute multiple times
- **Handle duplicate events** gracefully
- **Validate event structure** before processing
- **Use correlation IDs** for tracing

### 2. Performance
- **Process events asynchronously** when possible
- **Use appropriate batch sizes** for throughput
- **Monitor processing latency** and adjust accordingly
- **Scale subscribers** based on message volume

### 3. Reliability
- **Implement proper error handling** with retries
- **Use dead letter queues** for failed messages
- **Monitor subscription health** and lag
- **Handle service restarts** gracefully

### 4. Observability
- **Log all processed events** with correlation IDs
- **Track processing metrics** (latency, success rate)
- **Set up alerts** for processing failures
- **Include distributed tracing** information

---
**Guide Owner**: Platform Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26
