# Event Naming Conventions

> **Purpose**: Standardized naming conventions for all events in VibeMatch  
> **Audience**: All developers, architects, QA engineers  
> **Version**: 1.0.0  
> **Last Updated**: 2025-06-26

## Overview

Consistent event naming is crucial for maintainability, discoverability, and integration across VibeMatch's event-driven architecture. This document establishes the official naming standards that all services must follow.

## Core Naming Format

### Standard Pattern
```
<domain>.<action>
```

### Components
- **Domain**: Business domain or entity (singular noun)
- **Action**: What happened (past tense verb)

### Examples
- `user.created`
- `task.completed`
- `credit.deducted`
- `match.found`

## Domain Categories

### 1. Core Business Domains
| Domain | Description | Examples |
|--------|-------------|----------|
| `user` | User account and profile | `user.created`, `user.updated`, `user.deleted` |
| `agent` | AI agent management | `agent.created`, `agent.approved`, `agent.rated` |
| `task` | Task lifecycle | `task.created`, `task.assigned`, `task.completed` |
| `match` | Agent-task matching | `match.found`, `match.accepted`, `match.rejected` |
| `credit` | Credit transactions | `credit.added`, `credit.deducted`, `credit.reserved` |
| `billing` | Payment processing | `billing.payment_processed`, `billing.invoice_generated` |

### 2. System Domains
| Domain | Description | Examples |
|--------|-------------|----------|
| `system` | Platform operations | `system.health_check`, `system.error`, `system.alert` |
| `security` | Security events | `security.login_failed`, `security.token_expired` |
| `audit` | Compliance tracking | `audit.action_logged`, `audit.report_generated` |

## Action Naming Rules

### 1. Use Past Tense
Events represent something that has already happened.

✅ **Correct**:
- `user.created`
- `task.completed`
- `payment.processed`

❌ **Incorrect**:
- `user.create`
- `task.complete`
- `payment.process`

### 2. Be Specific and Clear
Use descriptive actions that clearly indicate what occurred.

✅ **Correct**:
- `user.email_verified`
- `agent.performance_updated`
- `task.deadline_extended`

❌ **Incorrect**:
- `user.changed`
- `agent.modified`
- `task.updated`

### 3. Use Underscores for Multi-Word Actions
When actions require multiple words, separate them with underscores.

✅ **Correct**:
- `billing.payment_failed`
- `agent.status_changed`
- `task.deadline_extended`

❌ **Incorrect**:
- `billing.paymentFailed`
- `agent.statusChanged`
- `task.deadlineExtended`

## Naming Guidelines

### 1. Consistency Rules
- Always use lowercase letters
- Use singular nouns for domains
- Use past tense for actions
- Separate words with underscores, not hyphens or camelCase
- Keep names concise but descriptive

### 2. Domain-Specific Patterns

#### User Events
```
user.created          # New user registration
user.updated          # Profile information changed
user.deleted          # Account permanently removed
user.suspended        # Account temporarily disabled
user.reactivated      # Suspended account restored
user.email_verified   # Email verification completed
user.password_changed # Password updated
user.preferences_updated # User settings modified
```

#### Task Events
```
task.created          # New task submitted
task.assigned         # Agent assigned to task
task.started          # Agent begins work
task.progress_updated # Milestone or progress report
task.completed        # Task finished successfully
task.cancelled        # Task cancelled by user
task.failed           # Task execution failed
task.disputed         # Task result disputed
task.rated            # User rated task completion
```

#### Credit Events
```
credit.added          # Credits added to account
credit.deducted       # Credits removed from account
credit.reserved       # Credits held for pending task
credit.released       # Reserved credits returned
credit.expired        # Credits reached expiration
credit.transferred    # Credits moved between accounts
credit.balance_low    # Balance below threshold
```

#### Matching Events
```
match.found           # Suitable agents identified
match.accepted        # User selected an agent
match.rejected        # User rejected all matches
match.expired         # Match offer timed out
match.algorithm_updated # Matching logic improved
```

### 3. Hierarchical Events
For complex domains, use dot notation to create hierarchies:

```
billing.payment.initiated     # Payment process started
billing.payment.processed     # Payment completed successfully
billing.payment.failed        # Payment processing failed
billing.payment.refunded      # Payment reversed

billing.invoice.generated     # Invoice created
billing.invoice.sent          # Invoice delivered to user
billing.invoice.paid          # Invoice payment received
billing.invoice.overdue       # Invoice payment late
```

### 4. State Change Events
For events that represent state transitions, include the new state:

```
agent.status_changed          # Generic status change
agent.approved               # Specific: status changed to approved
agent.suspended              # Specific: status changed to suspended
agent.reactivated            # Specific: status changed to active

task.status_changed          # Generic status change
task.assigned                # Specific: status changed to assigned
task.completed               # Specific: status changed to completed
task.cancelled               # Specific: status changed to cancelled
```

## Anti-Patterns to Avoid

### 1. Generic Names
❌ **Avoid**:
- `data.changed`
- `object.updated`
- `item.modified`
- `event.triggered`

✅ **Use Instead**:
- `user.profile_updated`
- `task.requirements_changed`
- `agent.capabilities_modified`
- `match.criteria_updated`

### 2. Implementation Details
❌ **Avoid**:
- `database.record_inserted`
- `cache.entry_updated`
- `queue.message_processed`

✅ **Use Instead**:
- `user.created`
- `task.updated`
- `notification.sent`

### 3. Technical Jargon
❌ **Avoid**:
- `user.crud_operation`
- `task.state_mutation`
- `agent.entity_persisted`

✅ **Use Instead**:
- `user.updated`
- `task.status_changed`
- `agent.created`

### 4. Ambiguous Actions
❌ **Avoid**:
- `user.processed`
- `task.handled`
- `agent.managed`

✅ **Use Instead**:
- `user.verified`
- `task.completed`
- `agent.approved`

## Event Versioning in Names

### Version-Agnostic Names
Event names should not include version numbers:

✅ **Correct**:
- `user.created`
- `task.completed`

❌ **Incorrect**:
- `user.created.v1`
- `task.completed.v2`

Version information belongs in the event schema, not the name.

## Reserved Keywords

### System Reserved
These prefixes are reserved for system-level events:
- `system.*`
- `platform.*`
- `infrastructure.*`
- `monitoring.*`

### Integration Reserved
These prefixes are reserved for external integrations:
- `stripe.*`
- `sendgrid.*`
- `analytics.*`
- `external.*`

## Validation Rules

### Automated Validation
All event names must pass these validation rules:

```typescript
const EVENT_NAME_PATTERN = /^[a-z]+\.[a-z_]+$/;

function validateEventName(eventName: string): boolean {
  // Must match pattern
  if (!EVENT_NAME_PATTERN.test(eventName)) {
    return false;
  }
  
  // Must have exactly one dot
  if (eventName.split('.').length !== 2) {
    return false;
  }
  
  // Domain and action must not be empty
  const [domain, action] = eventName.split('.');
  if (!domain || !action) {
    return false;
  }
  
  // Must not use reserved keywords
  const reservedPrefixes = ['system', 'platform', 'infrastructure'];
  if (reservedPrefixes.includes(domain)) {
    return false; // Unless authorized
  }
  
  return true;
}
```

### Manual Review Required
These event names require architecture team approval:
- New domain introductions
- Cross-domain events
- Breaking changes to existing names
- Events with high frequency (>1000/day)

## Migration Guidelines

### Renaming Existing Events
When renaming events:

1. **Deprecation Period**: Support both old and new names for 3 months
2. **Documentation**: Update all documentation immediately
3. **Consumer Migration**: Coordinate with all consuming services
4. **Monitoring**: Track usage of deprecated names
5. **Removal**: Remove deprecated names after migration period

### Example Migration
```typescript
// Phase 1: Support both names
eventBus.publish('user.profile_changed', data); // Old name (deprecated)
eventBus.publish('user.profile_updated', data); // New name

// Phase 2: Migrate consumers
eventBus.subscribe('user.profile_updated', handler); // New name only

// Phase 3: Remove old name
// eventBus.publish('user.profile_changed', data); // Removed
```

## Tooling and Enforcement

### IDE Integration
- Event name autocomplete
- Real-time validation
- Deprecated name warnings

### CI/CD Pipeline
- Event name validation in pull requests
- Breaking change detection
- Documentation generation

### Runtime Monitoring
- Event name usage tracking
- Deprecated name alerts
- Naming convention violations

## Best Practices Summary

1. **Be Consistent**: Follow the established patterns
2. **Be Descriptive**: Names should clearly indicate what happened
3. **Be Concise**: Avoid unnecessarily long names
4. **Be Future-Proof**: Consider how names might evolve
5. **Be Team-Friendly**: Use names that are easy to understand and remember

## Examples by Service

### User Management Service
```
user.created
user.updated
user.deleted
user.suspended
user.reactivated
user.email_verified
user.password_changed
user.preferences_updated
```

### Task Management Service
```
task.created
task.assigned
task.started
task.progress_updated
task.completed
task.cancelled
task.failed
task.disputed
task.rated
```

### Matching Engine Service
```
match.found
match.accepted
match.rejected
match.expired
match.algorithm_updated
```

### Credit System Service
```
credit.added
credit.deducted
credit.reserved
credit.released
credit.expired
credit.transferred
credit.balance_low
```

---
**Document Owner**: Architecture Team  
**Review Cycle**: Quarterly  
**Next Review**: 2025-09-26
