# Event Catalog

> **Purpose**: Complete catalog of all system events in VibeMatch  
> **Audience**: All developers, architects, QA engineers  
> **Version**: 1.0.0  
> **Last Updated**: 2025-06-26

## Overview
This catalog provides a comprehensive reference of all events published and consumed within the VibeMatch platform's event-driven architecture. Each event is documented with its purpose, payload structure, producers, consumers, and usage patterns.

## Table of Contents
1. [Event Standards](#event-standards)
2. [Event Categories](#event-categories)
   - [User Events](#user-events)
   - [Agent Events](#agent-events)
   - [Task Events](#task-events)
   - [Billing Events](#billing-events)
   - [System Events](#system-events)
3. [Event Flow Diagrams](#event-flow-diagrams)
4. [Implementation Guide](#implementation-guide)
5. [Event Versioning Strategy](#event-versioning-strategy)

## Event Standards

### Naming Convention
- Format: `domain.action`
- Domain: singular noun (user, agent, task, billing, system)
- Action: past tense verb (created, updated, completed)
- Examples: `user.created`, `task.completed`, `billing.credits_purchased`

### Base Event Structure
```json
{
  "eventId": "evt_1234567890abcdef",
  "type": "domain.action",
  "timestamp": "2025-06-26T10:30:00Z",
  "version": "1.0.0",
  "source": {
    "service": "service-name",
    "instance": "instance-id"
  },
  "data": {
    // Event-specific payload
  },
  "metadata": {
    "correlationId": "corr_1234567890",
    "causationId": "evt_0987654321",
    "userId": "usr_1234567890"
  }
}
```

### Required Fields
- `eventId`: Unique event identifier (UUID v4)
- `type`: Event name following naming convention
- `timestamp`: ISO 8601 UTC timestamp
- `version`: Semantic version of event schema
- `source`: Service that produced the event
- `data`: Event-specific payload

### Optional Fields
- `metadata.correlationId`: Links related events
- `metadata.causationId`: Previous event that caused this one
- `metadata.userId`: User associated with the event

## Event Categories

### User Events
Events related to user lifecycle and activities.

#### user.created

**Producer**: User Management Service  
**Consumers**: Analytics Service, Billing Service, Notification Service  
**Trigger**: When a new user completes registration  
**Frequency**: ~100/day (estimated)

**Payload**:
```json
{
  "eventId": "evt_1234567890abcdef",
  "type": "user.created",
  "timestamp": "2025-06-26T10:30:00Z",
  "version": "1.0.0",
  "data": {
    "userId": "usr_1234567890abcdef12",
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "role": "user",
    "registrationMethod": "google",
    "referralCode": "FRIEND123"
  }
}
```

**Schema**: [/schemas/user-events.json#user.created](./schemas/user-events.json)  
**Since**: v1.0.0

#### user.updated

**Producer**: User Management Service  
**Consumers**: Analytics Service, Cache Service  
**Trigger**: When user profile information is modified  
**Frequency**: ~500/day

**Payload**:
```json
{
  "eventId": "evt_2345678901bcdef0",
  "type": "user.updated",
  "timestamp": "2025-06-26T11:00:00Z",
  "version": "1.0.0",
  "data": {
    "userId": "usr_1234567890abcdef12",
    "changes": {
      "displayName": "Jane Doe",
      "preferences": {
        "notifications": "email",
        "language": "es"
      }
    },
    "previousValues": {
      "displayName": "John Doe",
      "preferences": {
        "notifications": "push",
        "language": "en"
      }
    }
  }
}
```

**Schema**: [/schemas/user-events.json#user.updated](./schemas/user-events.json)  
**Since**: v1.0.0

#### user.deleted

**Producer**: User Management Service  
**Consumers**: All Services  
**Trigger**: When a user account is permanently deleted  
**Frequency**: ~5/day

**Payload**:
```json
{
  "eventId": "evt_3456789012cdef01",
  "type": "user.deleted",
  "timestamp": "2025-06-26T12:00:00Z",
  "version": "1.0.0",
  "data": {
    "userId": "usr_1234567890abcdef12",
    "reason": "user_request",
    "deletedBy": "usr_1234567890abcdef12",
    "dataRetentionDays": 30
  }
}
```

**Schema**: [/schemas/user-events.json#user.deleted](./schemas/user-events.json)  
**Since**: v1.0.0

#### user.authenticated

**Producer**: Authentication Service (API Gateway)  
**Consumers**: Analytics Service, Security Service  
**Trigger**: When a user successfully authenticates  
**Frequency**: ~2000/day

**Payload**:
```json
{
  "eventId": "evt_4567890123def012",
  "type": "user.authenticated",
  "timestamp": "2025-06-26T09:00:00Z",
  "version": "1.0.0",
  "data": {
    "userId": "usr_1234567890abcdef12",
    "method": "google",
    "sessionId": "sess_1234567890abcdef",
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0...",
    "mfaUsed": false
  }
}
```

**Schema**: [/schemas/user-events.json#user.authenticated](./schemas/user-events.json)  
**Since**: v1.0.0

| Event | Description | Publishers | Consumers |
|-------|-------------|------------|-----------|
| `user.created` | New user registration | User Management | Analytics, Billing, Notification |
| `user.updated` | User profile modification | User Management | Analytics, Cache |
| `user.deleted` | User account deletion | User Management | All services |
| `user.authenticated` | User authentication success | API Gateway | Analytics, Security |
| `user.logout` | User session termination | API Gateway | Analytics |
| `user.suspended` | Account suspension | Admin Platform | All services |
| `user.reactivated` | Account reactivation | Admin Platform | All services |
| `user.password_changed` | Password update | User Management | Security, Notification |
| `user.email_verified` | Email verification complete | User Management | Notification |
| `user.preferences_updated` | User preferences changed | User Management | Notification, UI |

### Agent Events
Events for agent operations and state management.

#### agent.created

**Producer**: Agent Management Service  
**Consumers**: Marketplace Service, Analytics Service  
**Trigger**: When a new AI agent is registered in the system  
**Frequency**: ~20/day

**Payload**:
```json
{
  "eventId": "evt_5678901234ef0123",
  "type": "agent.created",
  "timestamp": "2025-06-26T14:00:00Z",
  "version": "1.0.0",
  "data": {
    "agentId": "agt_1234567890abcdef12",
    "userId": "usr_0987654321fedcba09",
    "name": "CodeAssist Pro",
    "description": "Expert coding assistant",
    "capabilities": ["code_assistance", "debugging", "refactoring"],
    "hourlyRate": 50,
    "model": "gpt-4",
    "status": "pending_review"
  }
}
```

**Schema**: [/schemas/agent-events.json#agent.created](./schemas/agent-events.json)  
**Since**: v1.0.0

#### agent.approved

**Producer**: Admin Platform Service  
**Consumers**: Marketplace Service, Agent Management Service  
**Trigger**: When an agent passes review and is approved for marketplace  
**Frequency**: ~15/day

**Payload**:
```json
{
  "eventId": "evt_6789012345f01234",
  "type": "agent.approved",
  "timestamp": "2025-06-26T15:00:00Z",
  "version": "1.0.0",
  "data": {
    "agentId": "agt_1234567890abcdef12",
    "approvedBy": "usr_admin123456789012",
    "verificationLevel": "professional",
    "certifications": ["quality_assured", "security_verified"],
    "notes": "Passed all quality checks"
  }
}
```

**Schema**: [/schemas/agent-events.json#agent.approved](./schemas/agent-events.json)  
**Since**: v1.0.0

#### agent.rejected

**Producer**: Admin Platform Service  
**Consumers**: Agent Management Service, Notification Service  
**Trigger**: When an agent fails review  
**Frequency**: ~5/day

**Payload**:
```json
{
  "eventId": "evt_7890123456012345",
  "type": "agent.rejected",
  "timestamp": "2025-06-26T15:30:00Z",
  "version": "1.0.0",
  "data": {
    "agentId": "agt_1234567890abcdef12",
    "rejectedBy": "usr_admin123456789012",
    "reasons": [
      "Incomplete documentation",
      "Failed security audit"
    ],
    "canResubmit": true,
    "resubmitAfter": "2025-06-27T00:00:00Z"
  }
}
```

**Schema**: [/schemas/agent-events.json#agent.rejected](./schemas/agent-events.json)  
**Since**: v1.0.0

#### agent.rated

**Producer**: Task Management Service  
**Consumers**: Agent Management Service, Analytics Service  
**Trigger**: When a user rates an agent after task completion  
**Frequency**: ~200/day

**Payload**:
```json
{
  "eventId": "evt_8901234567123456",
  "type": "agent.rated",
  "timestamp": "2025-06-26T16:00:00Z",
  "version": "1.0.0",
  "data": {
    "agentId": "agt_1234567890abcdef12",
    "taskId": "tsk_1234567890abcdef12",
    "userId": "usr_1234567890abcdef12",
    "rating": 4.5,
    "feedback": "Great work, very responsive",
    "categories": {
      "quality": 5,
      "speed": 4,
      "communication": 4
    }
  }
}
```

**Schema**: [/schemas/agent-events.json#agent.rated](./schemas/agent-events.json)  
**Since**: v1.0.0

| Event | Description | Publishers | Consumers |
|-------|-------------|------------|-----------|
| `agent.created` | New agent registration | Agent Management | Marketplace, Analytics |
| `agent.updated` | Agent profile changes | Agent Management | Marketplace, Cache |
| `agent.approved` | Agent approved for marketplace | Admin Platform | Marketplace, Agent Management |
| `agent.rejected` | Agent review failed | Admin Platform | Agent Management, Notification |
| `agent.matched` | Agent matched to task | Matching Engine | Task Management, Notification |
| `agent.task_started` | Agent begins task | Task Management | Analytics, Billing |
| `agent.task_completed` | Agent completes task | Task Management | Billing, Rating, Analytics |
| `agent.performance_updated` | Performance metrics update | Analytics | Marketplace, Admin |
| `agent.verified` | Agent verification status | Admin Platform | Marketplace |
| `agent.rated` | User rates agent performance | Task Management | Agent Management, Analytics |
| `agent.suspended` | Agent temporarily suspended | Admin Platform | Marketplace, Task Management |
| `agent.reactivated` | Agent suspension lifted | Admin Platform | Marketplace, Task Management |

### Task Events
Task lifecycle and orchestration events.

#### task.created

**Producer**: Task Management Service  
**Consumers**: Matching Engine Service, Analytics Service  
**Trigger**: When a user submits a new task request  
**Frequency**: ~300/day

**Payload**:
```json
{
  "eventId": "evt_9012345678234567",
  "type": "task.created",
  "timestamp": "2025-06-26T17:00:00Z",
  "version": "1.0.0",
  "data": {
    "taskId": "tsk_1234567890abcdef12",
    "userId": "usr_1234567890abcdef12",
    "title": "Create marketing content",
    "description": "Need 5 social media posts",
    "type": "single_agent",
    "requirements": [
      "Instagram-ready images",
      "Engaging captions",
      "Hashtag research"
    ],
    "budget": 150,
    "deadline": "2025-06-28T00:00:00Z",
    "priority": "high"
  }
}
```

**Schema**: [/schemas/task-events.json#task.created](./schemas/task-events.json)  
**Since**: v1.0.0

#### task.accepted

**Producer**: Task Management Service  
**Consumers**: Billing Service, Notification Service  
**Trigger**: When an agent accepts a matched task  
**Frequency**: ~250/day

**Payload**:
```json
{
  "eventId": "evt_0123456789345678",
  "type": "task.accepted",
  "timestamp": "2025-06-26T17:15:00Z",
  "version": "1.0.0",
  "data": {
    "taskId": "tsk_1234567890abcdef12",
    "agentId": "agt_1234567890abcdef12",
    "acceptedAt": "2025-06-26T17:15:00Z",
    "estimatedDuration": 120,
    "agreedPrice": 150
  }
}
```

**Schema**: [/schemas/task-events.json#task.accepted](./schemas/task-events.json)  
**Since**: v1.0.0

| Event | Description | Publishers | Consumers |
|-------|-------------|------------|-----------|
| `task.created` | New task submission | Task Management | Matching Engine |
| `task.matched` | Agents matched to task | Matching Engine | Task Management, Notification |
| `task.accepted` | Agent accepts task | Task Management | Billing, Notification |
| `task.started` | Task execution begins | Task Management | Billing, Analytics |
| `task.progress_updated` | Task progress milestone | Task Management | Notification, UI |
| `task.completed` | Task successfully finished | Task Management | Billing, Rating, Analytics |
| `task.failed` | Task execution failed | Task Management | Billing, Notification |
| `task.cancelled` | Task cancelled | Task Management | Billing, Notification |
| `task.expired` | Task deadline passed | Task Management | Notification, Analytics |
| `task.disputed` | Task result disputed | Task Management | Admin Platform, Notification |
| `task.orchestration_started` | Multi-agent task begins | Orchestration Engine | Task Management, Analytics |
| `task.orchestration_step` | Multi-agent step update | Orchestration Engine | Task Management, UI |
| `task.orchestration_completed` | Multi-agent task finished | Orchestration Engine | Billing, Analytics |

### Billing Events
Financial transactions and credit management.

#### billing.credits_purchased

**Producer**: Billing Service  
**Consumers**: Analytics Service, Notification Service, Credit System Service  
**Trigger**: When a user successfully purchases credits  
**Frequency**: ~150/day

**Payload**:
```json
{
  "eventId": "evt_1234567890456789",
  "type": "billing.credits_purchased",
  "timestamp": "2025-06-26T18:00:00Z",
  "version": "1.0.0",
  "data": {
    "transactionId": "txn_1234567890abcdef12",
    "userId": "usr_1234567890abcdef12",
    "amount": 99.99,
    "currency": "USD",
    "credits": 1000,
    "paymentMethod": "card",
    "cardLast4": "4242",
    "promoCode": "WELCOME20",
    "discount": 20.00,
    "newBalance": 1500
  }
}
```

**Schema**: [/schemas/billing-events.json#billing.credits_purchased](./schemas/billing-events.json)  
**Since**: v1.0.0

#### billing.invoice_generated

**Producer**: Billing Service  
**Consumers**: Notification Service, Admin Platform  
**Trigger**: When an invoice is generated for a transaction  
**Frequency**: ~200/day

**Payload**:
```json
{
  "eventId": "evt_2345678901567890",
  "type": "billing.invoice_generated",
  "timestamp": "2025-06-26T18:30:00Z",
  "version": "1.0.0",
  "data": {
    "invoiceId": "inv_1234567890abcdef12",
    "userId": "usr_1234567890abcdef12",
    "transactionId": "txn_1234567890abcdef12",
    "amount": 99.99,
    "currency": "USD",
    "dueDate": "2025-07-26T00:00:00Z",
    "items": [
      {
        "description": "1000 Credits Package",
        "quantity": 1,
        "unitPrice": 119.99,
        "discount": 20.00,
        "total": 99.99
      }
    ],
    "pdfUrl": "https://invoices.vibe-match.com/inv_1234567890abcdef12.pdf"
  }
}
```

**Schema**: [/schemas/billing-events.json#billing.invoice_generated](./schemas/billing-events.json)  
**Since**: v1.0.0

| Event | Description | Publishers | Consumers |
|-------|-------------|------------|-----------|
| `billing.credits_purchased` | User buys credits | Billing Service | Analytics, Notification, Credit System |
| `billing.credits_deducted` | Credits used for task | Credit System | Analytics, Billing |
| `billing.credits_refunded` | Credits returned | Billing Service | Analytics, Notification, Credit System |
| `billing.invoice_generated` | Invoice created | Billing Service | Notification, Admin |
| `billing.agent_payout_initiated` | Agent payment started | Billing Service | Analytics |
| `billing.agent_payout_completed` | Agent payment finished | Billing Service | Notification |
| `billing.payment_failed` | Payment processing failed | Billing Service | Notification, Admin |
| `billing.fraud_detected` | Suspicious activity | Billing Service | Security, Admin |
| `billing.subscription_created` | Subscription activated | Billing Service | Analytics, Notification |
| `billing.subscription_renewed` | Subscription auto-renewed | Billing Service | Analytics, Notification |
| `billing.subscription_cancelled` | Subscription ended | Billing Service | Analytics, Notification |
| `billing.chargeback_received` | Payment dispute filed | Billing Service | Admin, Security |

### System Events
Platform health, monitoring, and operational events.

#### system.health_check

**Producer**: All Services  
**Consumers**: Monitoring Service, Admin Platform  
**Trigger**: Every 30 seconds per service instance  
**Frequency**: ~20,000/day

**Payload**:
```json
{
  "eventId": "evt_3456789012678901",
  "type": "system.health_check",
  "timestamp": "2025-06-26T19:00:00Z",
  "version": "1.0.0",
  "data": {
    "service": "task-management",
    "instance": "task-mgmt-pod-abc123",
    "status": "healthy",
    "checks": {
      "database": "ok",
      "redis": "ok",
      "pubsub": "ok"
    },
    "metrics": {
      "cpu": 45.2,
      "memory": 72.8,
      "activeConnections": 156
    }
  }
}
```

**Schema**: [/schemas/system-events.json#system.health_check](./schemas/system-events.json)  
**Since**: v1.0.0

#### system.error

**Producer**: All Services  
**Consumers**: Monitoring Service, Admin Platform, Alert Service  
**Trigger**: When an unhandled error or critical issue occurs  
**Frequency**: ~50/day

**Payload**:
```json
{
  "eventId": "evt_4567890123789012",
  "type": "system.error",
  "timestamp": "2025-06-26T19:30:00Z",
  "version": "1.0.0",
  "data": {
    "service": "matching-engine",
    "instance": "matching-pod-xyz789",
    "severity": "error",
    "error": {
      "type": "DatabaseConnectionError",
      "message": "Unable to connect to Firestore",
      "code": "FIRESTORE_UNAVAILABLE",
      "stack": "Error: Unable to connect...\n at ..."
    },
    "context": {
      "userId": "usr_1234567890abcdef12",
      "taskId": "tsk_1234567890abcdef12",
      "operation": "findMatchingAgents"
    },
    "impact": "Task matching delayed"
  }
}
```

**Schema**: [/schemas/system-events.json#system.error](./schemas/system-events.json)  
**Since**: v1.0.0

#### system.alert

**Producer**: Monitoring Service  
**Consumers**: Admin Platform, Notification Service  
**Trigger**: When system metrics exceed thresholds  
**Frequency**: ~10/day

**Payload**:
```json
{
  "eventId": "evt_5678901234890123",
  "type": "system.alert",
  "timestamp": "2025-06-26T20:00:00Z",
  "version": "1.0.0",
  "data": {
    "alertId": "alert_1234567890",
    "severity": "warning",
    "type": "high_error_rate",
    "service": "billing-service",
    "metric": "error_rate",
    "threshold": 5.0,
    "currentValue": 7.2,
    "duration": 300,
    "message": "Error rate exceeded 5% for 5 minutes",
    "runbook": "https://docs.vibe-match.com/runbooks/high-error-rate"
  }
}
```

**Schema**: [/schemas/system-events.json#system.alert](./schemas/system-events.json)  
**Since**: v1.0.0

| Event | Description | Publishers | Consumers |
|-------|-------------|------------|-----------|  
| `system.health_check` | Service health status | All Services | Monitoring, Admin |
| `system.metrics` | Performance metrics | All Services | Analytics, Monitoring |
| `system.error` | System errors | All Services | Monitoring, Alert Service |
| `system.alert` | Threshold alerts | Monitoring | Admin, Notification |
| `system.deployment_started` | New deployment begins | CI/CD | All Services |
| `system.deployment_completed` | Deployment finished | CI/CD | All Services |
| `system.config_updated` | Configuration change | Admin Platform | Affected Services |
| `system.feature_flag_updated` | Feature flag change | Admin Platform | All Services |
| `system.maintenance_scheduled` | Maintenance window | Admin Platform | Notification |
| `system.backup_completed` | Data backup finished | Backup Service | Admin Platform |

## Event Flow Diagrams

### Single Agent Task Flow

```mermaid
sequenceDiagram
    participant User
    participant TaskMgmt as Task Management
    participant Matching as Matching Engine
    participant Agent
    participant Billing
    participant Credit as Credit System
    
    User->>TaskMgmt: Create task
    TaskMgmt-->>Matching: task.created
    
    Matching->>Matching: Find suitable agents
    Matching-->>Agent: agent.matched
    Matching-->>TaskMgmt: task.matched
    
    Agent->>TaskMgmt: Accept task
    TaskMgmt-->>Credit: task.accepted
    TaskMgmt-->>Agent: task.started
    TaskMgmt-->>Billing: agent.task_started
    
    Credit->>Credit: Deduct credits
    Credit-->>Billing: billing.credits_deducted
    
    loop Task Progress
        Agent->>TaskMgmt: Update progress
        TaskMgmt-->>User: task.progress_updated
    end
    
    Agent->>TaskMgmt: Complete task
    TaskMgmt-->>Billing: task.completed
    TaskMgmt-->>Agent: agent.task_completed
    
    User->>TaskMgmt: Rate agent
    TaskMgmt-->>Agent: agent.rated
    
    Billing->>Agent: Process payment
    Billing-->>Agent: billing.agent_payout_initiated
    Billing-->>Agent: billing.agent_payout_completed
```

### Multi-Agent Orchestration Flow

```mermaid
sequenceDiagram
    participant User
    participant TaskMgmt as Task Management
    participant Orch as Orchestration Engine
    participant Matching as Matching Engine
    participant Agent1
    participant Agent2
    participant Billing
    
    User->>TaskMgmt: Create complex task
    TaskMgmt-->>Orch: task.created (type: orchestrated)
    
    Orch->>Orch: Analyze task requirements
    Orch->>Matching: Request multiple agents
    
    Matching-->>Agent1: agent.matched (role: data_analysis)
    Matching-->>Agent2: agent.matched (role: content_creation)
    Matching-->>Orch: task.matched (multiple agents)
    
    Orch-->>TaskMgmt: task.orchestration_started
    
    Note over Orch: Step 1: Data Analysis
    Orch->>Agent1: Execute step 1
    Agent1-->>Orch: task.orchestration_step (completed)
    
    Note over Orch: Step 2: Content Creation
    Orch->>Agent2: Execute step 2 (with step 1 output)
    Agent2-->>Orch: task.orchestration_step (completed)
    
    Orch->>Orch: Assemble final result
    Orch-->>TaskMgmt: task.orchestration_completed
    TaskMgmt-->>User: task.completed
    
    Billing->>Agent1: billing.agent_payout_initiated
    Billing->>Agent2: billing.agent_payout_initiated
```

### Error Handling Flow

```mermaid
sequenceDiagram
    participant Agent
    participant TaskMgmt as Task Management
    participant Billing
    participant Credit as Credit System
    participant Admin
    
    Agent->>TaskMgmt: Task fails
    TaskMgmt-->>Admin: task.failed
    
    alt Can Retry
        TaskMgmt->>Agent: Retry task
        Agent->>TaskMgmt: Task succeeds
        TaskMgmt-->>Billing: task.completed
    else Cannot Retry
        TaskMgmt-->>Credit: task.failed (refundable)
        Credit->>Credit: Refund credits
        Credit-->>User: billing.credits_refunded
        Admin->>Admin: Review failure
        Admin-->>Agent: agent.performance_updated
    end
```

## Implementation Guide

### Publishing Events

#### Basic Event Publishing
```typescript
import { EventBus } from '@vibe-match/event-bus';
import { v4 as uuidv4 } from 'uuid';

// Publish a simple event
await EventBus.publish('task.completed', {
  eventId: `evt_${uuidv4()}`,
  type: 'task.completed',
  timestamp: new Date().toISOString(),
  version: '1.0.0',
  source: {
    service: 'task-management',
    instance: process.env.INSTANCE_ID
  },
  data: {
    taskId: task.id,
    agentId: agent.id,
    result: { success: true, deliverables: [...] },
    duration: 45,
    creditsUsed: 100
  },
  metadata: {
    correlationId: context.correlationId,
    causationId: context.eventId,
    userId: task.userId
  }
});
```

#### Event Publishing with Error Handling
```typescript
import { EventBus, EventPublishError } from '@vibe-match/event-bus';
import { logger } from '@vibe-match/logger';

try {
  await EventBus.publish('user.created', eventData, {
    timeout: 5000,
    retries: 3,
    priority: 'high'
  });
} catch (error) {
  if (error instanceof EventPublishError) {
    logger.error('Failed to publish event', {
      eventType: 'user.created',
      error: error.message,
      eventData
    });
    // Store in outbox for later retry
    await outbox.store(eventData);
  }
  throw error;
}
```

### Subscribing to Events

#### Basic Subscription
```typescript
import { EventBus } from '@vibe-match/event-bus';
import { TaskCompletedEvent } from '@vibe-match/event-types';

// Subscribe to a single event type
EventBus.subscribe<TaskCompletedEvent>('task.completed', async (event) => {
  const { taskId, agentId, result } = event.data;
  
  // Process the event
  await updateAgentStats(agentId);
  await processPayment(taskId, agentId);
  
  // Acknowledge successful processing
  await event.ack();
});
```

#### Subscription with Error Handling
```typescript
EventBus.subscribe('billing.payment_failed', async (event) => {
  try {
    await handlePaymentFailure(event.data);
    await event.ack();
  } catch (error) {
    logger.error('Failed to process payment failure', {
      error,
      event: event.data
    });
    
    // Retry with exponential backoff
    if (event.deliveryAttempt < 5) {
      await event.nack({ requeue: true, delay: event.deliveryAttempt * 1000 });
    } else {
      // Send to dead letter queue after max retries
      await event.nack({ requeue: false });
    }
  }
});
```

#### Batch Processing
```typescript
// Process events in batches for efficiency
EventBus.subscribeBatch('system.metrics', {
  batchSize: 100,
  maxWaitTime: 5000, // 5 seconds
  handler: async (events) => {
    const metrics = events.map(e => e.data);
    await batchInsertMetrics(metrics);
    
    // Acknowledge all events in batch
    await Promise.all(events.map(e => e.ack()));
  }
});
```

### Error Handling Patterns

#### Dead Letter Queue Processing
```typescript
// Monitor dead letter queue
EventBus.subscribeDLQ(async (failedEvent) => {
  logger.error('Event processing failed permanently', {
    eventType: failedEvent.type,
    eventId: failedEvent.eventId,
    attempts: failedEvent.deliveryAttempts,
    lastError: failedEvent.lastError
  });
  
  // Alert admin for manual intervention
  await alertAdmin(failedEvent);
});
```

#### Circuit Breaker Pattern
```typescript
import { CircuitBreaker } from '@vibe-match/circuit-breaker';

const paymentBreaker = new CircuitBreaker({
  threshold: 5,
  timeout: 60000,
  resetTimeout: 120000
});

EventBus.subscribe('billing.credits_purchased', async (event) => {
  await paymentBreaker.execute(async () => {
    await processPayment(event.data);
    await event.ack();
  }).catch(async (error) => {
    if (paymentBreaker.isOpen()) {
      // Circuit is open, send to retry queue
      await event.nack({ requeue: true, delay: 60000 });
    } else {
      throw error;
    }
  });
});
```

### Testing Events

```typescript
import { EventBus } from '@vibe-match/event-bus';
import { TestEventBus } from '@vibe-match/event-bus/testing';

describe('Task Completion', () => {
  let testBus: TestEventBus;
  
  beforeEach(() => {
    testBus = new TestEventBus();
    EventBus.setImplementation(testBus);
  });
  
  it('should publish task.completed event', async () => {
    await completeTask(taskId);
    
    const events = testBus.getPublishedEvents('task.completed');
    expect(events).toHaveLength(1);
    expect(events[0].data.taskId).toBe(taskId);
  });
});
```

## Event Versioning Strategy

### Version Format
Events use semantic versioning (MAJOR.MINOR.PATCH):
- **MAJOR**: Breaking changes (field removal, type changes)
- **MINOR**: Backward compatible additions (new optional fields)
- **PATCH**: Documentation or description updates

### Versioning Rules

1. **Adding Optional Fields** (Minor Version)
   ```json
   // v1.0.0
   {
     "type": "user.created",
     "data": {
       "userId": "usr_123",
       "email": "<EMAIL>"
     }
   }
   
   // v1.1.0 - Added optional field
   {
     "type": "user.created",
     "data": {
       "userId": "usr_123",
       "email": "<EMAIL>",
       "referralCode": "FRIEND123" // New optional field
     }
   }
   ```

2. **Removing or Changing Fields** (Major Version)
   ```json
   // v1.0.0
   {
     "type": "task.created",
     "data": {
       "taskId": "tsk_123",
       "budget": 100 // number
     }
   }
   
   // v2.0.0 - Changed field type
   {
     "type": "task.created",
     "data": {
       "taskId": "tsk_123",
       "budget": { // Now an object
         "amount": 100,
         "currency": "USD"
       }
     }
   }
   ```

### Consumer Compatibility

```typescript
// Handle multiple versions
EventBus.subscribe('user.created', async (event) => {
  const version = event.version;
  
  if (version.startsWith('1.')) {
    // Handle v1.x events
    await handleUserCreatedV1(event.data);
  } else if (version.startsWith('2.')) {
    // Handle v2.x events
    await handleUserCreatedV2(event.data);
  } else {
    logger.warn('Unknown event version', { version, eventType: event.type });
  }
});
```

### Migration Strategy

1. **Deprecation Notice**: Announce version deprecation 30 days in advance
2. **Dual Publishing**: Publish both versions during migration period
3. **Consumer Updates**: Update all consumers to handle new version
4. **Version Sunset**: Stop publishing old version after all consumers updated

### Schema Evolution Example

```typescript
// Event publisher with version migration
class UserEventPublisher {
  async publishUserCreated(user: User) {
    const v1Event = {
      type: 'user.created',
      version: '1.2.0',
      data: {
        userId: user.id,
        email: user.email,
        role: user.role
      }
    };
    
    const v2Event = {
      type: 'user.created',
      version: '2.0.0',
      data: {
        userId: user.id,
        email: user.email,
        roles: [user.role], // Changed from string to array
        profile: {
          displayName: user.displayName,
          avatar: user.avatar
        }
      }
    };
    
    // Publish both during migration
    if (FEATURE_FLAGS.enableEventV2) {
      await EventBus.publish('user.created', v2Event);
    }
    
    if (!FEATURE_FLAGS.disableEventV1) {
      await EventBus.publish('user.created.v1', v1Event);
    }
  }
}
```

### Retention Policy by Event Type

| Event Category | Retention Period | Reason |
|---------------|------------------|--------|
| User Events | 7 years | GDPR compliance |
| Financial Events | 7 years | Regulatory requirements |
| Agent Events | 2 years | Performance analysis |
| Task Events | 1 year | Dispute resolution |
| System Events | 90 days | Operational monitoring |
| Health Checks | 7 days | Real-time monitoring only |

---
**Document Owner**: Platform Team  
**Last Updated**: 2025-06-26  
**Parent Directory**: [./](./)