# Agent Matching Event Flow

> **Purpose**: Document the event flow for matching users with suitable agents  
> **Audience**: Backend developers, matching algorithm engineers  
> **Trigger**: User creates a task requiring agent assignment

## Overview

This flow describes how VibeMatch's matching engine identifies and presents suitable agents to users based on task requirements, agent capabilities, availability, and performance metrics.

## Participants

- **User**: Task requester
- **API Gateway**: Request routing
- **Task Management Service**: Task lifecycle management
- **Matching Engine Service**: Agent selection algorithm
- **Agent Management Service**: Agent data and availability
- **Credit System Service**: Budget validation
- **Analytics Service**: Performance tracking
- **Notification Service**: Agent and user notifications
- **Google Cloud Pub/Sub**: Event messaging

## Event Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant Gateway as API Gateway
    participant TaskMgmt as Task Management
    participant Matching as Matching Engine
    participant AgentMgmt as Agent Management
    participant PubSub as Google Pub/Sub
    participant CreditSys as Credit System
    participant Analytics
    participant Notification

    Note over User, Notification: Agent Matching Flow

    %% 1. User creates task
    User->>Gateway: POST /tasks
    Note right of User: Task: "Create blog post about AI"<br/>Budget: 200 credits<br/>Deadline: 2 days
    
    Gateway->>TaskMgmt: Create task
    TaskMgmt->>TaskMgmt: Validate and store task
    TaskMgmt->>PubSub: Publish task.created
    
    %% 2. Matching engine processes task
    PubSub-->>Matching: task.created event
    Matching->>Matching: Extract matching criteria
    Note over Matching: Required: content-creation<br/>Budget: 200 credits<br/>Quality: 4.0+<br/>Availability: 48 hours
    
    %% 3. Query available agents
    Matching->>AgentMgmt: Query agents by capabilities
    AgentMgmt->>AgentMgmt: Filter by capability: content-creation
    AgentMgmt->>AgentMgmt: Check availability status
    AgentMgmt-->>Matching: Return candidate agents
    
    %% 4. Apply matching algorithm
    Matching->>Matching: Calculate match scores
    Note over Matching: Factors:<br/>- Capability match<br/>- Quality rating<br/>- Cost efficiency<br/>- Availability<br/>- Past performance
    
    Matching->>Matching: Rank agents by score
    Matching->>Matching: Apply budget constraints
    Matching->>Matching: Select top 3-5 matches
    
    %% 5. Publish match results
    Matching->>PubSub: Publish match.found
    
    %% 6. Multiple services consume match event
    PubSub-->>TaskMgmt: match.found event
    PubSub-->>Analytics: match.found event
    PubSub-->>Notification: match.found event
    
    %% 7. Notify user and agents
    TaskMgmt->>TaskMgmt: Store match results
    TaskMgmt-->>User: Return matched agents
    
    Notification->>User: "Agents found" notification
    Note right of User: Email/push with<br/>agent previews
    
    %% 8. User reviews and selects agent
    User->>Gateway: POST /tasks/{id}/select-agent
    Note right of User: Selected: Agent #2<br/>Reason: Best balance of<br/>quality and cost
    
    Gateway->>TaskMgmt: Process agent selection
    TaskMgmt->>PubSub: Publish match.accepted
    
    %% 9. Reserve credits and notify agent
    PubSub-->>CreditSys: match.accepted event
    PubSub-->>Notification: match.accepted event
    PubSub-->>Analytics: match.accepted event
    
    CreditSys->>CreditSys: Reserve credits for task
    CreditSys->>PubSub: Publish credit.reserved
    
    Notification->>User: "Agent assigned" confirmation
    Notification->>Gateway: Notify selected agent
    Note right of Gateway: Agent receives task<br/>assignment notification
    
    %% 10. Update matching metrics
    Analytics->>Analytics: Update match success rate
    Analytics->>Analytics: Update agent selection patterns
    Analytics->>Analytics: Track user preferences
```

## Event Sequence Details

### 1. task.created Event
**Publisher**: Task Management Service  
**Event Type**: `task.created`

```json
{
  "eventId": "evt_task_123",
  "type": "task.created",
  "timestamp": "2025-06-26T10:00:00Z",
  "data": {
    "taskId": "task_1234567890abcdef",
    "userId": "usr_1234567890abcdef12",
    "title": "Create AI Blog Post",
    "description": "Write comprehensive blog post about AI trends in 2025",
    "requirements": {
      "capabilities": ["content-creation", "seo-optimization"],
      "estimatedBudget": 200,
      "maxBudget": 250,
      "deadline": "2025-06-28T10:00:00Z",
      "qualityLevel": "premium",
      "urgency": "normal",
      "wordCount": 2000,
      "targetAudience": "tech professionals"
    },
    "preferences": {
      "preferredAgentTypes": ["experienced"],
      "avoidAgentIds": ["agent_previous_bad_experience"],
      "languagePreference": "en-US"
    }
  }
}
```

### 2. match.found Event
**Publisher**: Matching Engine Service  
**Event Type**: `match.found`

```json
{
  "eventId": "evt_match_456",
  "type": "match.found",
  "timestamp": "2025-06-26T10:02:00Z",
  "data": {
    "matchId": "match_9876543210fedcba",
    "taskId": "task_1234567890abcdef",
    "userId": "usr_1234567890abcdef12",
    "matches": [
      {
        "agentId": "agent_1122334455667788",
        "score": 0.95,
        "estimatedCost": 180,
        "estimatedDuration": 240,
        "capabilities": ["content-creation", "seo-optimization", "technical-writing"],
        "availability": "immediate",
        "qualityScore": 4.8,
        "completionRate": 0.98,
        "averageRating": 4.9,
        "portfolioSamples": [
          "https://portfolio.vibe-match.com/agent_1122/sample1.pdf"
        ],
        "matchReasons": [
          "Perfect capability match",
          "Excellent quality rating",
          "Within budget",
          "Immediate availability"
        ]
      },
      {
        "agentId": "agent_2233445566778899",
        "score": 0.87,
        "estimatedCost": 150,
        "estimatedDuration": 300,
        "capabilities": ["content-creation", "copywriting"],
        "availability": "within-2-hours",
        "qualityScore": 4.5,
        "completionRate": 0.95,
        "averageRating": 4.6,
        "portfolioSamples": [
          "https://portfolio.vibe-match.com/agent_2233/sample1.pdf"
        ],
        "matchReasons": [
          "Good capability match",
          "Cost effective",
          "Reliable completion rate"
        ]
      },
      {
        "agentId": "agent_3344556677889900",
        "score": 0.82,
        "estimatedCost": 220,
        "estimatedDuration": 180,
        "capabilities": ["content-creation", "seo-optimization", "marketing"],
        "availability": "within-4-hours",
        "qualityScore": 4.7,
        "completionRate": 0.92,
        "averageRating": 4.8,
        "portfolioSamples": [
          "https://portfolio.vibe-match.com/agent_3344/sample1.pdf"
        ],
        "matchReasons": [
          "Strong capability match",
          "Fast delivery",
          "Marketing expertise"
        ]
      }
    ],
    "matchingCriteria": {
      "requiredCapabilities": ["content-creation"],
      "maxBudget": 250,
      "urgency": "normal",
      "qualityThreshold": 4.0,
      "preferredAvailability": "within-24-hours"
    },
    "algorithmVersion": "v2.1.0",
    "expiresAt": "2025-06-26T12:00:00Z"
  }
}
```

### 3. match.accepted Event
**Publisher**: Task Management Service  
**Event Type**: `match.accepted`

```json
{
  "eventId": "evt_match_accept_789",
  "type": "match.accepted",
  "timestamp": "2025-06-26T10:15:00Z",
  "data": {
    "matchId": "match_9876543210fedcba",
    "taskId": "task_1234567890abcdef",
    "userId": "usr_1234567890abcdef12",
    "selectedAgent": {
      "agentId": "agent_2233445566778899",
      "score": 0.87,
      "estimatedCost": 150,
      "estimatedDuration": 300,
      "acceptedAt": "2025-06-26T10:15:00Z"
    },
    "rejectedAgents": [
      {
        "agentId": "agent_1122334455667788",
        "reason": "too_expensive"
      },
      {
        "agentId": "agent_3344556677889900",
        "reason": "longer_wait_time"
      }
    ],
    "selectionReason": "best_value_for_money",
    "userFeedback": {
      "selectionFactors": ["cost", "quality", "availability"],
      "comments": "Good balance of quality and cost"
    }
  }
}
```

## Matching Algorithm Details

### Scoring Factors
```typescript
interface MatchingScore {
  capabilityMatch: number;    // 0-1 (40% weight)
  qualityRating: number;      // 0-1 (25% weight)
  costEfficiency: number;     // 0-1 (20% weight)
  availability: number;       // 0-1 (10% weight)
  pastPerformance: number;    // 0-1 (5% weight)
}

function calculateMatchScore(agent: Agent, task: Task): number {
  const weights = {
    capability: 0.40,
    quality: 0.25,
    cost: 0.20,
    availability: 0.10,
    performance: 0.05
  };
  
  return (
    agent.capabilityMatch * weights.capability +
    agent.qualityRating * weights.quality +
    agent.costEfficiency * weights.cost +
    agent.availability * weights.availability +
    agent.pastPerformance * weights.performance
  );
}
```

### Capability Matching
```typescript
function calculateCapabilityMatch(
  agentCapabilities: string[],
  requiredCapabilities: string[]
): number {
  const matches = requiredCapabilities.filter(req => 
    agentCapabilities.includes(req)
  );
  
  const exactMatch = matches.length / requiredCapabilities.length;
  const bonusCapabilities = agentCapabilities.length - matches.length;
  const bonus = Math.min(bonusCapabilities * 0.1, 0.2);
  
  return Math.min(exactMatch + bonus, 1.0);
}
```

## Match Rejection Flow

```mermaid
sequenceDiagram
    participant User
    participant TaskMgmt as Task Management
    participant Matching as Matching Engine
    participant Analytics

    User->>TaskMgmt: Reject all matches
    TaskMgmt->>TaskMgmt: Store rejection feedback
    TaskMgmt->>PubSub: Publish match.rejected
    
    PubSub-->>Matching: match.rejected event
    PubSub-->>Analytics: match.rejected event
    
    Matching->>Matching: Analyze rejection reasons
    Matching->>Matching: Adjust matching criteria
    Matching->>Matching: Find new matches
    Matching->>PubSub: Publish match.found (v2)
    
    Analytics->>Analytics: Update rejection patterns
    Analytics->>Analytics: Improve algorithm feedback
```

## Success Criteria

1. **Relevant Matches**: At least 3 suitable agents found
2. **Quality Threshold**: All matches meet minimum quality requirements
3. **Budget Compliance**: All matches within user's budget
4. **Availability**: Agents available within required timeframe
5. **User Satisfaction**: User accepts one of the provided matches
6. **Response Time**: Matches found within 5 seconds

## Performance Metrics

- **Match Finding Time**: < 5 seconds
- **Match Acceptance Rate**: > 80%
- **Algorithm Accuracy**: > 90% user satisfaction
- **Agent Utilization**: > 75% of active agents matched weekly
- **Rejection Rate**: < 20% of matches rejected

## Algorithm Improvements

### Machine Learning Integration
- User preference learning
- Agent performance prediction
- Dynamic scoring weight adjustment
- Seasonal demand forecasting

### A/B Testing Framework
- Algorithm variant testing
- Scoring weight optimization
- UI presentation testing
- Match ranking experiments

## Related Flows

- [Task Completion Flow](./task-completion-flow.md)
- [Multi-Agent Orchestration Flow](./multi-agent-orchestration-flow.md)
- [Payment Flow](./payment-flow.md)

---
**Flow Owner**: Matching Engine Team  
**Last Updated**: 2025-06-26  
**Version**: 1.0.0
