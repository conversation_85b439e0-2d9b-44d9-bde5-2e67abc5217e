# Payment Processing Event Flow

> **Purpose**: Document the complete event flow for credit purchases and payments in VibeMatch  
> **Audience**: Backend developers, payment integration engineers  
> **Trigger**: User initiates credit purchase or payment processing

## Overview

This flow describes the sequence of events that occur when a user purchases credits, from payment initiation through credit addition and notification delivery. It covers both successful payments and failure scenarios.

## Participants

- **User**: Credit purchaser
- **Frontend**: Web/mobile application
- **API Gateway**: Request routing and authentication
- **Billing Service**: Payment processing orchestration
- **Credit System Service**: Credit balance management
- **Stripe**: External payment processor
- **Analytics Service**: Transaction metrics
- **Notification Service**: User notifications
- **Google Cloud Pub/Sub**: Event messaging

## Event Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Gateway as API Gateway
    participant Billing as Billing Service
    participant Stripe as Stripe API
    participant PubSub as Google Pub/Sub
    participant CreditSys as Credit System
    participant Analytics
    participant Notification

    Note over User, Notification: Credit Purchase Flow

    %% 1. User initiates purchase
    User->>Frontend: Select credit package
    Frontend->>Gateway: POST /billing/purchase-credits
    Note right of Frontend: Package: 1000 credits<br/>Amount: $99.99
    
    Gateway->>Billing: Process purchase request
    Billing->>Billing: Validate user and package
    Billing->>Billing: Calculate total with taxes
    
    %% 2. Create Stripe payment intent
    Billing->>Stripe: Create PaymentIntent
    Note over Stripe: Amount: $99.99<br/>Currency: USD
    Stripe-->>Billing: PaymentIntent created
    Billing-->>Frontend: Return client_secret
    
    %% 3. User completes payment
    Frontend->>Stripe: Confirm payment (client-side)
    User->>Frontend: Enter payment details
    Stripe->>Stripe: Process payment
    
    %% 4. Payment successful
    Stripe-->>Frontend: Payment succeeded
    Stripe->>Billing: Webhook: payment_intent.succeeded
    
    %% 5. Billing processes success
    Billing->>Billing: Verify webhook signature
    Billing->>Billing: Update payment record
    Billing->>PubSub: Publish billing.payment_processed
    
    %% 6. Credit system adds credits
    PubSub-->>CreditSys: billing.payment_processed
    CreditSys->>CreditSys: Add credits to user account
    CreditSys->>CreditSys: Apply bonus credits if applicable
    CreditSys->>PubSub: Publish credit.added
    
    %% 7. Generate invoice
    Billing->>Billing: Generate invoice PDF
    Billing->>PubSub: Publish billing.invoice_generated
    
    %% 8. Analytics and notifications
    PubSub-->>Analytics: billing.payment_processed
    PubSub-->>Analytics: credit.added
    PubSub-->>Notification: billing.payment_processed
    PubSub-->>Notification: billing.invoice_generated
    
    Analytics->>Analytics: Update revenue metrics
    Analytics->>Analytics: Update user spending patterns
    
    Notification->>User: Payment confirmation email
    Note right of User: Includes invoice PDF<br/>and credit balance
    Notification->>User: Credits added notification
    
    %% 9. Frontend updates
    Frontend->>Gateway: GET /users/me/credits
    Gateway->>CreditSys: Get current balance
    CreditSys-->>Frontend: Updated credit balance
```

## Event Sequence Details

### 1. Payment Initiation
**Event**: HTTP POST to `/billing/purchase-credits`

Request payload:
```json
{
  "packageId": "pkg_standard_1000",
  "promoCode": "WELCOME20",
  "paymentMethodId": "pm_1234567890"
}
```

### 2. billing.payment_processed Event
**Publisher**: Billing Service  
**Event Type**: `billing.payment_processed`

```json
{
  "eventId": "evt_payment_123",
  "type": "billing.payment_processed",
  "timestamp": "2025-06-26T16:30:00Z",
  "data": {
    "paymentId": "pay_1234567890abcdef",
    "userId": "usr_1234567890abcdef12",
    "amount": 99.99,
    "currency": "USD",
    "paymentMethod": "card",
    "cardLast4": "4242",
    "packageId": "pkg_standard_1000",
    "creditsToAdd": 1000,
    "bonusCredits": 200,
    "promoCode": "WELCOME20",
    "discountAmount": 20.00,
    "taxAmount": 8.00,
    "stripePaymentIntentId": "pi_1234567890abcdef"
  }
}
```

### 3. credit.added Event
**Publisher**: Credit System Service  
**Event Type**: `credit.added`

```json
{
  "eventId": "evt_credit_add_456",
  "type": "credit.added",
  "timestamp": "2025-06-26T16:30:15Z",
  "data": {
    "transactionId": "txn_1234567890abcdef",
    "userId": "usr_1234567890abcdef12",
    "amount": 1200,
    "source": "purchase",
    "sourceDetails": {
      "paymentId": "pay_1234567890abcdef",
      "packageType": "standard",
      "promoCode": "WELCOME20",
      "bonusCredits": 200
    },
    "balanceBefore": 150,
    "balanceAfter": 1350,
    "expiresAt": "2026-06-26T16:30:15Z"
  }
}
```

### 4. billing.invoice_generated Event
**Publisher**: Billing Service  
**Event Type**: `billing.invoice_generated`

```json
{
  "eventId": "evt_invoice_789",
  "type": "billing.invoice_generated",
  "timestamp": "2025-06-26T16:30:30Z",
  "data": {
    "invoiceId": "inv_1234567890abcdef",
    "userId": "usr_1234567890abcdef12",
    "paymentId": "pay_1234567890abcdef",
    "invoiceNumber": "INV-2025-001234",
    "amount": 99.99,
    "currency": "USD",
    "pdfUrl": "https://invoices.vibe-match.com/inv_1234567890abcdef.pdf",
    "dueDate": "2025-06-26T16:30:30Z",
    "items": [
      {
        "description": "1000 Credits Package",
        "quantity": 1,
        "unitPrice": 119.99,
        "discount": 20.00,
        "total": 99.99
      }
    ]
  }
}
```

## Payment Failure Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Billing as Billing Service
    participant Stripe as Stripe API
    participant PubSub as Google Pub/Sub
    participant Analytics
    participant Notification

    Note over User, Notification: Payment Failure Flow

    %% Payment fails
    Stripe->>Stripe: Payment processing fails
    Stripe-->>Frontend: Payment failed
    Stripe->>Billing: Webhook: payment_intent.payment_failed
    
    %% Process failure
    Billing->>Billing: Update payment record (failed)
    Billing->>PubSub: Publish billing.payment_failed
    
    %% Analytics and notifications
    PubSub-->>Analytics: billing.payment_failed
    PubSub-->>Notification: billing.payment_failed
    
    Analytics->>Analytics: Update failure metrics
    Notification->>User: Payment failed notification
    Note right of User: Includes retry options<br/>and support contact
    
    %% User retry flow
    User->>Frontend: Retry payment
    Note over Frontend: Redirect to payment form<br/>with error context
```

## Webhook Security

### Stripe Webhook Verification
```typescript
// Webhook signature verification
const signature = request.headers['stripe-signature'];
const payload = request.body;

try {
  const event = stripe.webhooks.constructEvent(
    payload,
    signature,
    process.env.STRIPE_WEBHOOK_SECRET
  );
  
  // Process verified webhook
  await processStripeWebhook(event);
} catch (err) {
  console.error('Webhook signature verification failed:', err);
  return response.status(400).send('Invalid signature');
}
```

## Error Handling Scenarios

### 1. Insufficient Funds
- Stripe returns payment failure
- User notified with alternative payment methods
- Analytics tracks abandonment reasons

### 2. Network Timeout
- Implement retry logic with exponential backoff
- Idempotency keys prevent duplicate charges
- Manual reconciliation for edge cases

### 3. Webhook Delivery Failure
- Stripe retries webhooks automatically
- Implement webhook replay mechanism
- Monitor webhook delivery success rates

## Success Criteria

1. **Payment Processed**: Stripe confirms successful payment
2. **Credits Added**: User balance updated correctly
3. **Invoice Generated**: PDF invoice created and stored
4. **Events Published**: All payment events published to Pub/Sub
5. **User Notified**: Confirmation email sent with invoice
6. **Analytics Updated**: Revenue and user metrics updated

## Performance Metrics

- **Payment Processing Time**: < 10 seconds
- **Credit Addition**: < 3 seconds after payment
- **Invoice Generation**: < 5 seconds
- **Notification Delivery**: < 30 seconds
- **Webhook Processing**: < 2 seconds
- **Success Rate**: > 99.5%

## Security Considerations

1. **PCI Compliance**: All card data handled by Stripe
2. **Webhook Verification**: All webhooks cryptographically verified
3. **Idempotency**: Duplicate payment prevention
4. **Audit Trail**: All transactions logged and traceable
5. **Fraud Detection**: Stripe Radar integration

## Related Flows

- [Task Completion Flow](./task-completion-flow.md)
- [Agent Matching Flow](./agent-matching-flow.md)

---
**Flow Owner**: Billing Team  
**Last Updated**: 2025-06-26  
**Version**: 1.0.0
