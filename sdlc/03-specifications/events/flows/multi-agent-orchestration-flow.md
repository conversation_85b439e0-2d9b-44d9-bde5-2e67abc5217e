# Multi-Agent Orchestration Event Flow

> **Purpose**: Document the event flow for coordinating multiple agents on complex tasks  
> **Audience**: Backend developers, orchestration engineers  
> **Trigger**: User submits a task requiring multiple specialized agents

## Overview

This flow describes how VibeMatch orchestrates complex tasks that require multiple agents with different specializations. The Orchestration Engine coordinates agent selection, task decomposition, parallel execution, and result aggregation.

## Participants

- **User**: Task requester
- **API Gateway**: Request routing
- **Task Management Service**: Task lifecycle management
- **Orchestration Engine**: Multi-agent coordination
- **Matching Engine Service**: Agent selection
- **Agent A**: Specialized agent (e.g., Data Analysis)
- **Agent B**: Specialized agent (e.g., Content Creation)
- **Agent C**: Specialized agent (e.g., Design)
- **Credit System Service**: Credit management
- **Billing Service**: Payment processing
- **Analytics Service**: Performance tracking
- **Google Cloud Pub/Sub**: Event messaging

## Event Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant Gateway as API Gateway
    participant TaskMgmt as Task Management
    participant Orch as Orchestration Engine
    participant Matching as Matching Engine
    participant AgentA as Agent A (Data)
    participant AgentB as Agent B (Content)
    participant AgentC as Agent C (Design)
    participant PubSub as Google Pub/Sub
    participant CreditSys as Credit System
    participant Analytics

    Note over User, Analytics: Multi-Agent Orchestration Flow

    %% 1. User submits complex task
    User->>Gateway: POST /tasks (orchestrated=true)
    Note right of User: Task: "Create marketing campaign<br/>with data analysis, content, and design"
    
    Gateway->>TaskMgmt: Create orchestrated task
    TaskMgmt->>TaskMgmt: Validate and store task
    TaskMgmt->>PubSub: Publish task.created (type: orchestrated)
    
    %% 2. Orchestration engine takes control
    PubSub-->>Orch: task.created event
    Orch->>Orch: Analyze task requirements
    Orch->>Orch: Decompose into subtasks
    Note over Orch: Subtasks:<br/>1. Market research & data analysis<br/>2. Content creation<br/>3. Visual design
    
    %% 3. Request agents for each subtask
    Orch->>Matching: Request agent (data-analysis)
    Orch->>Matching: Request agent (content-creation)
    Orch->>Matching: Request agent (design)
    
    Matching->>Matching: Find suitable agents
    Matching-->>Orch: match.found (AgentA - data)
    Matching-->>Orch: match.found (AgentB - content)
    Matching-->>Orch: match.found (AgentC - design)
    
    %% 4. Orchestration engine assigns agents
    Orch->>PubSub: Publish task.orchestration_started
    Orch->>TaskMgmt: Assign AgentA to subtask 1
    Orch->>TaskMgmt: Assign AgentB to subtask 2
    Orch->>TaskMgmt: Assign AgentC to subtask 3
    
    %% 5. Reserve credits for all subtasks
    PubSub-->>CreditSys: task.orchestration_started
    CreditSys->>CreditSys: Reserve credits for all agents
    CreditSys->>PubSub: Publish credit.reserved (orchestrated)
    
    %% 6. Parallel agent execution
    par Agent A Execution
        TaskMgmt->>AgentA: Start subtask 1
        AgentA->>AgentA: Perform data analysis
        AgentA->>TaskMgmt: Complete subtask 1
        TaskMgmt->>PubSub: Publish task.subtask_completed (1)
    and Agent B Execution  
        TaskMgmt->>AgentB: Start subtask 2
        AgentB->>AgentB: Create content (depends on data)
        AgentB->>TaskMgmt: Complete subtask 2
        TaskMgmt->>PubSub: Publish task.subtask_completed (2)
    and Agent C Execution
        TaskMgmt->>AgentC: Start subtask 3
        AgentC->>AgentC: Create designs
        AgentC->>TaskMgmt: Complete subtask 3
        TaskMgmt->>PubSub: Publish task.subtask_completed (3)
    end
    
    %% 7. Orchestration engine coordinates completion
    PubSub-->>Orch: task.subtask_completed (1)
    PubSub-->>Orch: task.subtask_completed (2)
    PubSub-->>Orch: task.subtask_completed (3)
    
    Orch->>Orch: Check all subtasks complete
    Orch->>Orch: Aggregate results
    Orch->>Orch: Perform quality validation
    
    %% 8. Final orchestration completion
    Orch->>TaskMgmt: Mark main task complete
    TaskMgmt->>PubSub: Publish task.orchestration_completed
    
    %% 9. Process payments and notifications
    PubSub-->>CreditSys: task.orchestration_completed
    PubSub-->>Analytics: task.orchestration_completed
    
    CreditSys->>CreditSys: Deduct credits for all agents
    CreditSys->>PubSub: Publish credit.deducted (orchestrated)
    
    Analytics->>Analytics: Update orchestration metrics
    Analytics->>Analytics: Track multi-agent performance
```

## Event Sequence Details

### 1. task.created (Orchestrated)
**Publisher**: Task Management Service  
**Event Type**: `task.created`

```json
{
  "eventId": "evt_task_orch_123",
  "type": "task.created",
  "timestamp": "2025-06-26T14:00:00Z",
  "data": {
    "taskId": "task_1234567890abcdef",
    "userId": "usr_1234567890abcdef12",
    "title": "Marketing Campaign Creation",
    "description": "Create comprehensive marketing campaign with data analysis, content, and design",
    "type": "orchestrated",
    "requirements": {
      "capabilities": ["data-analysis", "content-creation", "design"],
      "estimatedBudget": 500,
      "deadline": "2025-06-28T14:00:00Z",
      "complexity": "high"
    },
    "orchestrationConfig": {
      "parallelExecution": true,
      "dependencyGraph": {
        "data-analysis": [],
        "content-creation": ["data-analysis"],
        "design": ["content-creation"]
      }
    }
  }
}
```

### 2. task.orchestration_started
**Publisher**: Orchestration Engine  
**Event Type**: `task.orchestration_started`

```json
{
  "eventId": "evt_orch_start_456",
  "type": "task.orchestration_started",
  "timestamp": "2025-06-26T14:05:00Z",
  "data": {
    "orchestrationId": "orch_1234567890abcdef",
    "taskId": "task_1234567890abcdef",
    "userId": "usr_1234567890abcdef12",
    "subtasks": [
      {
        "subtaskId": "subtask_data_001",
        "agentId": "agent_1122334455667788",
        "capability": "data-analysis",
        "estimatedCost": 150,
        "estimatedDuration": 120,
        "dependencies": []
      },
      {
        "subtaskId": "subtask_content_002", 
        "agentId": "agent_2233445566778899",
        "capability": "content-creation",
        "estimatedCost": 200,
        "estimatedDuration": 180,
        "dependencies": ["subtask_data_001"]
      },
      {
        "subtaskId": "subtask_design_003",
        "agentId": "agent_3344556677889900",
        "capability": "design",
        "estimatedCost": 150,
        "estimatedDuration": 240,
        "dependencies": ["subtask_content_002"]
      }
    ],
    "totalEstimatedCost": 500,
    "executionStrategy": "parallel_with_dependencies"
  }
}
```

### 3. task.subtask_completed
**Publisher**: Task Management Service  
**Event Type**: `task.subtask_completed`

```json
{
  "eventId": "evt_subtask_comp_789",
  "type": "task.subtask_completed",
  "timestamp": "2025-06-26T16:00:00Z",
  "data": {
    "orchestrationId": "orch_1234567890abcdef",
    "taskId": "task_1234567890abcdef",
    "subtaskId": "subtask_data_001",
    "agentId": "agent_1122334455667788",
    "completedAt": "2025-06-26T16:00:00Z",
    "duration": 105,
    "actualCost": 130,
    "deliverables": [
      {
        "type": "report",
        "url": "https://storage.vibe-match.com/deliverables/market_analysis.pdf",
        "name": "Market Analysis Report"
      },
      {
        "type": "dataset",
        "url": "https://storage.vibe-match.com/deliverables/market_data.csv",
        "name": "Market Research Data"
      }
    ],
    "quality": "excellent",
    "nextSubtasks": ["subtask_content_002"]
  }
}
```

### 4. task.orchestration_completed
**Publisher**: Orchestration Engine  
**Event Type**: `task.orchestration_completed`

```json
{
  "eventId": "evt_orch_comp_012",
  "type": "task.orchestration_completed",
  "timestamp": "2025-06-26T18:30:00Z",
  "data": {
    "orchestrationId": "orch_1234567890abcdef",
    "taskId": "task_1234567890abcdef",
    "userId": "usr_1234567890abcdef12",
    "completedAt": "2025-06-26T18:30:00Z",
    "totalDuration": 270,
    "totalCost": 480,
    "estimatedCost": 500,
    "savings": 20,
    "subtaskResults": [
      {
        "subtaskId": "subtask_data_001",
        "agentId": "agent_1122334455667788",
        "status": "completed",
        "cost": 130,
        "quality": "excellent"
      },
      {
        "subtaskId": "subtask_content_002",
        "agentId": "agent_2233445566778899", 
        "status": "completed",
        "cost": 190,
        "quality": "excellent"
      },
      {
        "subtaskId": "subtask_design_003",
        "agentId": "agent_3344556677889900",
        "status": "completed", 
        "cost": 160,
        "quality": "good"
      }
    ],
    "finalDeliverables": [
      {
        "type": "campaign_package",
        "url": "https://storage.vibe-match.com/campaigns/campaign_123.zip",
        "name": "Complete Marketing Campaign Package"
      }
    ],
    "overallQuality": "excellent"
  }
}
```

## Dependency Management

### Sequential Dependencies
```mermaid
graph TD
    A[Data Analysis] --> B[Content Creation]
    B --> C[Design Creation]
    C --> D[Final Assembly]
```

### Parallel with Dependencies
```mermaid
graph TD
    A[Data Analysis] --> B[Content Creation]
    A --> C[Market Research]
    B --> D[Copy Writing]
    C --> D
    D --> E[Design Creation]
    E --> F[Final Assembly]
```

## Error Handling

### Subtask Failure Scenario
```mermaid
sequenceDiagram
    participant Orch as Orchestration Engine
    participant TaskMgmt as Task Management
    participant AgentA as Failed Agent
    participant Matching as Matching Engine
    participant AgentB as Replacement Agent
    
    AgentA->>TaskMgmt: Subtask failed
    TaskMgmt->>Orch: task.subtask_failed
    Orch->>Orch: Evaluate impact on dependencies
    Orch->>Matching: Request replacement agent
    Matching-->>Orch: match.found (replacement)
    Orch->>TaskMgmt: Reassign subtask
    TaskMgmt->>AgentB: Start subtask (retry)
```

## Success Criteria

1. **Task Decomposition**: Complex task broken into manageable subtasks
2. **Agent Assignment**: Appropriate agents matched to each subtask
3. **Dependency Management**: Subtasks executed in correct order
4. **Quality Validation**: All deliverables meet quality standards
5. **Cost Management**: Total cost within estimated budget
6. **Timely Completion**: All subtasks completed within deadline

## Performance Metrics

- **Orchestration Setup Time**: < 30 seconds
- **Agent Assignment Time**: < 60 seconds per agent
- **Dependency Resolution**: < 5 seconds
- **Quality Validation**: < 10 seconds
- **Overall Success Rate**: > 95%
- **Cost Accuracy**: ±10% of estimate

## Related Flows

- [Agent Matching Flow](./agent-matching-flow.md)
- [Task Completion Flow](./task-completion-flow.md)
- [Payment Flow](./payment-flow.md)

---
**Flow Owner**: Orchestration Team  
**Last Updated**: 2025-06-26  
**Version**: 1.0.0
