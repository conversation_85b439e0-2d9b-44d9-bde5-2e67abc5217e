# Task Completion Event Flow

> **Purpose**: Document the complete event flow when a task is completed in VibeMatch  
> **Audience**: Backend developers, integration engineers  
> **Trigger**: Agent completes a task and submits deliverables

## Overview

This flow describes the sequence of events that occur when an agent completes a task, from the initial completion submission through credit deduction, agent payment, and user notification.

## Participants

- **User**: Task requester
- **Agent**: Task executor  
- **API Gateway**: Request routing
- **Task Management Service**: Task lifecycle management
- **Credit System Service**: Credit balance management
- **Billing Service**: Payment processing and agent payouts
- **Analytics Service**: Metrics and reporting
- **Notification Service**: User and agent notifications
- **Google Cloud Pub/Sub**: Event messaging

## Event Flow Diagram

```mermaid
sequenceDiagram
    participant Agent
    participant Gateway as API Gateway
    participant TaskMgmt as Task Management
    participant PubSub as Google Pub/Sub
    participant CreditSys as Credit System
    participant Billing as Billing Service
    participant Analytics
    participant Notification
    participant User

    Note over Agent, User: Task Completion Flow

    %% 1. Agent submits task completion
    Agent->>Gateway: POST /tasks/{id}/complete
    Note right of Agent: Includes deliverables,<br/>summary, time spent
    
    Gateway->>TaskMgmt: Forward completion request
    TaskMgmt->>TaskMgmt: Validate completion
    TaskMgmt->>TaskMgmt: Update task status to 'completed'
    
    %% 2. Publish task.completed event
    TaskMgmt->>PubSub: Publish task.completed
    Note over PubSub: Event contains task details,<br/>agent info, deliverables
    
    %% 3. Multiple services consume the event
    PubSub-->>CreditSys: task.completed event
    PubSub-->>Billing: task.completed event  
    PubSub-->>Analytics: task.completed event
    PubSub-->>Notification: task.completed event
    
    %% 4. Credit System processes payment
    CreditSys->>CreditSys: Calculate final cost
    CreditSys->>CreditSys: Release credit reservation
    CreditSys->>CreditSys: Deduct actual credits used
    CreditSys->>PubSub: Publish credit.deducted
    
    %% 5. Billing processes agent payout
    Billing->>Billing: Calculate agent earnings
    Billing->>Billing: Apply commission rates
    Billing->>Billing: Create payout record
    Billing->>PubSub: Publish billing.agent_payout_initiated
    
    %% 6. Analytics updates metrics
    Analytics->>Analytics: Update task completion metrics
    Analytics->>Analytics: Update agent performance
    Analytics->>Analytics: Update user satisfaction data
    
    %% 7. Notifications sent
    Notification->>User: Task completion notification
    Note right of User: Email + in-app notification<br/>with deliverables
    Notification->>Agent: Payment confirmation
    Note right of Agent: Earnings notification
    
    %% 8. User reviews and rates (optional)
    User->>Gateway: POST /tasks/{id}/rating
    Gateway->>TaskMgmt: Submit rating
    TaskMgmt->>PubSub: Publish task.rated
    PubSub-->>Analytics: task.rated event
    
    %% 9. Final payout processing
    PubSub-->>Billing: credit.deducted event
    Billing->>Billing: Process agent payout
    Billing->>PubSub: Publish billing.agent_payout_completed
    PubSub-->>Notification: billing.agent_payout_completed
    Notification->>Agent: Payout completed notification
```

## Event Sequence Details

### 1. Task Completion Submission
**Event**: HTTP POST to `/tasks/{id}/complete`
- Agent submits completion with deliverables
- Task Management validates submission
- Task status updated to 'completed'

### 2. task.completed Event
**Publisher**: Task Management Service  
**Event Type**: `task.completed`

```json
{
  "eventId": "evt_task_comp_123",
  "type": "task.completed",
  "timestamp": "2025-06-26T18:45:00Z",
  "data": {
    "taskId": "task_1234567890abcdef",
    "userId": "usr_1234567890abcdef12",
    "agentId": "agent_1122334455667788",
    "completedAt": "2025-06-26T18:45:00Z",
    "duration": 120,
    "deliverables": [
      {
        "type": "document",
        "url": "https://storage.vibe-match.com/deliverables/doc_123.pdf",
        "name": "Marketing Copy Final.pdf"
      }
    ],
    "summary": "Completed marketing copy with SEO optimization",
    "estimatedCost": 150,
    "actualTimeSpent": 120
  }
}
```

### 3. Credit Processing
**Event**: `credit.deducted`
- Credit System calculates final cost based on actual time
- Releases reserved credits
- Deducts actual amount used
- Publishes credit.deducted event

### 4. Agent Payout Processing
**Events**: `billing.agent_payout_initiated` → `billing.agent_payout_completed`
- Billing Service calculates agent earnings
- Applies platform commission (typically 20%)
- Creates payout record
- Processes payment to agent account

### 5. User Notification
**Channels**: Email + In-app notification
- Task completion summary
- Deliverables download links
- Rating/feedback request
- Credit usage summary

## Error Handling

### Task Completion Validation Failures
```mermaid
sequenceDiagram
    participant Agent
    participant TaskMgmt as Task Management
    participant Notification
    
    Agent->>TaskMgmt: POST /tasks/{id}/complete (invalid)
    TaskMgmt->>TaskMgmt: Validation fails
    TaskMgmt->>Notification: Send error notification
    Notification->>Agent: Completion rejected notification
    Note right of Agent: Includes specific<br/>validation errors
```

### Credit Insufficient Scenario
```mermaid
sequenceDiagram
    participant TaskMgmt as Task Management
    participant CreditSys as Credit System
    participant Notification
    participant User
    
    TaskMgmt->>CreditSys: Process credit deduction
    CreditSys->>CreditSys: Check available balance
    Note over CreditSys: Insufficient credits
    CreditSys->>Notification: Credit shortage alert
    Notification->>User: Add credits notification
    CreditSys->>TaskMgmt: Partial payment processed
```

## Success Criteria

1. **Task Status Updated**: Task marked as completed in database
2. **Events Published**: All required events published to Pub/Sub
3. **Credits Processed**: User credits deducted correctly
4. **Agent Paid**: Agent payout initiated and completed
5. **Notifications Sent**: User and agent notified of completion
6. **Analytics Updated**: Metrics and performance data updated

## Performance Metrics

- **Event Processing Time**: < 5 seconds end-to-end
- **Credit Deduction**: < 2 seconds
- **Notification Delivery**: < 30 seconds
- **Agent Payout**: < 24 hours
- **Error Rate**: < 0.1% of completions

## Related Flows

- [Agent Matching Flow](./agent-matching-flow.md)
- [Payment Flow](./payment-flow.md)
- [Multi-Agent Orchestration Flow](./multi-agent-orchestration-flow.md)

---
**Flow Owner**: Task Management Team  
**Last Updated**: 2025-06-26  
**Version**: 1.0.0
