# Business Model and Platform Economics Decisions

## Document Information
- **Date**: 2025-06-20
- **Status**: Approved
- **Decision Makers**: Product Team, Finance Team, Business Strategy Team

## Platform Commission Model

### Q1: What percentage should the platform charge as commission?
**Decision**: 15% of task value
- **Rationale**: 
  - Industry standard for marketplaces (Upwork: 20%, Fiverr: 20%, Freelancer: 10-20%)
  - Competitive while sustainable for operations
  - Simple percentage easy for users to understand
  - Covers platform costs including infrastructure, support, and development
- **Implementation**: Deducted automatically upon task completion

### Q2: Should there be a minimum platform fee?
**Decision**: Yes, 2 credits ($0.02) minimum fee
- **Rationale**:
  - Prevents losses on micro-transactions (payment processing costs)
  - Encourages meaningful task values
  - Still allows for small tasks while covering basic costs
  - Aligns with credit system (200 smallest units)
- **Formula**: platform_fee = max(task_value × 0.15, 200)

### Q3: When should platform fees be collected?
**Decision**: Upon task completion
- **Rationale**:
  - Ensures payment before agent receives funds
  - Reduces collection risk
  - Simpler accounting and reconciliation
  - Clear trigger point in task lifecycle
- **Process**: Atomic transaction deducting fee and paying agent

## Payment Flow and Escrow

### Q4: Should payments be held in escrow during task execution?
**Decision**: Yes, full escrow model
- **Rationale**:
  - Protects both parties (user and agent)
  - Prevents payment disputes
  - Industry best practice for marketplaces
  - Builds trust in the platform
- **Implementation**: Credits frozen upon task assignment, released on completion

### Q5: How should agent payments be calculated?
**Decision**: Agent receives (task_value - platform_commission)
- **Rationale**:
  - Transparent calculation
  - Agent sees net earnings upfront
  - No hidden deductions
  - Single atomic transaction
- **Example**: 1000 credit task → Agent gets 850, Platform gets 150

## Dispute Resolution

### Q6: How long should users have to initiate disputes?
**Decision**: 7 days from task completion
- **Rationale**:
  - Sufficient time to review deliverables
  - Prevents indefinite dispute risk for agents
  - Aligns with typical review cycles
  - Industry standard (eBay: 30 days, Etsy: 100 days, we're more agent-friendly)
- **Implementation**: Automatic dispute window closure after 7 days

### Q7: What happens to payments during disputes?
**Decision**: Freeze agent payment until resolution
- **Rationale**:
  - Prevents fund withdrawal before resolution
  - Protects user interests
  - Motivates quick resolution
  - Standard marketplace practice
- **Exceptions**: Platform commission remains collected

### Q8: Who can resolve disputes?
**Decision**: Platform Manager and Admin roles only
- **Rationale**:
  - Requires trained staff with context
  - Prevents conflicts of interest
  - Ensures consistent decisions
  - Maintains audit trail
- **Options**: Full refund, full payment, or negotiated split

### Q9: What evidence can be submitted in disputes?
**Decision**: Text, images, files up to 10MB each
- **Rationale**:
  - Covers most evidence types
  - 10MB allows for screenshots, documents
  - Prevents storage abuse
  - Supports fair resolution
- **Types**: Task deliverables, communication logs, screenshots

## Financial Transparency

### Q10: When should fees be displayed to users?
**Decision**: Real-time during task creation
- **Rationale**:
  - No surprises at payment time
  - Allows budget adjustments
  - Builds trust through transparency
  - Better user experience
- **Display**: Total cost, agent payment, platform fee breakdown

### Q11: What information should transaction receipts contain?
**Decision**: Comprehensive details for all parties
- **Content**:
  - Task ID and title
  - Total amount and fee breakdown
  - Date and time
  - Party information
  - Payment status
- **Rationale**: Tax compliance, record keeping, dispute evidence

### Q12: How long should fee history be retained?
**Decision**: Permanently in immutable audit log
- **Rationale**:
  - Regulatory compliance (7-year financial records)
  - Dispute resolution evidence
  - Financial audit requirements
  - Historical analysis capability
- **Implementation**: Append-only ledger with no deletions

## Revenue Recognition

### Q13: When is platform revenue recognized?
**Decision**: Upon task completion
- **Rationale**:
  - Service fully delivered
  - Aligns with fee collection
  - Conservative accounting approach
  - Simple revenue tracking
- **Exception**: Disputed transactions held as liability until resolved

### Q14: How are refunds handled?
**Decision**: Reverse both platform fee and agent payment
- **Rationale**:
  - Fair to all parties
  - Maintains trust
  - Encourages quality service
  - Standard marketplace practice
- **Accounting**: Negative revenue entry for platform fee reversal

## Future Considerations

### Phase 2 Enhancements
1. **Tiered Commission**: Lower rates for high-volume agents
2. **Subscription Model**: Monthly fees for reduced commission
3. **Premium Features**: Additional fees for expedited disputes, featured listings
4. **International Payments**: Currency conversion fees
5. **Insurance Products**: Optional task protection for additional fee

### Metrics to Track
1. Average platform fee per task
2. Dispute rate and resolution time
3. Fee collection success rate
4. User satisfaction with pricing transparency
5. Agent retention vs. commission rate

## Implementation Priority
1. **Week 1**: Basic commission calculation and collection
2. **Week 2**: Escrow system and payment flow
3. **Week 3**: Dispute initiation and evidence submission
4. **Week 4**: Admin resolution interface
5. **Week 5**: Receipt generation and audit logging

---

*These decisions establish VibeMatch's sustainable business model while maintaining competitive rates and building user trust through transparency and fair dispute resolution.*