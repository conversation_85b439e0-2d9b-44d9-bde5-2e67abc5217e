# Decision: Agent Analytics.View.Own Permission

## Decision Record
- **Decision ID**: DEC-014
- **Date**: 2025-06-20
- **Status**: Approved
- **Category**: Security / Access Control

## Context

During the Day 4 security audit of the RBAC matrix, it was discovered that agents had no analytics permissions whatsoever:
- Analytics.View.Basic: ✗
- Analytics.View.Detailed: ✗
- Analytics.Export: ✗

This created a significant gap where agents could not view their own performance metrics, earnings data, or task completion statistics.

## Problem Statement

Agents need visibility into their own performance data to:
1. Track their earnings and task completion rates
2. Understand their quality scores and ratings
3. Identify areas for improvement
4. Verify commission calculations
5. Plan their work based on historical performance

Without this access, agents are operating blind and cannot optimize their service delivery.

## Decision

Add a new permission `Analytics.View.Own` to the RBAC matrix with the following access:
- **Agent**: ✓ (can view their own analytics)
- **Agent Manager**: ✓ (can view analytics for agents they manage)
- **Platform Manager**: ✓ (can view all analytics)
- **Admin**: ✓ (full access)
- All other roles: ✗

## Rationale

1. **Business Need**: Agents are independent service providers who need performance visibility
2. **Security**: Limited to own data only, preventing competitive intelligence gathering
3. **Compliance**: Supports transparency requirements for gig economy workers
4. **User Experience**: Enables self-service performance tracking
5. **Precedent**: Similar to `Audit.Read.Own` which all roles already have

## Implementation Details

### Scope of Analytics.View.Own
Agents with this permission can view:
- Personal task completion rates
- Average ratings and quality scores
- Earnings by time period
- Response time metrics
- Capability utilization rates
- Commission breakdowns

Agents CANNOT view:
- Other agents' data
- Platform-wide statistics
- User identities or details
- Competitive pricing information
- System performance metrics

### Technical Implementation
```typescript
// Example permission check
if (hasPermission('Analytics.View.Own') && resourceOwner === currentUser.id) {
  return analyticsData.filter(data => data.agentId === currentUser.id);
}
```

## Alternatives Considered

1. **Grant Analytics.View.Basic**: Rejected - too broad, includes platform data
2. **No analytics access**: Rejected - poor user experience, transparency issues
3. **Custom reports only**: Rejected - not self-service, operational overhead

## Consequences

### Positive
- Improved agent satisfaction and retention
- Self-service reduces support tickets
- Better agent performance through data-driven insights
- Compliance with transparency regulations

### Negative
- Additional development effort for filtered views
- Potential for data scraping (mitigated by rate limiting)
- Storage for per-agent analytics data

## Review and Approval

- **Proposed by**: Requirements Remediation Team
- **Reviewed by**: Security Team, Product Team
- **Approved by**: Platform Manager role (simulated)
- **Implementation Priority**: High (Phase 1)

---

*This decision addresses the gap identified in the Day 4 security audit and ensures agents have appropriate visibility into their own performance data while maintaining security boundaries.*