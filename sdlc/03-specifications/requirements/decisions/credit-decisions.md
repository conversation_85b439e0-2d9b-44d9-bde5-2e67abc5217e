# Credit System and Fraud Detection Decisions

## Document Information
- **Date**: 2025-06-20
- **Status**: Approved
- **Decision Makers**: Product Team, Finance Team, Security Team, Risk Management

## CREDIT-003: Fraud Detection Decisions

### Q1: Should we integrate with external fraud detection services?
**Decision**: NO for MVP, YES for Phase 2
- **MVP**: Build internal rule-based system with velocity checks and pattern detection
- **Phase 2**: Integrate with specialized services (e.g., Stripe Radar, Sift)
- **Rationale**: Start with controllable internal system, add sophistication as volume grows

### Q2: What is the acceptable false positive rate for the business?
**Decision**: Target 2% false positive rate
- **Metric**: Max 2 false blocks per 100 legitimate transactions
- **Review process**: Manual review queue for blocked transactions
- **User experience**: Clear appeal process with 24-hour resolution SLA
- **Rationale**: Balances security with user experience; industry standard is 1-3%

### Q3: How should we handle cross-border transactions?
**Decision**: Allow with enhanced monitoring
- **Implementation**: Additional risk score weight (+0.2) for international transactions
- **Monitoring**: Flag for manual review if risk score > 0.7
- **Future**: Add country-specific risk profiles in Phase 2
- **Rationale**: Support global marketplace while managing increased risk

### Q4: Should fraud scores affect user reputation permanently?
**Decision**: Temporary impact with decay
- **High risk events**: 90-day impact on trust score
- **Medium risk events**: 30-day impact
- **Cleared false positives**: No impact, expunged from record
- **Repeat offenders**: Escalating penalties, potential permanent ban
- **Rationale**: Allows rehabilitation while protecting platform

## CREDIT-004: Transaction Audit Trail Decisions

### Q1: Should we use Google Cloud Audit Logs integration?
**Decision**: YES - Primary audit mechanism
- **Implementation**: Cloud Audit Logs for infrastructure events
- **Application logs**: Custom audit trail in Firestore for business events
- **Integration**: Export to BigQuery for analysis
- **Rationale**: Leverages GCP native capabilities with custom business logic

### Q2: Do we need real-time streaming of audit events?
**Decision**: YES for high-risk events only
- **Real-time**: Fraud alerts, large transactions (>10,000 credits), role changes
- **Batch (5-min)**: Standard transactions, access logs
- **Daily batch**: Reports, analytics, compliance exports
- **Rationale**: Balances performance with security monitoring needs

### Q3: What specific compliance standards must we meet?
**Decision**: SOC 2 Type I for launch, Type II within 12 months
- **Primary**: SOC 2 for trust and security
- **Secondary**: GDPR for data protection
- **Future**: PCI DSS when adding payment processing
- **Rationale**: SOC 2 provides broad security coverage for B2B trust

### Q4: Should audit logs be replicated across regions?
**Decision**: NO for MVP, single region with backups
- **Primary**: Single region (us-central1) with high availability
- **Backup**: Daily exports to Cloud Storage with cross-region replication
- **Disaster recovery**: 24-hour RPO, 4-hour RTO
- **Rationale**: Simplifies compliance while maintaining data durability

## CREDIT-005: Suspicious Activity Reporting Decisions

### Q1: Should we integrate with external threat intelligence feeds?
**Decision**: NO for MVP
- **MVP**: Build internal pattern library
- **Phase 2**: Consider commercial threat feeds
- **Rationale**: Start with known patterns, expand based on actual threats

### Q2: What SLA for alert investigation and resolution?
**Decision**: Tiered response times
- **Critical (score > 0.9)**: 1-hour initial response
- **High (score 0.7-0.9)**: 4-hour initial response  
- **Medium (score 0.5-0.7)**: 24-hour initial response
- **Low (score < 0.5)**: 72-hour initial response
- **Rationale**: Prioritizes resources on highest risks

### Q3: Do we need 24/7 security monitoring?
**Decision**: NO for MVP, business hours only
- **MVP**: 8am-8pm PT monitoring with on-call for critical alerts
- **Growth trigger**: 24/7 when transaction volume > $1M/month
- **Rationale**: Cost-effective for initial volume levels

### Q4: Should we implement automated playbooks for common scenarios?
**Decision**: YES for specific scenarios
- **Automated**: Velocity limit blocks, duplicate transaction prevention
- **Semi-automated**: Account suspension with review
- **Manual**: High-value transaction reviews, appeals
- **Rationale**: Automates clear-cut cases while maintaining human oversight

## CREDIT-006: Velocity Limits Decisions

### Q1: Should velocity limits be adjustable per user risk profile?
**Decision**: YES with 3 tiers
- **New users (<30 days)**: 50 tx/hour, 200 tx/day, 5,000 credit daily limit
- **Established users**: 100 tx/hour, 500 tx/day, 100,000 credit daily limit
- **Verified business**: 500 tx/hour, 2,000 tx/day, 500,000 credit daily limit
- **Rationale**: Rewards trust while protecting against account takeover

### Q2: How should we handle legitimate business accounts with high volume?
**Decision**: Business verification program
- **Process**: Manual verification with documentation
- **Benefits**: 5x higher limits, dedicated support
- **Requirements**: Business registration, website, history
- **Rationale**: Supports power users while maintaining security

### Q3: Should there be grace periods for first-time violations?
**Decision**: YES with education
- **First violation**: Warning with explanation
- **Second violation**: 24-hour cooling period
- **Third violation**: Account review and potential suspension
- **Rationale**: Educates users while preventing abuse

### Q4: How should we handle time zone differences for daily limits?
**Decision**: UTC-based rolling windows
- **Daily limits**: Rolling 24-hour window (not calendar day)
- **Hourly limits**: Rolling 60-minute window
- **Display**: Show user's local time in UI with UTC reference
- **Rationale**: Fair and consistent globally, prevents gaming

## CREDIT-007: Pattern Detection Decisions

### Q1: Should pattern detection thresholds be dynamic based on user behavior?
**Decision**: YES with baseline learning
- **New users**: Conservative static thresholds
- **After 30 days**: Dynamic thresholds based on behavior
- **Calculation**: 3x standard deviation from user's baseline
- **Rationale**: Reduces false positives for established users

### Q2: How should we handle seasonal or business-specific transaction patterns?
**Decision**: Profile-based adjustments
- **Implementation**: Tag users with business type
- **Seasonal adjustments**: E-commerce (holidays), Tax prep (Q1), etc.
- **Learning period**: 12 months to establish patterns
- **Rationale**: Accommodates legitimate business cycles

### Q3: Should patterns be weighted differently based on transaction amounts?
**Decision**: YES with logarithmic scaling
- **Small (<100 credits)**: Weight = 1.0
- **Medium (100-1,000)**: Weight = 1.5
- **Large (1,000-10,000)**: Weight = 2.0
- **Very large (>10,000)**: Weight = 3.0
- **Rationale**: Higher amounts pose greater risk

### Q4: How can we incorporate external fraud intelligence feeds?
**Decision**: Deferred to Phase 2
- **MVP**: Internal patterns only
- **Phase 2**: API integration framework for threat feeds
- **Rationale**: Focus on core patterns first

## CREDIT-008: Automated Response Decisions

### Q1: Should response thresholds be adjustable per user segment?
**Decision**: YES based on trust tiers
- **Implementation**: Same 3 tiers as velocity limits
- **New users**: Conservative thresholds (block at 0.7)
- **Established**: Standard thresholds (block at 0.9)
- **Business verified**: Relaxed thresholds (block at 0.95)
- **Rationale**: Balances risk with user experience

### Q2: How long should automatic suspensions last by default?
**Decision**: Graduated durations
- **First suspension**: 24 hours
- **Second suspension**: 7 days
- **Third suspension**: 30 days
- **Fourth+**: Permanent pending appeal
- **Rationale**: Escalating consequences deter repeat offenses

### Q3: What information should be provided to users about automated actions?
**Decision**: Transparent but secure
- **Provide**: Action taken, general reason category, appeal process
- **Don't provide**: Specific rules triggered, risk scores, detection methods
- **Example**: "Transaction blocked due to unusual activity pattern"
- **Rationale**: Informs users without revealing security measures

### Q4: Should there be escalation procedures for repeated false positives?
**Decision**: YES with whitelist capability
- **3 false positives**: Automatic escalation to senior review
- **Review outcome**: Possible whitelist for specific patterns
- **Duration**: 90-day whitelist periods with renewal
- **Rationale**: Improves system accuracy and user experience

## CREDIT-009: Fraud Audit Trail Decisions

### Q1: Should audit logs be stored on-premises or in cloud storage?
**Decision**: Cloud-native storage
- **Primary**: Firestore for hot data (90 days)
- **Archive**: BigQuery for analysis and long-term storage
- **Backup**: Cloud Storage for compliance archives
- **Rationale**: Leverages GCP managed services for reliability

### Q2: What level of detail should be included in exported reports?
**Decision**: Role-based detail levels
- **Compliance Officer**: Full detail including PII
- **Fraud Manager**: Anonymized but complete
- **Platform Manager**: Aggregated metrics only
- **Rationale**: Principle of least privilege for data access

### Q3: How should we handle audit log queries across archived data?
**Decision**: Tiered query system
- **Hot (90 days)**: Real-time queries via API
- **Warm (1 year)**: BigQuery with 1-minute response
- **Cold (1-7 years)**: Request-based with 24-hour SLA
- **Rationale**: Balances cost with accessibility

### Q4: Should there be role-based access controls for different audit data?
**Decision**: YES - Granular permissions
- **Implementation**: Extend RBAC model to audit data
- **Permissions**: View own, view team, view all
- **Special**: Break-glass emergency access with logging
- **Rationale**: Protects sensitive data while enabling investigation

## CREDIT-010: Admin Dashboard Decisions

### Q1: Should the dashboard support mobile/tablet access?
**Decision**: Responsive web only for MVP
- **MVP**: Responsive web design for tablets/mobile
- **Phase 2**: Dedicated mobile app if needed
- **Rationale**: Cost-effective while supporting mobile use cases

### Q2: What level of customization should be available for individual admins?
**Decision**: Pre-built views with limited customization
- **Options**: Choose from 5 pre-built dashboards
- **Customization**: Show/hide widgets, date ranges
- **Personal**: Save 3 custom view configurations
- **Rationale**: Balances flexibility with maintenance

### Q3: Should there be automated case assignment based on workload?
**Decision**: Round-robin with skills matching
- **Default**: Round-robin assignment to available analysts
- **Skills**: Route specific fraud types to specialists
- **Load balancing**: Max 20 active cases per analyst
- **Rationale**: Fair distribution with expertise matching

### Q4: How should we handle dashboard access during system maintenance?
**Decision**: Read-only fallback mode
- **Implementation**: Static snapshot updated hourly
- **Features**: View-only access to last known state
- **Duration**: Available during planned maintenance
- **Rationale**: Maintains visibility during downtime

## Implementation Priority
1. Velocity limits and basic pattern detection (Week 1)
2. Audit trail and compliance logging (Week 1-2)
3. Automated responses and case management (Week 2)
4. Admin dashboard and reporting (Week 3)
5. Advanced patterns and ML preparation (Week 4)