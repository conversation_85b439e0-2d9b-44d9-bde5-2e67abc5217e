# VibeMatch Requirements Glossary

This glossary defines canonical terms used throughout the VibeMatch requirements documentation to ensure consistency and clarity.

## Core Entities

### Agent

**Definition**: A user who has registered as a service provider on the platform and can be matched to tasks.
**Usage**: Always capitalize when referring to the role. Use lowercase when referring to generic agents.
**Related**: Agent Profile, Agent Rating, Agent Performance

### Task

**Definition**: A unit of work posted by a user that requires one or more agents to complete.
**Usage**: Always capitalize when referring to the entity.
**States**: draft, open, matched, in_progress, completed, cancelled
**Related**: Task Requirements, Task Budget, Task Deadline

### User

**Definition**: Any authenticated person using the platform, regardless of role.
**Usage**: Always capitalize when referring to the role.
**Roles**: User, Agent, Moderator, Support, Admin, Finance, Compliance, Security, SuperAdmin

### Match

**Definition**: The pairing of one or more agents to a task based on capability alignment and availability.
**Usage**: Use "matched" (not "assigned") when an agent is selected for a task.
**Related**: Match Score, Match Suggestion, Multi-Agent Match

## Economic Terms

### Credit

**Definition**: The platform's internal currency used for all transactions.
**Usage**: Always singular (1 credit, 1000 credits). Never use "Credit(s)".
**Representation**: Integer value where 1 credit = $0.01 USD

### Commission

**Definition**: The 15% fee taken by the platform from each completed task payment.
**Usage**: Always specify as "platform commission" for clarity.

### Transaction

**Definition**: Any movement of credits between accounts, including payments, refunds, and commissions.
**Usage**: Always include transaction type (payment, refund, commission, purchase).

## Technical Terms

### API

**Definition**: Application Programming Interface - the REST endpoints exposed by services.
**Usage**: Always uppercase. Specify version when relevant (e.g., API v1).

### Endpoint

**Definition**: A specific URL path that handles API requests.
**Usage**: Always include HTTP method (e.g., GET /api/v1/tasks).

### Token

**Definition**: A cryptographic string used for authentication and authorization.
**Types**: ID Token (1-hour expiry), Refresh Token (30-day expiry)

### RBAC

**Definition**: Role-Based Access Control - the permission system used throughout the platform.
**Usage**: Always use the acronym after first mention.

## Performance Terms

### Response Time

**Definition**: The time from request receipt to response delivery.
**Measurement**: Always specify percentile (p50, p95, p99).
**Target**: <100ms (p99), <50ms (p95)

### Throughput

**Definition**: The number of requests the system can handle per unit time.
**Measurement**: Requests per second (RPS) or requests per minute (RPM).

### Availability

**Definition**: The percentage of time the system is operational and accessible.
**Target**: 99.9% (three nines)

## Security Terms

### Authentication

**Definition**: The process of verifying a user's identity.
**Methods**: Email/password, OAuth 2.0, TOTP-based MFA

### Authorization

**Definition**: The process of determining what an authenticated user can access.
**Implementation**: Firebase custom claims, RBAC permissions

### MFA

**Definition**: Multi-Factor Authentication - requiring multiple verification methods.
**Implementation**: TOTP-based (Time-based One-Time Password)

## Process Terms

### Orchestration

**Definition**: The coordination of multiple agents working on different parts of a single task.
**Modes**: Sequential, Parallel
**Related**: Task Decomposition, Step Progress

### Validation

**Definition**: The process of ensuring data meets specified criteria before processing.
**Types**: Input validation, business rule validation, schema validation

### Audit Trail

**Definition**: An immutable record of all significant system events and transactions.
**Requirements**: Cryptographically signed, time-stamped, user-attributed

## State Transitions

### Task States

- **draft**: Initial state when task is created
- **open**: Task is published and available for matching
- **matched**: Agent(s) have been selected for the task
- **in_progress**: Work has begun on the task
- **completed**: Task deliverables have been submitted and accepted
- **cancelled**: Task was terminated before completion

### Agent States

- **active**: Agent is available for matching
- **busy**: Agent is working on maximum allowed tasks
- **inactive**: Agent has paused availability
- **suspended**: Agent access temporarily revoked

## Consistency Rules

1. **Capitalization**:
   - Roles: Always capitalize (User, Agent, Admin)
   - Entities: Capitalize when referring to system entities (Task, Match)
   - Technical terms: Follow industry standards (API, URL, REST)

2. **Tense and Voice**:
   - Requirements: Use "shall" for mandatory requirements
   - User stories: Use present tense, active voice
   - Documentation: Use imperative mood for instructions

3. **Measurements**:
   - Time: Use milliseconds (ms) for sub-second, seconds (s) otherwise
   - Currency: Always specify credits as integers
   - Percentages: Use decimal (0.15) in code, percentage (15%) in docs

4. **Abbreviations**:
   - Always define on first use
   - Use consistently after definition
   - Maintain a standard set (RBAC, API, MFA, TOTP, SLA)

## Common Mistakes to Avoid

1. **Assigned vs Matched**: Always use "matched" when referring to agent-task pairing
2. **Credits vs Credit**: Always use "credits" as plural, never "credit(s)"
3. **Percent vs Percentage**: Use "percentage" as noun, "percent" with numbers
4. **Setup vs Set up**: "Setup" is noun, "set up" is verb
5. **Login vs Log in**: "Login" is noun, "log in" is verb

## Version History

- v1.0.0 (2025-01-20): Initial glossary created
- Last updated: 2025-01-20
