# User Service Contract

> **Service**: User Management  
> **Version**: 1.0.0  
> **Owner**: User Team  
> **Last Updated**: 2025-06-26

## Service Overview

The User Management service handles all user-related operations including registration, authentication, profile management, and user data access.

## Service Level Agreement

| Metric | Target |
|--------|--------|
| Availability | 99.9% |
| Response Time (p99) | 100ms |
| Error Rate | <0.1% |
| Throughput | 1000 RPS |

## API Endpoints

### Create User
```yaml
method: POST
path: /api/users
description: Register a new user
authentication: Optional (for initial registration)
request:
  body:
    type: object
    required: [email, password, displayName]
    properties:
      email:
        type: string
        format: email
      password:
        type: string
        minLength: 8
      displayName:
        type: string
        minLength: 2
        maxLength: 50
response:
  201:
    body:
      $ref: '#/definitions/UserDTO'
  400:
    body:
      $ref: '#/definitions/ValidationError'
  409:
    body:
      $ref: '#/definitions/ConflictError'
```

### Get User by ID
```yaml
method: GET
path: /api/users/{userId}
description: Retrieve user details by ID
authentication: Required
request:
  params:
    userId:
      type: string
      pattern: '^usr_[a-zA-Z0-9]{20}$'
response:
  200:
    body:
      $ref: '#/definitions/UserDTO'
  404:
    body:
      $ref: '#/definitions/NotFoundError'
```

### Update User
```yaml
method: PATCH
path: /api/users/{userId}
description: Update user profile
authentication: Required (user or admin)
request:
  params:
    userId:
      type: string
  body:
    type: object
    properties:
      displayName:
        type: string
      preferences:
        type: object
response:
  200:
    body:
      $ref: '#/definitions/UserDTO'
  403:
    body:
      $ref: '#/definitions/ForbiddenError'
```

### Delete User
```yaml
method: DELETE
path: /api/users/{userId}
description: Delete user account (soft delete)
authentication: Required (user or admin)
request:
  params:
    userId:
      type: string
  body:
    type: object
    required: [reason]
    properties:
      reason:
        type: string
        enum: [user_request, admin_action, policy_violation]
response:
  204:
    description: User deleted successfully
  403:
    body:
      $ref: '#/definitions/ForbiddenError'
```

### List Users (Admin)
```yaml
method: GET
path: /api/users
description: List users with pagination and filtering
authentication: Required (admin only)
request:
  query:
    page:
      type: integer
      default: 1
    pageSize:
      type: integer
      default: 20
      max: 100
    filter:
      type: string
      description: JSON filter object
    sort:
      type: string
      default: createdAt:desc
response:
  200:
    body:
      $ref: '#/definitions/UserListResponse'
```

## Data Models

### UserDTO
```typescript
interface UserDTO {
  id: string;                    // usr_[20 chars]
  email: string;
  displayName: string;
  role: 'user' | 'agent' | 'admin';
  status: 'active' | 'suspended' | 'deleted';
  preferences: {
    notifications: boolean;
    language: string;
    timezone: string;
  };
  createdAt: string;            // ISO 8601
  updatedAt: string;            // ISO 8601
  lastLoginAt?: string;         // ISO 8601
}
```

### UserListResponse
```typescript
interface UserListResponse {
  data: UserDTO[];
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
  };
  links: {
    first: string;
    prev?: string;
    next?: string;
    last: string;
  };
}
```

## Error Responses

### Standard Error Format
```typescript
interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  requestId: string;
}
```

### Error Codes
| Code | HTTP Status | Description |
|------|-------------|-------------|
| `USER_NOT_FOUND` | 404 | User does not exist |
| `EMAIL_ALREADY_EXISTS` | 409 | Email already registered |
| `INVALID_CREDENTIALS` | 401 | Wrong email/password |
| `INSUFFICIENT_PERMISSIONS` | 403 | Not authorized |
| `VALIDATION_ERROR` | 400 | Invalid input data |
| `RATE_LIMITED` | 429 | Too many requests |

## Events Published

### user.created
```typescript
{
  type: 'user.created',
  timestamp: string,
  data: {
    userId: string,
    email: string,
    displayName: string,
    role: string
  }
}
```

### user.updated
```typescript
{
  type: 'user.updated',
  timestamp: string,
  data: {
    userId: string,
    changes: Record<string, any>
  }
}
```

### user.deleted
```typescript
{
  type: 'user.deleted',
  timestamp: string,
  data: {
    userId: string,
    reason: string,
    deletedBy: string
  }
}
```

## Events Consumed

- `task.completed` - Update user statistics
- `billing.credits_purchased` - Update user tier
- `agent.verified` - Update user role to agent

## Dependencies

### Internal Services
- **Authentication Service**: Token validation
- **Billing Service**: Credit balance checks
- **Notification Service**: Email notifications

### External Services
- **Google Identity Platform**: OAuth authentication
- **SendGrid**: Email delivery

## Rate Limits

| Endpoint | Limit | Window |
|----------|-------|--------|
| POST /users | 5 | 1 hour |
| GET /users/{id} | 100 | 1 minute |
| PATCH /users/{id} | 10 | 1 minute |
| DELETE /users/{id} | 1 | 1 hour |

## Migration Notes

### Version 1.0.0
- Initial release
- Migrated from monolithic user table
- Added event publishing
- Implemented soft delete

---
**Next Review**: 2025-09-26  
**Contract Status**: Active  
**Consumers**: All services