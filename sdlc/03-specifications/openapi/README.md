# OpenAPI Specifications

> **Purpose**: REST API specifications in OpenAPI 3.0 format  
> **Audience**: API developers and consumers

## Overview
OpenAPI 3.0 specifications for all VibeMatch REST APIs. These specs serve as the single source of truth for API contracts and are used to generate documentation, client SDKs, and mock servers.

## API Specifications
### Core Services
- **gateway-api.yaml** - API Gateway endpoints
- **auth-service-api.yaml** - Authentication APIs
- **user-service-api.yaml** - User management
- **agent-service-api.yaml** - Agent operations
- **task-service-api.yaml** - Task management
- **credit-service-api.yaml** - Credit system
- **orchestration-api.yaml** - Multi-agent workflows

### Supporting Services
- **admin-api.yaml** - Admin platform
- **analytics-api.yaml** - Analytics endpoints
- **notification-api.yaml** - Notifications
- **compliance-api.yaml** - GDPR/compliance

## Specification Standards
### File Structure
```yaml
openapi: 3.0.3
info:
  title: Service Name API
  version: 1.0.0
  description: Service description
servers:
  - url: https://api.vibematch.com/v1
paths:
  /resource:
    get:
      summary: Get resources
      operationId: getResources
      tags: [Resources]
      responses:
        200:
          description: Success
```

### Common Components
- Standard error responses
- Pagination parameters
- Authentication schemes
- Common data models

## Development Workflow
1. **Design** - API design in YAML
2. **Review** - Team review via PR
3. **Mock** - Generate mock server
4. **Implement** - Build actual API
5. **Validate** - Test against spec
6. **Document** - Auto-generate docs

## Tools
- **Editor**: Stoplight Studio
- **Validation**: OpenAPI validator
- **Mocking**: Prism mock server
- **Documentation**: Redoc/Swagger UI
- **SDK Generation**: OpenAPI Generator

---
**Section Owner**: API Team  
**Last Updated**: 2025-06-26  
**Parent**: [Specifications](../)