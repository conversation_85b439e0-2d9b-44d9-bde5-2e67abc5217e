# Environment Management

> **Purpose**: Configuration and management of deployment environments  
> **Audience**: DevOps team and release managers

## Overview
Environment-specific configurations and management procedures for VibeMatch's development, staging, and production environments.

## Environment Overview
### Development (dev)
- **Purpose**: Active development and testing
- **URL**: dev.vibematch.com
- **Refresh**: Daily from main branch
- **Data**: Synthetic test data
- **Scale**: Minimal resources

### Staging (staging)
- **Purpose**: Pre-production validation
- **URL**: staging.vibematch.com
- **Refresh**: On-demand from release branch
- **Data**: Production-like dataset
- **Scale**: 50% of production

### Production (prod)
- **Purpose**: Live customer environment
- **URL**: api.vibematch.com
- **Refresh**: Controlled releases only
- **Data**: Real customer data
- **Scale**: Auto-scaling enabled

## Environment Configuration
### Key Differences
| Setting | Dev | Staging | Production |
|---------|-----|---------|------------|
| Logging | Debug | Info | Warning |
| Monitoring | Basic | Full | Full + Alerts |
| Backup | None | Daily | Continuous |
| SSL | Self-signed | Valid | Valid + WAF |
| Rate Limits | None | Moderate | Strict |

### Environment Variables
```bash
# Common across environments
NODE_ENV=[development|staging|production]
LOG_LEVEL=[debug|info|warn|error]

# Environment-specific
DATABASE_URL=projects/$PROJECT_ID/databases/(default)
API_URL=https://[env].vibematch.com
ENABLE_ANALYTICS=[false|true|true]
```

## Promotion Process
1. **Dev → Staging**
   - Automated on PR merge
   - Run integration tests
   - Verify configurations

2. **Staging → Production**
   - Manual approval required
   - Full regression testing
   - Staged rollout (canary)

## Access Control
- **Dev**: All developers
- **Staging**: Senior devs + QA
- **Production**: SRE team only

---
**Section Owner**: Platform Team  
**Last Updated**: 2025-06-26  
**Parent**: [Deployment](../)