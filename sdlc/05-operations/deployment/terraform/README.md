# Terraform Infrastructure

> **Purpose**: Infrastructure as Code for GCP resources  
> **Audience**: DevOps engineers and platform team

## Overview
Terraform configurations defining all Google Cloud Platform infrastructure for VibeMatch. These configs ensure reproducible, version-controlled infrastructure deployment.

## Module Structure
```
terraform/
├── modules/           # Reusable modules
│   ├── networking/   # VPC, subnets, firewall
│   ├── compute/      # Cloud Run, GKE
│   ├── storage/      # GCS, Firestore
│   ├── security/     # IAM, KMS
│   └── monitoring/   # Logging, metrics
├── environments/     # Environment configs
│   ├── dev/
│   ├── staging/
│   └── production/
└── main.tf          # Root configuration
```

## Key Resources
### Compute
- Cloud Run services
- GKE cluster (future)
- Cloud Functions

### Storage
- Firestore database
- Cloud Storage buckets
- Memorystore (Redis)

### Networking
- VPC networks
- Cloud Load Balancer
- Cloud CDN
- Cloud Armor

### Security
- Identity Platform
- Secret Manager
- KMS encryption
- VPC Service Controls

## Usage
### Initial Setup
```bash
# Initialize Terraform
terraform init

# Plan changes
terraform plan -var-file=environments/dev/terraform.tfvars

# Apply changes
terraform apply -var-file=environments/dev/terraform.tfvars
```

### Best Practices
- Use remote state in GCS
- Enable state locking
- Separate environments
- Use modules for reusability
- Tag all resources

## Environment Variables
```hcl
project_id = "vibematch-dev"
region = "us-central1"
environment = "dev"
```

---
**Section Owner**: Platform Team  
**Last Updated**: 2025-06-26  
**Parent**: [Deployment](../)