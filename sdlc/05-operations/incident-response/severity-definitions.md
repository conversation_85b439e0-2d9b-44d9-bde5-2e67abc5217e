# Incident Severity Definitions

> **Purpose**: Standardize incident severity classification  
> **Last Updated**: 2025-06-26

## Severity Levels

### SEV1 - Critical Outage
**Definition**: Complete service outage or data loss affecting all users

**Criteria** (any of):
- API Gateway completely unavailable
- Authentication service down
- Complete database failure
- Data corruption or loss
- Security breach detected

**Response**:
- Page primary and secondary on-call immediately
- Incident Commander assigned within 15 minutes
- Executive team notified
- Status page updated every 30 minutes
- War room opened

**Examples**:
- "All API requests returning 503"
- "Cannot access Firestore from any service"
- "Unauthorized data access detected"

### SEV2 - Major Degradation
**Definition**: Significant functionality impaired for many users

**Criteria** (any of):
- Core feature unavailable (matching, payments)
- Performance degraded >50%
- Partial data unavailability
- Third-party integration failure (Stripe)

**Response**:
- Page primary on-call
- Incident Commander for extended issues
- Status page updated
- Stakeholders notified via Slack

**Examples**:
- "Payment processing failing for 80% of transactions"
- "Task matching taking >30 seconds"
- "Agent profiles not loading"

### SEV3 - Minor Issue
**Definition**: Limited impact affecting subset of users or features

**Criteria** (any of):
- Non-critical feature unavailable
- Performance degraded 10-50%
- Errors affecting <10% of requests
- UI/UX issues

**Response**:
- Notify on-call via Slack
- Fix during business hours
- Status page updated if user-facing

**Examples**:
- "Export feature returning errors"
- "Analytics dashboard slow"
- "Some notification emails delayed"

### SEV4 - Low Priority
**Definition**: Minimal impact, cosmetic issues

**Criteria** (any of):
- Cosmetic UI issues
- Non-critical bugs
- Documentation errors
- Minor inconsistencies

**Response**:
- Create ticket in backlog
- Fix in next sprint
- No immediate action required

**Examples**:
- "Button alignment off on mobile"
- "Typo in error message"
- "Old logo in email footer"

## Escalation Matrix

| Severity | Initial Response | Escalation (if >1hr) | Communication |
|----------|-----------------|---------------------|---------------|
| SEV1 | Primary + Secondary | CTO + VP Eng | Public status + Email |
| SEV2 | Primary On-call | Secondary + Lead | Public status |
| SEV3 | Primary On-call | Team Lead | Internal only |
| SEV4 | Next business day | None | Ticket only |

## Automatic Severity Triggers

### Monitoring Alerts → Severity
- Error rate >50% for 5 min → SEV2
- Error rate >90% for 2 min → SEV1
- Response time >5s for 10 min → SEV3
- Response time >10s for 5 min → SEV2
- Database connection failures → SEV1
- Payment failures >25% → SEV2

## Severity Changes

### Upgrading Severity
Increase severity if:
- Impact spreads to more users
- Resolution taking longer than expected
- Root cause indicates bigger issue
- Customer complaints increasing

### Downgrading Severity
Decrease severity if:
- Mitigation in place reducing impact
- Affected users < initial assessment
- Workaround available

## Decision Tree

```mermaid
graph TD
    A[Incident Detected] --> B{Users Affected?}
    B -->|All Users| C{Service Available?}
    B -->|Many Users| D{Core Feature?}
    B -->|Some Users| E{Feature Type?}
    B -->|Few Users| F[SEV4]
    
    C -->|No| G[SEV1]
    C -->|Degraded| H[SEV2]
    
    D -->|Yes| I[SEV2]
    D -->|No| J[SEV3]
    
    E -->|Critical| K[SEV3]
    E -->|Non-critical| L[SEV4]
    
    style G fill:#ff0000,color:#fff
    style H fill:#ff9800,color:#fff
    style I fill:#ff9800,color:#fff
    style J fill:#ffeb3b,color:#000
    style K fill:#ffeb3b,color:#000
    style F fill:#4caf50,color:#fff
    style L fill:#4caf50,color:#fff
```

---
**Document Owner**: SRE Team  
**Review Cycle**: Quarterly  
**Parent**: [./](./)