# Operations

> **Purpose**: Deployment, monitoring, and operational procedures  
> **Audience**: DevOps engineers, SREs, and operations teams

## Overview
Comprehensive operational documentation covering deployment strategies, CI/CD pipelines, monitoring architecture, disaster recovery plans, and incident response procedures for maintaining VibeMatch in production.

## Directory Structure
```
05-operations/
├── deployment/       # Deployment strategies, Kubernetes, Terraform
├── monitoring/       # Observability, alerting, and dashboards
├── ci-cd/           # Continuous integration and deployment pipelines
├── disaster-recovery/ # DR plans and procedures
├── incident-response/ # Incident handling and escalation
└── runbooks/        # Operational procedures and playbooks
```

## Quick Links
### Most Used Documents
- [Kubernetes Configuration](./deployment/kubernetes/) - K8s deployment manifests
- [Monitoring Architecture](./monitoring/) - Observability setup
- [Cloud Architecture](./deployment/cloud-architecture.md) - GCP infrastructure design
- [Deployment DevOps Architecture](./deployment/deployment-devops-architecture.md) - Full DevOps strategy

### By Topic
- **Infrastructure**: [Terraform](./deployment/terraform/), [Kubernetes](./deployment/kubernetes/)
- **Monitoring**: [Metrics](./monitoring/metrics-architecture.md), [Dashboards](./monitoring/dashboards/)
- **Deployment**: [Strategies](./deployment/strategies/), [Environments](./deployment/environments/)
- **Reliability**: [Disaster Recovery](./disaster-recovery/), [Incident Response](./incident-response/)

## How to Use This Section
### For DevOps Engineers
1. Review [Cloud Architecture](./deployment/cloud-architecture.md)
2. Set up infrastructure with [Terraform](./deployment/terraform/)
3. Deploy services using [Kubernetes](./deployment/kubernetes/)
4. Configure [Monitoring](./monitoring/)

### For On-Call Engineers
- [Runbooks](./runbooks/) for common procedures
- [Incident Response](./incident-response/) for escalation
- [Monitoring Dashboards](./monitoring/dashboards/) for system health

---
**Section Owner**: Platform Team  
**Last Updated**: 2025-06-26  
**Parent**: [SDLC Root](../)