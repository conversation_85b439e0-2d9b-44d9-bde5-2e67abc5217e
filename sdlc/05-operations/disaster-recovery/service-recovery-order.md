# Service Recovery Order

> **Purpose**: Define the correct sequence for service recovery during DR  
> **Critical**: Services must be recovered in this order to avoid cascading failures

## Recovery Sequence

### Phase 1: Infrastructure (0-15 min)
Essential infrastructure that all services depend on.

```mermaid
graph TD
    A[1. Networking/VPC] --> B[2. Firestore]
    B --> C[3. Redis Cache]
    C --> D[4. Pub/Sub Topics]
    D --> E[5. Cloud Storage]
    
    style A fill:#ff5252,color:#fff
    style B fill:#ff5252,color:#fff
```

**Validation**: 
- VPC connectivity established
- Database responding to health checks
- Cache cluster formed
- Topics accepting messages

### Phase 2: Core Services (15-45 min)
Authentication and user management must come first.

```mermaid
graph TD
    A[6. User Management] --> B[7. Authentication Service]
    B --> C[8. API Gateway]
    C --> D[9. Credit System]
    
    style A fill:#ff9800,color:#fff
    style B fill:#ff9800,color:#fff
    style C fill:#ff9800,color:#fff
    style D fill:#ff9800,color:#fff
```

**Validation**:
- User service: Can retrieve user profiles
- Auth service: Can validate tokens
- API Gateway: Health endpoint responding
- Credit System: Balance queries working

### Phase 3: Business Services (45-90 min)
Core business functionality in dependency order.

```mermaid
graph TD
    A[10. Agent Management] --> B[11. Task Management]
    B --> C[12. Matching Engine]
    C --> D[13. Orchestration Engine]
    D --> E[14. Billing Service]
    
    style A fill:#ffc107,color:#000
    style B fill:#ffc107,color:#000
    style C fill:#ffc107,color:#000
```

**Validation**:
- Agent profiles accessible
- Tasks can be created/retrieved
- Matching algorithm responding
- Orchestration workflows executing
- Payment processing functional

### Phase 4: Supporting Services (90-120 min)
Analytics and administrative functions.

```mermaid
graph TD
    A[15. Analytics Service] --> B[16. Admin Platform]
    B --> C[17. Compliance Service]
    C --> D[18. Notification Service]
    
    style A fill:#4caf50,color:#fff
    style B fill:#4caf50,color:#fff
```

## Service Dependencies

### Critical Dependencies
Each service's hard dependencies that must be running first:

| Service | Depends On | Validation Check |
|---------|------------|------------------|
| API Gateway | Auth Service, User Management | Token validation works |
| Credit System | User Management, Firestore | Balance queries return |
| Matching Engine | Agent Management, Task Management | Can retrieve agents/tasks |
| Orchestration | All core services | Can create orchestration |
| Billing | Credit System, User Management | Can process transactions |

### Startup Commands

```bash
# Phase 1: Infrastructure validation
./scripts/validate-infrastructure.sh

# Phase 2: Core services
kubectl apply -f services/user-management/
kubectl apply -f services/authentication/
kubectl apply -f services/api-gateway/
kubectl apply -f services/credit-system/

# Wait for readiness
kubectl wait --for=condition=ready pod -l tier=core --timeout=300s

# Phase 3: Business services  
kubectl apply -f services/agent-management/
kubectl apply -f services/task-management/
kubectl apply -f services/matching-engine/
kubectl apply -f services/orchestration-engine/
kubectl apply -f services/billing-service/

# Phase 4: Supporting services
kubectl apply -f services/analytics-service/
kubectl apply -f services/admin-platform/
kubectl apply -f services/compliance-service/
```

## Validation Scripts

### Health Check Loop
```bash
#!/bin/bash
# Check all services are healthy

SERVICES=(
  "user-management"
  "authentication" 
  "api-gateway"
  "credit-system"
  "agent-management"
  "task-management"
  "matching-engine"
  "orchestration-engine"
  "billing-service"
)

for service in "${SERVICES[@]}"; do
  echo "Checking $service..."
  kubectl exec -it deploy/$service -- wget -q -O- http://localhost:8080/health
  if [ $? -ne 0 ]; then
    echo "ERROR: $service is not healthy"
    exit 1
  fi
done

echo "All services healthy!"
```

### Smoke Test Sequence
After all services are running:

1. **Authentication Test**: Login with test user
2. **User Profile Test**: Retrieve user data
3. **Agent List Test**: Query available agents
4. **Task Creation Test**: Create simple task
5. **Matching Test**: Verify agent matching
6. **Payment Test**: Process test transaction

## Rollback Procedures

If recovery fails at any phase:

### Phase Failure Actions
- **Phase 1 Failure**: Switch to secondary region
- **Phase 2 Failure**: Restore from snapshot
- **Phase 3 Failure**: Run in degraded mode
- **Phase 4 Failure**: Continue without analytics

### Emergency Contacts
- Infrastructure: SRE on-call
- Database: DBA team
- Security: Security on-call
- Business: Product owner

---
**Document Owner**: SRE Team  
**Last Updated**: 2025-06-26  
**Review**: After each DR test  
**Parent**: [./](./)