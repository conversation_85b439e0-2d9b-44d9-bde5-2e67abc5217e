#!/bin/bash

# Script to find broken links in markdown files
SDLC_DIR="/Users/<USER>/Documents/GitHub/vibe-match/sdlc"
BROKEN_LINKS_FILE="$SDLC_DIR/broken-links.txt"

echo "Checking for broken links in SDLC documentation..."
echo "=============================================="
echo "" > "$BROKEN_LINKS_FILE"

# Function to check if a file or anchor exists
check_link() {
    local file="$1"
    local link="$2"
    local link_path=""
    local anchor=""
    
    # Extract path and anchor
    if [[ "$link" =~ ^([^#]+)(#.+)?$ ]]; then
        link_path="${BASH_REMATCH[1]}"
        anchor="${BASH_REMATCH[2]}"
    else
        link_path="$link"
    fi
    
    # Skip external links
    if [[ "$link_path" =~ ^https?:// ]] || [[ "$link_path" =~ ^mailto: ]]; then
        return 0
    fi
    
    # Calculate absolute path
    local dir=$(dirname "$file")
    local target_path=""
    
    if [[ "$link_path" =~ ^/ ]]; then
        # Absolute path from repo root
        target_path="$SDLC_DIR$link_path"
    else
        # Relative path
        target_path="$dir/$link_path"
    fi
    
    # Normalize path
    target_path=$(cd "$dir" 2>/dev/null && cd "$(dirname "$target_path")" 2>/dev/null && pwd)/$(basename "$target_path")
    
    # Check if file exists
    if [[ ! -f "$target_path" ]]; then
        echo "BROKEN: $file -> $link (File not found: $target_path)" | tee -a "$BROKEN_LINKS_FILE"
        return 1
    fi
    
    # TODO: Check anchor existence
    
    return 0
}

# Find all markdown files
total_files=0
broken_count=0

while IFS= read -r file; do
    ((total_files++))
    # Extract all links from the file
    while IFS= read -r line; do
        # Match markdown links [text](url)
        if [[ "$line" =~ \[([^\]]+)\]\(([^)]+)\) ]]; then
            link="${BASH_REMATCH[2]}"
            if ! check_link "$file" "$link"; then
                ((broken_count++))
            fi
        fi
    done < "$file"
done < <(find "$SDLC_DIR" -name "*.md" -type f)

echo ""
echo "Summary:"
echo "- Total files scanned: $total_files"
echo "- Broken links found: $broken_count"
echo ""
echo "Broken links saved to: $BROKEN_LINKS_FILE"