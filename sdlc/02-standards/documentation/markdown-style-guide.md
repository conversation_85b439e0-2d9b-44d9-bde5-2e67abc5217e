# Markdown Style Guide

> **Purpose**: Ensure consistent markdown formatting across all VibeMatch documentation  
> **Scope**: All `.md` files in repositories, wikis, and documentation  
> **Enforcement**: <PERSON><PERSON>lint, prettier, manual review

## File Structure

### File Naming
- Use lowercase with hyphens: `api-documentation.md`
- Be descriptive: `agent-deployment-guide.md` not `deploy.md`
- Use `.md` extension (not `.markdown`)

### File Organization
```
docs/
├── README.md           # Overview and index
├── getting-started/    # New user guides
├── guides/            # How-to guides
├── reference/         # API references
├── architecture/      # Technical docs
└── troubleshooting/   # Problem solving
```

## Document Structure

### Standard Document Template

```markdown
# Document Title

> **Purpose**: One-line description of document purpose  
> **Audience**: Target readers (developers, users, ops)  
> **Last Updated**: 2025-06-26

Brief overview paragraph explaining what this document covers and why it's important.

## Table of Contents

- [Section 1](#section-1)
  - [Subsection 1.1](#subsection-11)
  - [Subsection 1.2](#subsection-12)
- [Section 2](#section-2)
- [Section 3](#section-3)

## Section 1

Content for section 1...

### Subsection 1.1

Content for subsection...

---
**Owner**: Team Name  
**Review Date**: 2025-09-26
```

## Headings

### Heading Hierarchy

```markdown
# Document Title (H1) - Only one per document

## Major Section (H2) - Main sections

### Subsection (H3) - Detailed topics

#### Minor Subsection (H4) - Rarely used

##### Detail Level (H5) - Avoid if possible

###### Lowest Level (H6) - Never use
```

### Heading Rules
- ✅ **DO**: Use sentence case: `## Getting started with agents`
- ❌ **DON'T**: Use title case: `## Getting Started With Agents`
- ✅ **DO**: Keep headings concise and descriptive
- ❌ **DON'T**: Use punctuation in headings
- ✅ **DO**: Add blank line before and after headings
- ❌ **DON'T**: Use inline formatting in headings

## Text Formatting

### Emphasis

```markdown
*Italic text* for emphasis
**Bold text** for strong emphasis
***Bold italic*** for very strong emphasis (use sparingly)
~~Strikethrough~~ for deprecated content
```

### Lists

#### Unordered Lists
```markdown
- First item
- Second item
  - Nested item
  - Another nested item
- Third item
```

#### Ordered Lists
```markdown
1. First step
2. Second step
   1. Substep A
   2. Substep B
3. Third step
```

#### Task Lists
```markdown
- [x] Completed task
- [ ] Pending task
- [ ] Another pending task
```

### Links

```markdown
# Inline links
[Link text](https://example.com)
[Internal link](./another-file.md)
[Section link](#section-name)

# Reference links
[Link text][reference-id]
[Another link][another-ref]

[reference-id]: https://example.com
[another-ref]: ./another-file.md

# Automatic links
<https://example.com>
<<EMAIL>>
```

### Images

```markdown
# Inline image
![Alt text](./images/diagram.png "Optional title")

# Reference image
![Alt text][image-ref]

[image-ref]: ./images/diagram.png "Optional title"

# Image with link
[![Alt text](./images/thumbnail.png)](https://example.com)
```

## Code

### Inline Code

```markdown
Use `backticks` for inline code, commands like `npm install`, or file names like `package.json`.
```

### Code Blocks

````markdown
```typescript
// Always specify language for syntax highlighting
interface Agent {
  id: string;
  name: string;
  type: AgentType;
}
```

```bash
# Shell commands
npm install
npm test
npm run build
```

```json
{
  "name": "vibematch",
  "version": "1.0.0",
  "dependencies": {}
}
```
````

### Code Block Languages
Common languages to use:
- `typescript` or `ts` - TypeScript code
- `javascript` or `js` - JavaScript code
- `bash` or `sh` - Shell commands
- `json` - JSON data
- `yaml` or `yml` - YAML configuration
- `sql` - SQL queries
- `dockerfile` - Dockerfile content
- `markdown` or `md` - Markdown examples

## Tables

### Basic Table

```markdown
| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |
```

### Aligned Table

```markdown
| Left Aligned | Center Aligned | Right Aligned |
|:-------------|:--------------:|--------------:|
| Left         | Center         | Right         |
| Text         | Text           | Text          |
```

### Complex Table

```markdown
| Feature | Free Tier | Pro Tier | Enterprise |
|---------|:---------:|:--------:|:----------:|
| Agents | 1 | 10 | Unlimited |
| API Calls | 1,000/mo | 100,000/mo | Unlimited |
| Support | Community | Email | 24/7 Phone |
| SLA | - | 99.9% | 99.99% |
| Price | $0 | $99/mo | Custom |
```

## Blockquotes

```markdown
> Single line quote

> Multi-line quote continues here
> and can span multiple lines
> with proper formatting

> **Note**: Important information highlighted

> ⚠️ **Warning**: Critical warning message

> 💡 **Tip**: Helpful suggestion
```

## Alerts and Callouts

### Note/Info
```markdown
> **Note**: This is important information that users should be aware of.
```

### Warning
```markdown
> ⚠️ **Warning**: This action cannot be undone. Please backup your data first.
```

### Danger
```markdown
> 🚨 **Danger**: This will delete all data. Use with extreme caution.
```

### Success
```markdown
> ✅ **Success**: Configuration completed successfully.
```

### Tip
```markdown
> 💡 **Tip**: Use keyboard shortcuts to speed up your workflow.
```

## Horizontal Rules

```markdown
Use three or more hyphens, asterisks, or underscores:

---

***

___
```

## Special Elements

### Diagrams

```markdown
```mermaid
graph TD
    A[Client] -->|Request| B[API Gateway]
    B --> C[Service]
    C --> D[Database]
```
```

### Mathematical Expressions

```markdown
Inline math: $E = mc^2$

Block math:
$$
\sum_{i=1}^{n} x_i = x_1 + x_2 + ... + x_n
$$
```

### Footnotes

```markdown
Here's a sentence with a footnote[^1].

[^1]: This is the footnote content.
```

## Directory Trees

```markdown
```
project/
├── src/
│   ├── index.ts
│   ├── services/
│   │   ├── agent-service.ts
│   │   └── task-service.ts
│   └── utils/
│       └── helpers.ts
├── tests/
│   └── unit/
└── package.json
```
```

## Best Practices

### Line Length
- Keep lines under 100 characters when possible
- Break long sentences into multiple lines
- Use soft wrapping in your editor

### Whitespace
- One blank line between sections
- No trailing whitespace
- Consistent indentation (2 spaces for nested lists)
- Blank line at end of file

### Consistency
- Use the same style throughout a document
- Follow existing patterns in the codebase
- Use American English spelling

### Accessibility
- Always include alt text for images
- Use descriptive link text (not "click here")
- Ensure heading hierarchy is logical
- Consider screen reader users

## Common Patterns

### API Endpoint Documentation

```markdown
## Create Agent

Creates a new agent in the system.

### Endpoint
```
POST /api/v1/agents
```

### Request
```json
{
  "name": "CodeAssistant",
  "type": "code_assistant",
  "capabilities": ["javascript", "typescript"]
}
```

### Response
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "CodeAssistant",
  "type": "code_assistant",
  "status": "active"
}
```

### Error Responses
| Code | Description |
|------|-------------|
| 400 | Invalid request body |
| 401 | Unauthorized |
| 409 | Agent name already exists |
```

### Configuration Documentation

```markdown
## Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `host` | string | `localhost` | Server hostname |
| `port` | number | `3000` | Server port |
| `database.url` | string | - | Database connection URL |
| `redis.host` | string | `localhost` | Redis server host |
| `redis.port` | number | `6379` | Redis server port |

### Example Configuration

```yaml
server:
  host: 0.0.0.0
  port: 8080

database:
  url: postgresql://localhost:5432/vibematch

redis:
  host: redis.example.com
  port: 6379
```
```

### Step-by-Step Guides

```markdown
## How to Deploy an Agent

Follow these steps to deploy a new agent to production:

### Prerequisites
- [ ] Admin access to VibeMatch
- [ ] Agent configuration file
- [ ] Deployment credentials

### Steps

1. **Prepare the agent configuration**
   ```bash
   cp agent-template.yaml my-agent.yaml
   # Edit my-agent.yaml with your settings
   ```

2. **Validate the configuration**
   ```bash
   npm run validate:agent my-agent.yaml
   ```

3. **Deploy to staging**
   ```bash
   npm run deploy:staging my-agent.yaml
   ```

4. **Test in staging**
   - Navigate to https://staging.vibematch.io
   - Verify agent appears in list
   - Run test task

5. **Deploy to production**
   ```bash
   npm run deploy:prod my-agent.yaml
   ```

### Verification
Confirm deployment success:
- [ ] Agent visible in dashboard
- [ ] Health check passing
- [ ] Test task completes successfully
```

## Linting Rules

### Markdownlint Configuration

```json
{
  "default": true,
  "MD003": { "style": "atx" },
  "MD004": { "style": "dash" },
  "MD007": { "indent": 2 },
  "MD013": { "line_length": 100 },
  "MD024": { "allow_different_nesting": true },
  "MD033": { "allowed_elements": ["br", "details", "summary"] },
  "MD041": false
}
```

### Common Violations
- MD001: Heading levels should increment by one
- MD009: Trailing spaces
- MD010: Hard tabs
- MD012: Multiple consecutive blank lines
- MD022: Headings should be surrounded by blank lines
- MD031: Fenced code blocks should be surrounded by blank lines
- MD032: Lists should be surrounded by blank lines

---
**Owner**: Documentation Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26