# Documentation Guide

> **Purpose**: Define when, why, and how to create documentation for VibeMatch  
> **Scope**: All documentation across code, APIs, and systems  
> **Enforcement**: Documentation reviews, automated checks

## Documentation Philosophy

### Core Principles

1. **Document for the Next Person** - Write as if you'll forget everything
2. **Just Enough Documentation** - Not too little, not too much
3. **Living Documentation** - Keep it updated or delete it
4. **Single Source of Truth** - One authoritative location
5. **Searchable and Discoverable** - Easy to find when needed

## When to Document

### MUST Document ✅

#### Code Level
- Public APIs and interfaces
- Complex algorithms or business logic
- Non-obvious design decisions
- Workarounds and technical debt
- Integration points

#### System Level
- Architecture decisions
- API contracts
- Deployment procedures
- Configuration options
- Security considerations

#### Project Level
- Getting started guides
- Contributing guidelines
- Troubleshooting guides
- Migration procedures
- Breaking changes

### SHOULD NOT Document ❌

- Obvious code (self-documenting)
- Implementation details that change frequently
- Deprecated features (mark as deprecated instead)
- Personal opinions or rants
- Temporary workarounds without context

## Documentation Hierarchy

### 1. Code-Level Documentation

Location: In the code files themselves

```typescript
/**
 * Calculates the optimal agent match score for a given task.
 * 
 * This algorithm considers multiple factors including agent capabilities,
 * historical performance, current load, and task requirements.
 * 
 * @param task - The task requiring agent assignment
 * @param agents - Available agents to consider
 * @param config - Scoring configuration with weights
 * @returns Sorted array of agents with match scores
 * 
 * @example
 * ```typescript
 * const matches = calculateAgentMatches(task, agents, {
 *   capabilityWeight: 0.4,
 *   performanceWeight: 0.3,
 *   availabilityWeight: 0.3
 * });
 * const bestAgent = matches[0];
 * ```
 * 
 * @see {@link https://docs.vibematch.io/matching-algorithm} for algorithm details
 */
export function calculateAgentMatches(
  task: Task,
  agents: Agent[],
  config: MatchConfig
): AgentMatch[] {
  // Implementation with inline comments for complex parts
}
```

### 2. API Documentation

Location: OpenAPI specifications and API docs

```yaml
paths:
  /agents/{agentId}/tasks:
    post:
      summary: Assign a task to an agent
      description: |
        Assigns a new task to the specified agent. The agent must be active
        and have the required capabilities for the task type.
        
        This endpoint will return an error if:
        - The agent is not found or inactive
        - The agent lacks required capabilities
        - The agent has reached maximum concurrent tasks
        - The task requirements exceed agent limitations
      operationId: assignTaskToAgent
      tags:
        - Agents
        - Tasks
      parameters:
        - name: agentId
          in: path
          required: true
          description: Unique identifier of the agent
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskAssignment'
            examples:
              simple:
                summary: Simple task assignment
                value:
                  taskId: "550e8400-e29b-41d4-a716-************"
                  priority: "normal"
              urgent:
                summary: Urgent task with deadline
                value:
                  taskId: "660e8400-e29b-41d4-a716-************"
                  priority: "urgent"
                  deadline: "2025-06-26T23:59:59Z"
      responses:
        '201':
          description: Task successfully assigned
        '400':
          description: Invalid request
        '404':
          description: Agent not found
        '409':
          description: Agent cannot accept task
```

### 3. Architecture Documentation

Location: `/docs/architecture/` or `/sdlc/01-architecture/`

```markdown
# Agent Matching Architecture

## Overview
The agent matching system uses a multi-factor scoring algorithm to assign
tasks to the most suitable agents.

## Design Decisions

### Decision: Use Weighted Scoring Algorithm
**Date**: 2025-06-20
**Status**: Accepted

**Context**: We need to match tasks with agents based on multiple criteria.

**Decision**: Implement a weighted scoring system that considers:
- Agent capabilities (40%)
- Historical performance (30%)
- Current availability (30%)

**Consequences**:
- Flexible and tunable matching
- Requires careful weight calibration
- May need ML enhancement later

**Alternatives Considered**:
- Rule-based matching: Too rigid
- ML-only approach: Not enough training data initially
- Random assignment: Poor user experience
```

### 4. User Documentation

Location: `/docs/users/` or product documentation site

```markdown
# Getting Started with Agent Tasks

This guide helps you create and manage tasks for AI agents in VibeMatch.

## Prerequisites
- Active VibeMatch account
- At least one agent configured
- API credentials (for API usage)

## Creating Your First Task

1. **Navigate to Tasks**
   Click on "Tasks" in the main navigation menu.

2. **Click "New Task"**
   The task creation form will appear.

3. **Fill in Task Details**
   - **Title**: Clear, descriptive title
   - **Type**: Select from available task types
   - **Description**: Detailed requirements
   - **Priority**: Normal, High, or Urgent

4. **Set Requirements**
   - Required capabilities
   - Estimated duration
   - Success criteria

5. **Submit Task**
   Click "Create Task" to submit.

## What Happens Next?
VibeMatch automatically:
1. Analyzes your task requirements
2. Identifies suitable agents
3. Assigns to the best available agent
4. Notifies you of assignment
```

## Documentation Formats

### README Files

Every project/service MUST have a README with:

```markdown
# Service Name

Brief description of what this service does.

## Quick Start

```bash
# Install dependencies
npm install

# Run tests
npm test

# Start development server
npm run dev
```

## Architecture

Brief overview of service architecture and key components.

## API

Link to API documentation or brief endpoint summary.

## Configuration

| Variable | Description | Default |
|----------|-------------|---------|
| PORT | Server port | 3000 |
| DB_URL | Database URL | - |

## Development

### Prerequisites
- Node.js 20+
- Docker
- Access to GCP

### Setup
1. Clone repository
2. Install dependencies
3. Copy `.env.example` to `.env`
4. Run `docker-compose up`

## Testing

```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# All tests with coverage
npm test
```

## Deployment

Service is automatically deployed via CI/CD on merge to main.

## Monitoring

- Logs: [Cloud Logging](link)
- Metrics: [Cloud Monitoring](link)
- Alerts: [PagerDuty](link)

## Contributing

See [CONTRIBUTING.md](./CONTRIBUTING.md)

## License

Proprietary - see [LICENSE](./LICENSE)
```

### Code Comments

Follow these patterns:

```typescript
// ❌ BAD - Obvious comment
// Increment i by 1
i++;

// ✅ GOOD - Explains why
// Skip the first element as it's the header row
i++;

// ❌ BAD - Outdated comment
// TODO: Implement caching (implemented in v2.1)
const result = await cache.get(key);

// ✅ GOOD - Actionable TODO
// TODO(john): Implement Redis caching by 2025-07-01 (JIRA-1234)
const result = await fetchFromDatabase(key);

// ❌ BAD - Commented code
// const oldImplementation = () => {
//   return slowAlgorithm();
// };

// ✅ GOOD - Explanation of removal
// Removed slowAlgorithm in favor of optimizedAlgorithm (PR #234)
// See git history for previous implementation

// ✅ GOOD - Complex logic explanation
// Binary search for the insertion point
// We maintain sorted order for O(log n) lookups
let left = 0;
let right = arr.length;
while (left < right) {
  const mid = Math.floor((left + right) / 2);
  if (arr[mid] < value) {
    left = mid + 1;
  } else {
    right = mid;
  }
}
```

### API Documentation Examples

Include examples for all major use cases:

```typescript
/**
 * @example Basic usage
 * ```typescript
 * const agent = await createAgent({
 *   name: 'CodeHelper',
 *   type: 'code_assistant',
 *   capabilities: ['javascript', 'typescript']
 * });
 * ```
 * 
 * @example With optional configuration
 * ```typescript
 * const agent = await createAgent({
 *   name: 'CodeHelper',
 *   type: 'code_assistant',
 *   capabilities: ['javascript', 'typescript'],
 *   config: {
 *     maxConcurrentTasks: 5,
 *     timeout: 30000,
 *     retryAttempts: 3
 *   }
 * });
 * ```
 * 
 * @example Error handling
 * ```typescript
 * try {
 *   const agent = await createAgent(data);
 * } catch (error) {
 *   if (error instanceof ValidationError) {
 *     console.error('Invalid agent data:', error.errors);
 *   } else if (error instanceof QuotaExceededError) {
 *     console.error('Agent limit reached');
 *   }
 * }
 * ```
 */
```

## Documentation Tools

### 1. Code Documentation
- **TypeDoc**: Generate documentation from TypeScript
- **JSDoc**: Standard for JavaScript documentation
- **OpenAPI Generator**: Create API docs from specs

### 2. Markdown
- **Mermaid**: Diagrams in markdown
- **Markdown linters**: Ensure consistency
- **Prettier**: Format markdown files

### 3. API Documentation
- **Swagger UI**: Interactive API documentation
- **Redoc**: Alternative API doc renderer
- **Postman**: API examples and testing

### 4. Architecture
- **C4 Model**: Architecture diagrams
- **ADR Tools**: Architecture Decision Records
- **Draw.io**: Diagram creation

## Documentation Review Process

### 1. Code Review Includes Docs
- Verify JSDoc completeness
- Check example accuracy
- Ensure README updates
- Validate API spec changes

### 2. Documentation-Only Reviews
For large documentation changes:
- Technical accuracy review
- Grammar and clarity review
- Example testing
- Link validation

### 3. Automated Checks
```yaml
# CI/CD documentation checks
documentation:
  - markdown-lint
  - broken-link-checker
  - api-spec-validator
  - jsdoc-coverage-check
```

## Maintaining Documentation

### Regular Updates
- **Sprint**: Update affected documentation
- **Monthly**: Review and update READMEs
- **Quarterly**: Architecture documentation review
- **Yearly**: Full documentation audit

### Deprecation Process
```typescript
/**
 * @deprecated Since version 2.0. Use {@link createAgentV2} instead.
 * Will be removed in version 3.0.
 * 
 * @example Migration
 * ```typescript
 * // Old way
 * const agent = await createAgent(name, type);
 * 
 * // New way
 * const agent = await createAgentV2({ name, type });
 * ```
 */
export async function createAgent(name: string, type: string): Promise<Agent> {
  console.warn('createAgent is deprecated. Use createAgentV2 instead.');
  return createAgentV2({ name, type });
}
```

## Documentation Templates

### Feature Documentation
```markdown
# Feature Name

## Overview
Brief description of the feature and its purpose.

## Use Cases
- Primary use case
- Secondary use case
- Edge cases

## How It Works
Technical explanation with diagrams if needed.

## Configuration
Required and optional configuration options.

## Examples
Common usage patterns with code examples.

## Limitations
Known limitations and constraints.

## Troubleshooting
Common issues and solutions.

## Related Features
Links to related functionality.
```

### Troubleshooting Guide
```markdown
# Troubleshooting: [Component Name]

## Common Issues

### Issue: Error message or symptom
**Symptoms**:
- Specific error messages
- Observable behavior

**Causes**:
- Possible cause 1
- Possible cause 2

**Solutions**:
1. First solution to try
2. Second solution
3. If all else fails

**Prevention**:
- How to avoid this issue

### Issue: Another common problem
[Same structure...]

## Debug Information
What information to collect when reporting issues.

## Support
Where to get help if these solutions don't work.
```

## Best Practices

### DO ✅
- Write for your audience (developer, user, ops)
- Include examples for everything
- Keep documentation close to code
- Use diagrams for complex concepts
- Test your examples
- Version your documentation
- Use consistent terminology

### DON'T ❌
- Document the obvious
- Let documentation become stale
- Use jargon without explanation
- Write novels - be concise
- Assume prior knowledge
- Mix concerns in one document
- Forget to update when code changes

---
**Owner**: Documentation Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26