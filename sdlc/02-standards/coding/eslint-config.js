/**
 * ESLint Configuration for VibeMatch
 * 
 * This configuration enforces TypeScript best practices and VibeMatch coding standards.
 * It extends from recommended rulesets and adds custom rules for consistency.
 */

module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    project: './tsconfig.json',
    tsconfigRootDir: __dirname,
  },
  
  env: {
    node: true,
    es2022: true,
    jest: true,
  },
  
  plugins: [
    '@typescript-eslint',
    'import',
    'jest',
    'security',
    'promise',
  ],
  
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:@typescript-eslint/recommended-requiring-type-checking',
    'plugin:import/errors',
    'plugin:import/warnings',
    'plugin:import/typescript',
    'plugin:jest/recommended',
    'plugin:security/recommended',
    'plugin:promise/recommended',
    'prettier', // Must be last to override other configs
  ],
  
  rules: {
    // TypeScript specific rules
    '@typescript-eslint/explicit-function-return-type': ['error', {
      allowExpressions: true,
      allowTypedFunctionExpressions: true,
      allowHigherOrderFunctions: true,
      allowDirectConstAssertionInArrowFunctions: true,
    }],
    
    '@typescript-eslint/no-explicit-any': 'error',
    '@typescript-eslint/no-unused-vars': ['error', {
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_',
    }],
    
    '@typescript-eslint/naming-convention': [
      'error',
      // Interfaces can optionally start with I
      {
        selector: 'interface',
        format: ['PascalCase'],
        custom: {
          regex: '^I[A-Z]',
          match: false,
        },
      },
      // Type aliases must be PascalCase
      {
        selector: 'typeAlias',
        format: ['PascalCase'],
      },
      // Enums must be PascalCase
      {
        selector: 'enum',
        format: ['PascalCase'],
      },
      // Enum members must be UPPER_CASE
      {
        selector: 'enumMember',
        format: ['UPPER_CASE'],
      },
      // Classes must be PascalCase
      {
        selector: 'class',
        format: ['PascalCase'],
      },
      // Variables must be camelCase or UPPER_CASE (for constants)
      {
        selector: 'variable',
        format: ['camelCase', 'UPPER_CASE'],
        leadingUnderscore: 'allow',
      },
      // Functions must be camelCase
      {
        selector: 'function',
        format: ['camelCase'],
      },
      // Parameters must be camelCase
      {
        selector: 'parameter',
        format: ['camelCase'],
        leadingUnderscore: 'allow',
      },
      // Private members can have leading underscore
      {
        selector: 'memberLike',
        modifiers: ['private'],
        format: ['camelCase'],
        leadingUnderscore: 'allow',
      },
    ],
    
    '@typescript-eslint/explicit-member-accessibility': ['error', {
      accessibility: 'explicit',
      overrides: {
        constructors: 'no-public',
      },
    }],
    
    '@typescript-eslint/member-ordering': ['error', {
      default: [
        // Static fields
        'public-static-field',
        'protected-static-field',
        'private-static-field',
        
        // Instance fields
        'public-instance-field',
        'protected-instance-field',
        'private-instance-field',
        
        // Constructors
        'constructor',
        
        // Static methods
        'public-static-method',
        'protected-static-method',
        'private-static-method',
        
        // Instance methods
        'public-instance-method',
        'protected-instance-method',
        'private-instance-method',
      ],
    }],
    
    '@typescript-eslint/no-floating-promises': 'error',
    '@typescript-eslint/no-misused-promises': 'error',
    '@typescript-eslint/await-thenable': 'error',
    '@typescript-eslint/no-unnecessary-type-assertion': 'error',
    '@typescript-eslint/prefer-nullish-coalescing': 'error',
    '@typescript-eslint/prefer-optional-chain': 'error',
    '@typescript-eslint/strict-boolean-expressions': ['error', {
      allowString: false,
      allowNumber: false,
      allowNullableObject: true,
    }],
    
    // Import rules
    'import/order': ['error', {
      groups: [
        'builtin',
        'external',
        'internal',
        'parent',
        'sibling',
        'index',
        'type',
      ],
      'newlines-between': 'always',
      alphabetize: {
        order: 'asc',
        caseInsensitive: true,
      },
    }],
    
    'import/no-duplicates': 'error',
    'import/no-cycle': 'error',
    'import/no-self-import': 'error',
    'import/no-useless-path-segments': 'error',
    
    // General ESLint rules
    'no-console': ['error', {
      allow: ['warn', 'error'],
    }],
    
    'no-debugger': 'error',
    'no-alert': 'error',
    'no-return-await': 'error',
    'no-param-reassign': 'error',
    'no-nested-ternary': 'error',
    'no-unneeded-ternary': 'error',
    'no-var': 'error',
    'prefer-const': 'error',
    'prefer-template': 'error',
    'prefer-arrow-callback': 'error',
    'arrow-body-style': ['error', 'as-needed'],
    'object-shorthand': 'error',
    
    // Security rules
    'security/detect-object-injection': 'off', // Too many false positives
    'security/detect-non-literal-fs-filename': 'error',
    'security/detect-eval-with-expression': 'error',
    'security/detect-no-csrf-before-method-override': 'error',
    
    // Promise rules
    'promise/always-return': 'error',
    'promise/catch-or-return': 'error',
    'promise/param-names': 'error',
    'promise/no-nesting': 'warn',
    
    // Jest rules
    'jest/consistent-test-it': ['error', {
      fn: 'it',
      withinDescribe: 'it',
    }],
    'jest/expect-expect': 'error',
    'jest/no-disabled-tests': 'warn',
    'jest/no-focused-tests': 'error',
    'jest/valid-expect': 'error',
    'jest/valid-expect-in-promise': 'error',
  },
  
  overrides: [
    // Test file specific rules
    {
      files: ['**/*.test.ts', '**/*.spec.ts'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/no-non-null-assertion': 'off',
        'security/detect-object-injection': 'off',
      },
    },
    
    // Configuration files
    {
      files: ['*.js', '*.config.ts'],
      rules: {
        '@typescript-eslint/no-var-requires': 'off',
        '@typescript-eslint/explicit-function-return-type': 'off',
      },
    },
  ],
  
  settings: {
    'import/parsers': {
      '@typescript-eslint/parser': ['.ts'],
    },
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.json',
      },
    },
  },
  
  ignorePatterns: [
    'dist/',
    'build/',
    'coverage/',
    'node_modules/',
    '*.d.ts',
    'generated/',
  ],
};