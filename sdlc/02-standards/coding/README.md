# Coding Standards

> **Purpose**: TypeScript and Node.js coding standards for VibeMatch  
> **Audience**: All developers contributing to the codebase

## Overview
Comprehensive coding standards ensuring consistent, maintainable, and high-quality TypeScript code across all VibeMatch services. These standards are enforced through ESLint, Prettier, and code review processes.

## Directory Structure
```
coding/
├── typescript-style-guide.md   # TypeScript coding conventions
├── naming-conventions.md       # Naming rules for all code elements
├── project-structure.md        # Service and package organization
├── code-review-checklist.md   # Review criteria and process
├── eslint-config.js           # ESLint configuration
└── prettier-config.js         # Prettier formatting rules
```

## Quick Links
### Essential Standards
- [TypeScript Style Guide](./typescript-style-guide.md) - Language-specific rules
- [Naming Conventions](./naming-conventions.md) - How to name things
- [Project Structure](./project-structure.md) - Code organization
- [Code Review Checklist](./code-review-checklist.md) - Review process

### Configuration Files
- [ESLint Config](./eslint-config.js) - Linting rules
- [Prettier Config](./prettier-config.js) - Formatting rules

## Key Principles
- **Type Safety**: TypeScript strict mode always enabled
- **Consistency**: Same patterns across all services
- **Readability**: Code should be self-documenting
- **Maintainability**: Easy to modify and extend
- **Performance**: Consider efficiency in implementations

## Common Patterns
### TypeScript Configuration
```json
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "commonjs",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  }
}
```

### Code Organization
```typescript
// Clear imports organization
import { external } from 'external-package';
import { internal } from '@vibematch/shared';
import { local } from './local-module';

// Proper error handling
try {
  await performOperation();
} catch (error) {
  logger.error('Operation failed', { error, context });
  throw new OperationError('Failed to perform operation', error);
}
```

---
**Section Owner**: Development Team  
**Last Updated**: 2025-06-26  
**Parent**: [Standards](../)