# Code Review Checklist

> **Purpose**: Ensure consistent, high-quality code reviews across VibeMatch  
> **Scope**: All pull requests and code changes  
> **Enforcement**: PR templates, review automation, team training

## Pre-Review Checklist (Author)

Before requesting review, ensure:

- [ ] Code compiles without warnings
- [ ] All tests pass (unit, integration, e2e)
- [ ] <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> have been run
- [ ] PR description explains the changes
- [ ] Related issue/ticket is linked
- [ ] Documentation is updated if needed
- [ ] No sensitive data in code or commits

## Review Categories

### 1. Functionality

#### Requirements
- [ ] Code implements the specified requirements
- [ ] Edge cases are handled appropriately
- [ ] Error scenarios are properly managed
- [ ] No regressions introduced

#### Business Logic
- [ ] Business rules are correctly implemented
- [ ] Domain logic is in the appropriate layer
- [ ] Validation rules are comprehensive
- [ ] State transitions are valid

### 2. Code Quality

#### Readability
- [ ] Code is self-documenting
- [ ] Variable/function names are descriptive
- [ ] Complex logic is commented
- [ ] No dead code or commented-out code

#### Structure
- [ ] Follows project structure standards
- [ ] Single Responsibility Principle applied
- [ ] DRY principle followed (no duplication)
- [ ] Appropriate abstraction levels

#### TypeScript Specific
- [ ] Proper type annotations (no `any`)
- [ ] Interfaces used appropriately
- [ ] Generics used where beneficial
- [ ] Strict null checks pass

### 3. Testing

#### Test Coverage
- [ ] New code has tests
- [ ] Edge cases are tested
- [ ] Error paths are tested
- [ ] Coverage meets standards (>80%)

#### Test Quality
- [ ] Tests are readable and maintainable
- [ ] Tests follow AAA pattern (Arrange, Act, Assert)
- [ ] Mocks are used appropriately
- [ ] Tests are independent and repeatable

### 4. Security

#### Authentication & Authorization
- [ ] Authentication is properly implemented
- [ ] Authorization checks are in place
- [ ] No privilege escalation vulnerabilities
- [ ] Tokens/secrets are handled securely

#### Input Validation
- [ ] All inputs are validated
- [ ] SQL injection prevention
- [ ] XSS prevention
- [ ] Command injection prevention

#### Data Protection
- [ ] Sensitive data is encrypted
- [ ] PII is handled according to standards
- [ ] No sensitive data in logs
- [ ] Proper data classification applied

### 5. Performance

#### Efficiency
- [ ] No obvious performance issues
- [ ] Database queries are optimized
- [ ] Appropriate caching is used
- [ ] No memory leaks

#### Scalability
- [ ] Code can handle expected load
- [ ] No blocking operations
- [ ] Appropriate async/await usage
- [ ] Resource cleanup is proper

### 6. Error Handling

#### Exception Management
- [ ] All exceptions are caught
- [ ] Errors are logged appropriately
- [ ] User-friendly error messages
- [ ] No sensitive data in error messages

#### Recovery
- [ ] Graceful degradation implemented
- [ ] Retry logic where appropriate
- [ ] Circuit breakers for external calls
- [ ] Proper cleanup on failure

### 7. API Design

#### REST Standards
- [ ] Follows REST conventions
- [ ] Proper HTTP methods used
- [ ] Appropriate status codes returned
- [ ] Consistent response format

#### Documentation
- [ ] OpenAPI spec updated
- [ ] Endpoint documentation complete
- [ ] Examples provided
- [ ] Breaking changes noted

### 8. Database

#### Schema Changes
- [ ] Migrations are backwards compatible
- [ ] Indexes are appropriate
- [ ] Constraints are defined
- [ ] No breaking changes without version bump

#### Queries
- [ ] Queries are parameterized
- [ ] N+1 problems avoided
- [ ] Appropriate use of transactions
- [ ] Connection pooling considered

### 9. Dependencies

#### Package Management
- [ ] Dependencies are necessary
- [ ] Versions are pinned appropriately
- [ ] No security vulnerabilities
- [ ] License compatibility checked

#### Internal Dependencies
- [ ] Circular dependencies avoided
- [ ] Appropriate use of shared packages
- [ ] Version compatibility maintained
- [ ] Breaking changes communicated

### 10. Documentation

#### Code Documentation
- [ ] Public APIs have JSDoc
- [ ] Complex algorithms explained
- [ ] README updated if needed
- [ ] Architecture decisions recorded

#### User Documentation
- [ ] User-facing changes documented
- [ ] API documentation updated
- [ ] Migration guides provided
- [ ] Changelog updated

## Review Comments

### Effective Comments

#### DO ✅
```typescript
// Suggestion: Consider using a Map for O(1) lookups instead of array.find()
// This would improve performance for large datasets.
const agentMap = new Map(agents.map(a => [a.id, a]));
const agent = agentMap.get(agentId);
```

#### DON'T ❌
```typescript
// This is wrong
// Fix this
// Bad code
```

### Comment Types

#### 🚨 **MUST** - Blocking issues
- Security vulnerabilities
- Data loss risks
- Breaking changes
- Incorrect business logic

#### ⚠️ **SHOULD** - Important improvements
- Performance issues
- Code quality problems
- Missing tests
- Better patterns available

#### 💡 **CONSIDER** - Suggestions
- Alternative approaches
- Future improvements
- Nice-to-have features
- Style preferences

#### ❓ **QUESTION** - Clarifications
- Understanding intent
- Business rule clarification
- Design decisions
- Edge case handling

## Common Issues Checklist

### TypeScript Issues
- [ ] No use of `any` type
- [ ] No use of `@ts-ignore`
- [ ] No unchecked array access
- [ ] Proper null/undefined handling
- [ ] Correct generic constraints

### Async/Promise Issues
- [ ] No floating promises
- [ ] Proper error handling in async
- [ ] No missing `await`
- [ ] Promise.all for parallel operations
- [ ] No async in loops without care

### Security Issues
- [ ] No hardcoded credentials
- [ ] No eval() or Function()
- [ ] No user input in queries
- [ ] No sensitive data exposure
- [ ] Proper authentication checks

### Performance Issues
- [ ] No synchronous I/O
- [ ] No unbounded loops
- [ ] No memory leaks
- [ ] Proper connection cleanup
- [ ] Efficient data structures

## Review Process

### 1. Initial Review (15-30 minutes)
- Understand the change context
- Review PR description and linked issues
- Check CI/CD status
- Quick code scan for obvious issues

### 2. Detailed Review (30-60 minutes)
- Line-by-line code review
- Check against checklist categories
- Test locally if needed
- Verify test coverage

### 3. Feedback
- Provide constructive comments
- Suggest improvements
- Ask clarifying questions
- Acknowledge good practices

### 4. Follow-up
- Verify requested changes
- Re-review modified code
- Approve when satisfied
- Help resolve any blockers

## Approval Criteria

### Required for Approval
- All MUST items addressed
- Tests pass and coverage adequate
- No security vulnerabilities
- Follows coding standards
- Clear commit messages

### Nice to Have
- All SHOULD items addressed
- Performance optimizations
- Additional documentation
- Refactoring opportunities

## Review Metrics

Track these metrics for continuous improvement:

- Review turnaround time
- Number of review cycles
- Defects found post-merge
- Review comment quality
- Team satisfaction

## Tools and Automation

### Automated Checks
- ESLint for code quality
- Prettier for formatting
- Jest for test coverage
- Snyk for security scanning
- SonarQube for code analysis

### Review Tools
- GitHub PR reviews
- Code comments
- Suggested changes
- Review assignments
- Status checks

## Best Practices

### For Reviewers
1. Be constructive and respectful
2. Explain the "why" behind suggestions
3. Provide code examples
4. Focus on the code, not the person
5. Acknowledge time constraints

### For Authors
1. Keep PRs small and focused
2. Provide context in description
3. Respond to all comments
4. Ask for clarification when needed
5. Thank reviewers for their time

## Escalation Path

When consensus cannot be reached:

1. **Technical Discussion** - Team meeting
2. **Architecture Review** - Tech lead input
3. **Team Decision** - Democratic vote
4. **Executive Decision** - CTO/VP Engineering

---
**Owner**: Development Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26