# Naming Conventions

> **Purpose**: Define consistent naming rules across the VibeMatch codebase  
> **Scope**: All code elements including files, variables, functions, classes  
> **Enforcement**: ESLint rules, code review process

## File Naming

### TypeScript Files

```bash
# ✅ GOOD - Descriptive kebab-case
agent-service.ts
task-repository.ts
create-agent.dto.ts
agent-service.test.ts
index.ts

# ❌ BAD - Wrong casing or unclear
AgentService.ts      # PascalCase
agent_service.ts     # snake_case
agentservice.ts      # No separation
as.ts               # Too abbreviated
```

### File Naming by Type

| File Type | Convention | Example |
|-----------|------------|---------|
| Service | `{name}-service.ts` | `agent-service.ts` |
| Repository | `{name}-repository.ts` | `task-repository.ts` |
| Controller | `{name}-controller.ts` | `agent-controller.ts` |
| Interface | `{name}.interface.ts` | `agent.interface.ts` |
| Type | `{name}.types.ts` | `task.types.ts` |
| Test | `{name}.test.ts` | `agent-service.test.ts` |
| Config | `{name}.config.ts` | `database.config.ts` |
| Utils | `{name}.utils.ts` | `string.utils.ts` |
| Constants | `{name}.constants.ts` | `api.constants.ts` |

### Directory Structure

```bash
# ✅ GOOD - Clear organization
src/
├── agents/
│   ├── agent-service.ts
│   ├── agent-repository.ts
│   ├── agent.interface.ts
│   └── agent-service.test.ts
├── tasks/
│   ├── task-service.ts
│   ├── task-repository.ts
│   └── task.types.ts
└── shared/
    ├── utils/
    │   ├── string.utils.ts
    │   └── date.utils.ts
    └── constants/
        └── api.constants.ts
```

## Variable Naming

### General Rules

```typescript
// ✅ GOOD - Descriptive camelCase
const userEmail = '<EMAIL>';
const isActive = true;
const maxRetryCount = 3;
const createdAt = new Date();

// ❌ BAD - Poor naming
const e = '<EMAIL>';      // Too short
const user_email = '<EMAIL>';  // snake_case
const ISACTIVE = true;              // Wrong case
const max = 3;                      // Unclear
```

### Boolean Variables

Always use `is`, `has`, `can`, or similar prefixes:

```typescript
// ✅ GOOD - Clear boolean names
const isLoading = true;
const hasPermission = false;
const canEdit = true;
const shouldRetry = false;
const wasSuccessful = true;

// ❌ BAD - Unclear booleans
const loading = true;      // Missing prefix
const permission = false;  // Ambiguous
const editable = true;     // Use canEdit
const retry = false;       // Use shouldRetry
```

### Arrays and Collections

Use plural names:

```typescript
// ✅ GOOD - Plural for collections
const users: User[] = [];
const activeAgents: Agent[] = [];
const taskIds: string[] = [];
const errors: Error[] = [];

// ❌ BAD - Singular for collections
const user: User[] = [];
const agentList: Agent[] = [];  // Redundant 'List'
const taskId: string[] = [];
```

### Constants

Use UPPER_SNAKE_CASE for true constants:

```typescript
// ✅ GOOD - Clear constant naming
const MAX_RETRY_COUNT = 3;
const DEFAULT_PAGE_SIZE = 20;
const API_BASE_URL = 'https://api.vibematch.io';
const CACHE_TTL_SECONDS = 3600;

// ✅ GOOD - Object constants
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  NOT_FOUND: 404
} as const;

// ❌ BAD - Wrong constant naming
const maxRetryCount = 3;        // Not UPPER_CASE
const MAXRETRYCOUNT = 3;        // No underscores
const max_retry_count = 3;      // Not UPPER_CASE
```

## Function Naming

### Action Functions

Use verb or verb-noun combinations:

```typescript
// ✅ GOOD - Clear action names
function createAgent(data: CreateAgentDto): Agent {}
function validateEmail(email: string): boolean {}
function calculateScore(metrics: Metrics): number {}
async function fetchUserById(id: string): Promise<User> {}
async function updateTaskStatus(id: string, status: Status): Promise<void> {}

// ❌ BAD - Poor function names
function agent(data: CreateAgentDto): Agent {}      // No verb
function email(email: string): boolean {}            // Unclear
function calc(metrics: Metrics): number {}           // Abbreviated
function getData(id: string): Promise<User> {}       // Too generic
```

### Boolean Functions

Use `is`, `has`, `can` prefixes:

```typescript
// ✅ GOOD - Boolean function names
function isValidEmail(email: string): boolean {}
function hasPermission(user: User, action: string): boolean {}
function canExecuteTask(agent: Agent, task: Task): boolean {}
function shouldRetry(error: Error): boolean {}

// ❌ BAD - Unclear boolean functions
function checkEmail(email: string): boolean {}      // Use isValidEmail
function permission(user: User): boolean {}          // Use hasPermission
function validateTask(task: Task): boolean {}        // Use isValidTask
```

### Event Handlers

Use `handle` or `on` prefix:

```typescript
// ✅ GOOD - Event handler names
function handleClick(event: MouseEvent): void {}
function handleSubmit(data: FormData): void {}
function onUserCreated(user: User): void {}
function onTaskCompleted(task: Task): void {}

// ❌ BAD - Poor handler names
function click(event: MouseEvent): void {}          // Missing prefix
function userCreated(user: User): void {}           // Missing prefix
function doClick(event: MouseEvent): void {}        // Use handle
```

## Class Naming

### General Classes

Use PascalCase with descriptive names:

```typescript
// ✅ GOOD - Clear class names
class AgentService {}
class TaskRepository {}
class UserController {}
class EmailValidator {}
class AuthenticationMiddleware {}

// ❌ BAD - Poor class names
class agentService {}           // Not PascalCase
class Agent_Service {}          // No underscores
class AS {}                     // Too abbreviated
class Service {}                // Too generic
```

### Abstract Classes and Interfaces

```typescript
// ✅ GOOD - Abstract class with prefix
abstract class BaseRepository<T> {}
abstract class AbstractService {}

// ✅ GOOD - Interface with 'I' prefix (optional but consistent)
interface IUserService {}
interface IRepository<T> {}

// ✅ GOOD - Interface without prefix (also acceptable)
interface UserService {}
interface Repository<T> {}

// ❌ BAD - Inconsistent naming
abstract class Repository {}     // Missing 'Abstract' or 'Base'
interface userService {}         // Not PascalCase
```

### Error Classes

Always suffix with `Error`:

```typescript
// ✅ GOOD - Error class names
class ValidationError extends Error {}
class NotFoundError extends Error {}
class AuthenticationError extends Error {}
class NetworkError extends Error {}

// ❌ BAD - Missing Error suffix
class Validation extends Error {}
class NotFound extends Error {}
class AuthFailed extends Error {}
```

## Type and Interface Naming

### Type Aliases

```typescript
// ✅ GOOD - Descriptive type names
type UserId = string;
type Email = string;
type Timestamp = Date;
type AgentStatus = 'active' | 'inactive' | 'pending';
type TaskResult = Success | Failure;

// ❌ BAD - Poor type names
type ID = string;               // Too abbreviated
type str = string;              // Redundant
type Status = boolean;          // Misleading
```

### Generic Type Parameters

Use descriptive single letters or short names:

```typescript
// ✅ GOOD - Clear generic parameters
interface Repository<T extends BaseEntity> {}
interface Mapper<TInput, TOutput> {}
interface Response<TData, TError = Error> {}
function transform<TSource, TTarget>(source: TSource): TTarget {}

// Common conventions:
// T - Type
// K - Key
// V - Value
// E - Element
// S - Source
// R - Result

// ❌ BAD - Unclear generics
interface Repository<Type> {}    // Too verbose
interface Mapper<A, B> {}        // Unclear meaning
interface Response<D, E> {}      // Ambiguous
```

## Enum Naming

### Enum Names

Use PascalCase for enum names, UPPER_SNAKE_CASE for values:

```typescript
// ✅ GOOD - Proper enum naming
enum TaskStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

enum UserRole {
  USER = 'USER',
  AGENT = 'AGENT',
  ADMIN = 'ADMIN'
}

// ❌ BAD - Poor enum naming
enum task_status {              // Not PascalCase
  pending = 'pending',          // Not UPPER_CASE
  inProgress = 'inProgress',    // Not UPPER_SNAKE_CASE
  COMPLETED = 'COMPLETED'
}
```

## Method Naming in Classes

### Public Methods

```typescript
class AgentService {
  // ✅ GOOD - Clear public method names
  async createAgent(dto: CreateAgentDto): Promise<Agent> {}
  async findAgentById(id: string): Promise<Agent | null> {}
  async updateAgentStatus(id: string, status: AgentStatus): Promise<void> {}
  isAgentEligible(agent: Agent, task: Task): boolean {}
  
  // ❌ BAD - Poor method names
  async create(dto: CreateAgentDto): Promise<Agent> {}      // Too generic
  async get(id: string): Promise<Agent | null> {}           // Too generic
  async update(id: string, status: AgentStatus): Promise<void> {} // Unclear what updates
}
```

### Private Methods

Prefix with underscore or use `private` keyword:

```typescript
class TaskProcessor {
  // ✅ GOOD - TypeScript private keyword (preferred)
  private validateTask(task: Task): void {}
  private calculatePriority(task: Task): number {}
  
  // ✅ ACCEPTABLE - Underscore prefix (legacy)
  private _validateTask(task: Task): void {}
  private _calculatePriority(task: Task): number {}
  
  // ❌ BAD - No distinction
  validateTask(task: Task): void {}  // Looks public
}
```

## Package and Module Naming

### NPM Package Names

```json
// ✅ GOOD - Scoped package names
{
  "name": "@vibematch/agent-service",
  "name": "@vibematch/shared-utils",
  "name": "@vibematch/task-processor"
}

// ❌ BAD - Unscoped or poor names
{
  "name": "agent-service",          // Missing scope
  "name": "@vm/agent",              // Unclear scope
  "name": "@vibematch/AgentService" // Wrong case
}
```

### Module Exports

```typescript
// ✅ GOOD - Clear module exports
// agents/index.ts
export { AgentService } from './agent-service';
export { AgentRepository } from './agent-repository';
export type { Agent, CreateAgentDto } from './agent.types';

// ❌ BAD - Unclear exports
export * from './agent-service';    // Too broad
export { default } from './service'; // Unclear default
```

## Environment Variables

Use UPPER_SNAKE_CASE:

```bash
# ✅ GOOD - Clear env var names
NODE_ENV=production
DATABASE_URL=postgresql://localhost:5432/vibematch
REDIS_HOST=localhost
REDIS_PORT=6379
JWT_SECRET_KEY=your-secret-key
MAX_POOL_SIZE=20

# ❌ BAD - Poor env var names
nodeEnv=production              # Not UPPER_CASE
database-url=postgresql://...   # Hyphens instead of underscores
JWTSECRET=your-secret-key      # No word separation
```

## Configuration Keys

Use camelCase for config objects:

```typescript
// ✅ GOOD - Config object keys
const config = {
  database: {
    host: 'localhost',
    port: 5432,
    maxPoolSize: 20,
    connectionTimeout: 5000
  },
  redis: {
    host: 'localhost',
    port: 6379,
    enableTls: true
  }
};

// ❌ BAD - Inconsistent config keys
const config = {
  database: {
    HOST: 'localhost',          // Wrong case
    database_port: 5432,        // Snake case
    'max-pool-size': 20,        // Kebab case
    TIMEOUT: 5000               // Wrong case
  }
};
```

## Event Names

Use dot notation or colon notation consistently:

```typescript
// ✅ GOOD - Consistent event naming (dot notation)
const EVENTS = {
  AGENT_CREATED: 'agent.created',
  AGENT_UPDATED: 'agent.updated',
  TASK_ASSIGNED: 'task.assigned',
  TASK_COMPLETED: 'task.completed'
};

// ✅ GOOD - Consistent event naming (colon notation)
const EVENTS = {
  AGENT_CREATED: 'agent:created',
  AGENT_UPDATED: 'agent:updated',
  TASK_ASSIGNED: 'task:assigned',
  TASK_COMPLETED: 'task:completed'
};

// ❌ BAD - Inconsistent event naming
const EVENTS = {
  AGENT_CREATED: 'agentCreated',     // Camel case
  AGENT_UPDATED: 'agent.updated',     // Mixed styles
  TASK_ASSIGNED: 'task:assigned',     // Mixed styles
  TASK_COMPLETED: 'TASK_COMPLETED'    // Upper case
};
```

## Database Naming

### Table Names

Use snake_case plural:

```sql
-- ✅ GOOD - Database table names
CREATE TABLE users (...);
CREATE TABLE agents (...);
CREATE TABLE task_executions (...);
CREATE TABLE agent_metrics (...);

-- ❌ BAD - Poor table names
CREATE TABLE User (...);           -- PascalCase
CREATE TABLE agent (...);          -- Singular
CREATE TABLE taskExecutions (...); -- camelCase
```

### Column Names

Use snake_case:

```sql
-- ✅ GOOD - Column names
CREATE TABLE users (
  id UUID PRIMARY KEY,
  email VARCHAR(255),
  created_at TIMESTAMP,
  is_active BOOLEAN,
  last_login_at TIMESTAMP
);

-- ❌ BAD - Poor column names
CREATE TABLE users (
  ID UUID PRIMARY KEY,        -- Upper case
  emailAddress VARCHAR(255),  -- camelCase
  created-at TIMESTAMP,       -- Kebab case
  isActive BOOLEAN           -- camelCase
);
```

## Common Abbreviations

Avoid abbreviations except for widely accepted ones:

```typescript
// ✅ ACCEPTABLE - Common abbreviations
const config = loadConfig();
const dto = new CreateAgentDto();
const api = new ApiClient();
const db = new Database();
const id = generateId();
const url = 'https://example.com';

// ❌ AVOID - Unclear abbreviations
const u = getUser();           // Use 'user'
const agt = getAgent();        // Use 'agent'
const resp = getResponse();    // Use 'response'
const vals = getValues();      // Use 'values'
const idx = getIndex();        // Use 'index'
```

## Naming Checklist

Before naming something, ask:
- [ ] Is the name descriptive and unambiguous?
- [ ] Does it follow the appropriate casing convention?
- [ ] Is it consistent with similar names in the codebase?
- [ ] Would a new developer understand it immediately?
- [ ] Is it free from unnecessary abbreviations?
- [ ] Does it avoid reserved words and conflicts?

---
**Owner**: Development Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26