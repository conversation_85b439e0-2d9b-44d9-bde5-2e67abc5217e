# Project Structure Standards

> **Purpose**: Define consistent project and code organization patterns  
> **Scope**: All services, packages, and repositories in VibeMatch  
> **Enforcement**: Project templates, CI/CD validation

## Repository Structure

### Monorepo Organization

VibeMatch uses a monorepo structure with Turborepo:

```
vibe-match/
├── .github/                    # GitHub configuration
│   ├── workflows/             # CI/CD workflows
│   └── CODEOWNERS            # Code ownership
├── infrastructure/            # Infrastructure as Code
│   ├── terraform/            # Terraform configurations
│   ├── k8s/                 # Kubernetes manifests
│   └── docker/              # Dockerfiles
├── packages/                 # Shared packages
│   ├── contracts/           # TypeScript interfaces
│   ├── utils/              # Common utilities
│   ├── logger/             # Logging package
│   └── testing/            # Test utilities
├── services/                # Microservices
│   ├── gateway/            # API Gateway
│   ├── orchestrator/       # Agent orchestration
│   ├── marketplace/        # Market matching
│   ├── agents/            # Agent management
│   └── billing/           # Credit system
├── tools/                   # Development tools
│   ├── scripts/           # Build/deploy scripts
│   └── generators/        # Code generators
├── docs/                   # Documentation
├── sdlc/                   # SDLC documentation
├── .eslintrc.js           # ESLint config
├── .prettierrc            # Prettier config
├── turbo.json             # Turborepo config
├── package.json           # Root package.json
└── tsconfig.json          # TypeScript config
```

## Service Structure

### Standard Microservice Layout

Each service follows this structure:

```
services/agent-service/
├── src/
│   ├── api/                    # API layer
│   │   ├── controllers/       # HTTP controllers
│   │   ├── middleware/        # Express middleware
│   │   ├── routes/           # Route definitions
│   │   └── validators/       # Request validation
│   ├── application/           # Application layer
│   │   ├── services/         # Business logic
│   │   ├── use-cases/        # Use case implementations
│   │   └── dto/              # Data transfer objects
│   ├── domain/               # Domain layer
│   │   ├── entities/         # Domain entities
│   │   ├── value-objects/    # Value objects
│   │   ├── repositories/     # Repository interfaces
│   │   └── events/           # Domain events
│   ├── infrastructure/       # Infrastructure layer
│   │   ├── database/         # Database implementation
│   │   ├── messaging/        # Pub/Sub implementation
│   │   ├── cache/           # Redis implementation
│   │   └── external/        # External services
│   ├── config/              # Configuration
│   │   ├── default.ts       # Default config
│   │   ├── production.ts    # Production config
│   │   └── test.ts          # Test config
│   ├── app.ts               # Express app setup
│   ├── server.ts            # Server entry point
│   └── index.ts             # Main entry point
├── tests/
│   ├── unit/               # Unit tests
│   ├── integration/        # Integration tests
│   └── e2e/               # End-to-end tests
├── scripts/                # Service-specific scripts
├── Dockerfile             # Container definition
├── package.json           # Service dependencies
├── tsconfig.json          # TypeScript config
└── README.md              # Service documentation
```

## Layer Architecture

### Clean Architecture Principles

Each service follows clean architecture with clear boundaries:

```typescript
// ❌ BAD - Domain depends on infrastructure
// domain/entities/user.ts
import { FirestoreDocument } from '@google-cloud/firestore'; // Wrong!

export class User extends FirestoreDocument {
  // ...
}

// ✅ GOOD - Infrastructure depends on domain
// domain/entities/user.ts
export class User {
  constructor(
    public readonly id: string,
    public readonly email: string,
    public readonly roles: Role[]
  ) {}
}

// infrastructure/database/user-repository.ts
import { User } from '../../domain/entities/user';
import { Firestore } from '@google-cloud/firestore';

export class FirestoreUserRepository implements UserRepository {
  constructor(private firestore: Firestore) {}
  
  async findById(id: string): Promise<User | null> {
    const doc = await this.firestore.collection('users').doc(id).get();
    if (!doc.exists) return null;
    
    const data = doc.data();
    return new User(doc.id, data.email, data.roles);
  }
}
```

### Dependency Direction

Dependencies flow inward:
- Domain → Nothing (pure business logic)
- Application → Domain
- Infrastructure → Application & Domain
- API → Application

## Package Structure

### Shared Package Organization

```
packages/contracts/
├── src/
│   ├── agents/           # Agent-related contracts
│   │   ├── agent.interface.ts
│   │   ├── agent.dto.ts
│   │   └── agent.events.ts
│   ├── tasks/           # Task-related contracts
│   │   ├── task.interface.ts
│   │   ├── task.dto.ts
│   │   └── task.events.ts
│   ├── common/          # Common contracts
│   │   ├── pagination.dto.ts
│   │   ├── error.interface.ts
│   │   └── base.entity.ts
│   └── index.ts         # Main exports
├── package.json
└── tsconfig.json
```

### Utility Package Structure

```
packages/utils/
├── src/
│   ├── string/          # String utilities
│   │   ├── string.utils.ts
│   │   └── string.utils.test.ts
│   ├── date/           # Date utilities
│   │   ├── date.utils.ts
│   │   └── date.utils.test.ts
│   ├── crypto/         # Cryptography utilities
│   │   ├── hash.utils.ts
│   │   ├── encrypt.utils.ts
│   │   └── crypto.utils.test.ts
│   └── index.ts        # Barrel exports
├── package.json
└── tsconfig.json
```

## File Organization

### Controller Structure

```typescript
// api/controllers/agent-controller.ts
import { Request, Response, NextFunction } from 'express';
import { AgentService } from '../../application/services/agent-service';
import { CreateAgentDto } from '../dto/create-agent.dto';

export class AgentController {
  constructor(private readonly agentService: AgentService) {}

  async create(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const dto = req.body as CreateAgentDto;
      const agent = await this.agentService.createAgent(dto);
      res.status(201).json({ data: agent });
    } catch (error) {
      next(error);
    }
  }

  async findById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const agent = await this.agentService.findById(id);
      
      if (!agent) {
        res.status(404).json({ 
          error: { code: 'NOT_FOUND', message: 'Agent not found' } 
        });
        return;
      }
      
      res.json({ data: agent });
    } catch (error) {
      next(error);
    }
  }
}
```

### Service Structure

```typescript
// application/services/agent-service.ts
import { Agent } from '../../domain/entities/agent';
import { AgentRepository } from '../../domain/repositories/agent-repository';
import { EventBus } from '../../domain/events/event-bus';
import { CreateAgentDto } from '../dto/create-agent.dto';
import { AgentCreatedEvent } from '../../domain/events/agent-created.event';

export class AgentService {
  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly eventBus: EventBus
  ) {}

  async createAgent(dto: CreateAgentDto): Promise<Agent> {
    // Business logic validation
    this.validateAgentData(dto);
    
    // Create domain entity
    const agent = Agent.create({
      name: dto.name,
      type: dto.type,
      capabilities: dto.capabilities
    });
    
    // Persist
    await this.agentRepository.save(agent);
    
    // Publish event
    await this.eventBus.publish(
      new AgentCreatedEvent(agent.id, agent.name, new Date())
    );
    
    return agent;
  }

  private validateAgentData(dto: CreateAgentDto): void {
    // Business rule validation
    if (dto.capabilities.length === 0) {
      throw new Error('Agent must have at least one capability');
    }
  }
}
```

### Repository Structure

```typescript
// infrastructure/database/agent-repository-impl.ts
import { Firestore } from '@google-cloud/firestore';
import { Agent } from '../../domain/entities/agent';
import { AgentRepository } from '../../domain/repositories/agent-repository';

export class FirestoreAgentRepository implements AgentRepository {
  private readonly collection = 'agents';
  
  constructor(private readonly db: Firestore) {}

  async save(agent: Agent): Promise<void> {
    const data = this.toPersistence(agent);
    await this.db.collection(this.collection).doc(agent.id).set(data);
  }

  async findById(id: string): Promise<Agent | null> {
    const doc = await this.db.collection(this.collection).doc(id).get();
    
    if (!doc.exists) {
      return null;
    }
    
    return this.toDomain(doc.id, doc.data()!);
  }

  private toPersistence(agent: Agent): Record<string, any> {
    return {
      name: agent.name,
      type: agent.type,
      capabilities: agent.capabilities,
      status: agent.status,
      createdAt: agent.createdAt,
      updatedAt: agent.updatedAt
    };
  }

  private toDomain(id: string, data: any): Agent {
    return Agent.restore({
      id,
      name: data.name,
      type: data.type,
      capabilities: data.capabilities,
      status: data.status,
      createdAt: data.createdAt.toDate(),
      updatedAt: data.updatedAt.toDate()
    });
  }
}
```

## Test Organization

### Test File Structure

Tests mirror source structure:

```
tests/
├── unit/
│   ├── domain/
│   │   ├── entities/
│   │   │   └── agent.test.ts
│   │   └── value-objects/
│   │       └── agent-type.test.ts
│   ├── application/
│   │   └── services/
│   │       └── agent-service.test.ts
│   └── utils/
│       └── test-helpers.ts
├── integration/
│   ├── api/
│   │   └── agent-api.test.ts
│   ├── database/
│   │   └── agent-repository.test.ts
│   └── messaging/
│       └── event-bus.test.ts
└── e2e/
    ├── scenarios/
    │   ├── create-agent.e2e.ts
    │   └── task-execution.e2e.ts
    └── fixtures/
        └── test-data.ts
```

### Test File Naming

```typescript
// ✅ GOOD - Clear test organization
// tests/unit/domain/entities/agent.test.ts
describe('Agent Entity', () => {
  describe('create', () => {
    it('should create agent with valid data', () => {
      // Test implementation
    });
  });
});

// tests/integration/api/agent-api.test.ts
describe('Agent API Integration', () => {
  describe('POST /agents', () => {
    it('should create agent and return 201', async () => {
      // Test implementation
    });
  });
});
```

## Configuration Management

### Environment-Based Config

```
config/
├── default.ts          # Default configuration
├── development.ts      # Development overrides
├── test.ts            # Test configuration
├── staging.ts         # Staging configuration
└── production.ts      # Production configuration
```

### Config Structure

```typescript
// config/default.ts
export default {
  server: {
    port: 3000,
    host: '0.0.0.0'
  },
  database: {
    projectId: 'vibematch-dev',
    keyFilename: './service-account.json'
  },
  redis: {
    host: 'localhost',
    port: 6379,
    ttl: 3600
  },
  pubsub: {
    projectId: 'vibematch-dev',
    topicPrefix: 'vibematch'
  },
  logging: {
    level: 'info',
    format: 'json'
  }
};

// config/production.ts
export default {
  server: {
    port: process.env.PORT || 8080
  },
  database: {
    projectId: 'vibematch-prod',
    keyFilename: undefined // Use default credentials
  },
  redis: {
    host: process.env.REDIS_HOST,
    port: parseInt(process.env.REDIS_PORT || '6379')
  },
  logging: {
    level: 'error'
  }
};
```

## Import Organization

### Barrel Exports

Use index.ts for clean imports:

```typescript
// domain/entities/index.ts
export { Agent } from './agent';
export { Task } from './task';
export { User } from './user';

// Usage in other files
import { Agent, Task, User } from '../domain/entities';
```

### Import Ordering

Follow consistent import order:

```typescript
// 1. Node built-ins
import { readFile } from 'fs/promises';
import { join } from 'path';

// 2. External packages
import express from 'express';
import { z } from 'zod';

// 3. Internal packages (monorepo)
import { Logger } from '@vibematch/logger';
import { AgentContract } from '@vibematch/contracts';

// 4. Absolute imports (using path aliases)
import { AgentService } from '@/application/services';
import { Agent } from '@/domain/entities';

// 5. Relative imports
import { validateAgent } from './validators';
import { AgentController } from './agent-controller';

// 6. Type imports
import type { Request, Response } from 'express';
import type { AgentDto } from '@vibematch/contracts';
```

## Build Output Structure

### Compiled Output

```
dist/
├── api/
│   ├── controllers/
│   └── routes/
├── application/
│   └── services/
├── domain/
│   └── entities/
├── infrastructure/
│   └── database/
├── app.js
├── server.js
└── index.js
```

## Docker Structure

### Dockerfile Organization

```dockerfile
# Build stage
FROM node:20-alpine AS builder
WORKDIR /app
COPY package*.json ./
COPY turbo.json ./
COPY packages ./packages
COPY services/agent-service ./services/agent-service
RUN npm ci
RUN npm run build -- --filter=agent-service

# Runtime stage
FROM node:20-alpine
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/packages ./packages
COPY --from=builder /app/services/agent-service/dist ./dist
COPY --from=builder /app/services/agent-service/package.json ./
EXPOSE 3000
CMD ["node", "dist/index.js"]
```

## Common Patterns

### Feature Folders

For larger features, organize by feature:

```
src/features/
├── agent-matching/
│   ├── controllers/
│   ├── services/
│   ├── repositories/
│   └── events/
├── task-execution/
│   ├── controllers/
│   ├── services/
│   ├── repositories/
│   └── events/
└── billing/
    ├── controllers/
    ├── services/
    ├── repositories/
    └── events/
```

### Shared Code

Place shared code in appropriate locations:

```
# Within a service
src/shared/
├── errors/          # Custom error classes
├── utils/          # Service-specific utilities
└── types/          # Service-specific types

# Across services (monorepo packages)
packages/
├── errors/         # Shared error classes
├── utils/          # Shared utilities
└── contracts/      # Shared interfaces
```

## Anti-Patterns to Avoid

### ❌ Circular Dependencies

```typescript
// ❌ BAD - Circular dependency
// services/agent-service.ts
import { TaskService } from './task-service';

// services/task-service.ts
import { AgentService } from './agent-service';
```

### ❌ Mixed Concerns

```typescript
// ❌ BAD - Controller with business logic
class AgentController {
  async create(req: Request, res: Response) {
    // Business logic in controller
    if (req.body.type === 'premium' && !user.isPremium) {
      throw new Error('Premium agents require premium account');
    }
    
    // Direct database access
    const agent = await db.collection('agents').add(req.body);
    res.json(agent);
  }
}
```

### ❌ God Objects

```typescript
// ❌ BAD - Service doing too much
class SuperService {
  // Too many responsibilities
  async createAgent() {}
  async processPayment() {}
  async sendEmail() {}
  async generateReport() {}
  async validateUser() {}
  // ... 50 more methods
}
```

## Project Templates

### New Service Template

Use the service generator:

```bash
npm run generate:service -- --name=notification-service
```

This creates a new service with:
- Standard folder structure
- Basic configuration
- Dockerfile
- Package.json with standard scripts
- README template
- Basic tests setup

---
**Owner**: Architecture Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26