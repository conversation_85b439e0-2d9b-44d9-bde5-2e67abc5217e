# TypeScript Style Guide

> **Purpose**: Define TypeScript coding conventions and best practices  
> **Scope**: All TypeScript code in the VibeMatch codebase  
> **Enforcement**: ESLint rules, pre-commit hooks, code review

## TypeScript Configuration

### Required Compiler Options

All services MUST use these TypeScript compiler options:

```json
{
  "compilerOptions": {
    "target": "ES2022",                    // Node.js 20+ compatible
    "module": "commonjs",                  // Node.js module system
    "lib": ["ES2022"],                     // Available libraries
    "outDir": "./dist",                    // Output directory
    "rootDir": "./src",                    // Source directory
    
    // Strict Type Checking
    "strict": true,                        // Enable all strict options
    "noImplicitAny": true,                 // Error on implicit any
    "strictNullChecks": true,              // Strict null checking
    "strictFunctionTypes": true,           // Strict function types
    "strictBindCallApply": true,           // Strict bind/call/apply
    "strictPropertyInitialization": true,  // Strict property init
    "noImplicitThis": true,                // Error on implicit this
    "alwaysStrict": true,                  // Use strict mode
    
    // Additional Checks
    "noUnusedLocals": true,                // Error on unused locals
    "noUnusedParameters": true,            // Error on unused params
    "noImplicitReturns": true,             // Error on implicit returns
    "noFallthroughCasesInSwitch": true,   // Error on fallthrough
    "noUncheckedIndexedAccess": true,      // Strict index access
    
    // Module Resolution
    "moduleResolution": "node",            // Node resolution
    "esModuleInterop": true,               // ES module interop
    "allowSyntheticDefaultImports": true,  // Synthetic imports
    "resolveJsonModule": true,             // Import JSON
    
    // Emit Options
    "declaration": true,                   // Generate .d.ts files
    "declarationMap": true,                // Generate .d.ts.map
    "sourceMap": true,                     // Generate source maps
    "removeComments": true,                // Remove comments
    
    // Advanced Options
    "skipLibCheck": true,                  // Skip lib checking
    "forceConsistentCasingInFileNames": true, // Consistent casing
    "experimentalDecorators": true,        // Enable decorators
    "emitDecoratorMetadata": true         // Emit decorator metadata
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "coverage"]
}
```

## Type Annotations

### Always Use Explicit Types

```typescript
// ✅ GOOD - Explicit types
const agentId: string = generateId();
const isActive: boolean = true;
const retryCount: number = 3;
const config: AgentConfig = loadConfig();

// ❌ BAD - Implicit types
const agentId = generateId();
const isActive = true;
const retryCount = 3;
const config = loadConfig();
```

### Function Type Annotations

```typescript
// ✅ GOOD - Full type annotations
function calculateScore(
  metrics: AgentMetrics,
  weights: ScoreWeights
): number {
  return metrics.successRate * weights.success +
         metrics.responseTime * weights.speed;
}

// ✅ GOOD - Arrow function with types
const processTask = async (
  task: Task,
  agent: Agent
): Promise<TaskResult> => {
  // Implementation
};

// ❌ BAD - Missing type annotations
function calculateScore(metrics, weights) {
  return metrics.successRate * weights.success;
}
```

### Interface vs Type Alias

Use interfaces for object shapes, type aliases for unions/intersections:

```typescript
// ✅ Interface for objects
interface User {
  id: string;
  email: string;
  roles: Role[];
}

// ✅ Type alias for unions
type Status = 'pending' | 'active' | 'inactive' | 'deleted';

// ✅ Type alias for intersections
type AuthenticatedUser = User & {
  token: string;
  expiresAt: Date;
};

// ❌ BAD - Type alias for simple object
type User = {
  id: string;
  email: string;
};
```

## Async/Await Patterns

### Always Use Async/Await

```typescript
// ✅ GOOD - Async/await
async function fetchAgent(id: string): Promise<Agent> {
  try {
    const agent = await agentRepository.findById(id);
    if (!agent) {
      throw new NotFoundError(`Agent ${id} not found`);
    }
    return agent;
  } catch (error) {
    logger.error('Failed to fetch agent', { id, error });
    throw error;
  }
}

// ❌ BAD - Promise chains
function fetchAgent(id: string): Promise<Agent> {
  return agentRepository.findById(id)
    .then(agent => {
      if (!agent) {
        throw new NotFoundError(`Agent ${id} not found`);
      }
      return agent;
    })
    .catch(error => {
      logger.error('Failed to fetch agent', { id, error });
      throw error;
    });
}
```

### Parallel Async Operations

```typescript
// ✅ GOOD - Parallel execution
async function processMultiple(ids: string[]): Promise<Result[]> {
  const results = await Promise.all(
    ids.map(id => processItem(id))
  );
  return results;
}

// ✅ GOOD - With error handling
async function processWithErrors(ids: string[]): Promise<SettledResult[]> {
  const results = await Promise.allSettled(
    ids.map(id => processItem(id))
  );
  
  const failures = results.filter(r => r.status === 'rejected');
  if (failures.length > 0) {
    logger.warn('Some items failed', { failures });
  }
  
  return results;
}

// ❌ BAD - Sequential when parallel is possible
async function processMultiple(ids: string[]): Promise<Result[]> {
  const results: Result[] = [];
  for (const id of ids) {
    results.push(await processItem(id));
  }
  return results;
}
```

## Error Handling

### Custom Error Classes

```typescript
// ✅ GOOD - Custom error classes
export class AppError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly statusCode: number,
    public readonly isOperational: boolean = true
  ) {
    super(message);
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, public readonly errors: ValidationErrorDetail[]) {
    super(message, 'VALIDATION_ERROR', 400);
  }
}

// Usage
throw new ValidationError('Invalid input', [
  { field: 'email', message: 'Invalid email format' }
]);
```

### Error Handling Patterns

```typescript
// ✅ GOOD - Comprehensive error handling
async function executeTask(taskId: string): Promise<TaskResult> {
  try {
    const task = await fetchTask(taskId);
    const result = await processTask(task);
    await saveResult(result);
    return result;
  } catch (error) {
    if (error instanceof ValidationError) {
      logger.warn('Validation failed', { taskId, errors: error.errors });
      throw error;
    }
    
    if (error instanceof NetworkError) {
      logger.error('Network error', { taskId, error });
      throw new ServiceUnavailableError('Service temporarily unavailable');
    }
    
    logger.error('Unexpected error', { taskId, error });
    throw new InternalError('An unexpected error occurred');
  }
}

// ✅ GOOD - Error recovery
async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3
): Promise<T> {
  let lastError: Error;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      if (i < maxRetries - 1) {
        await delay(Math.pow(2, i) * 1000); // Exponential backoff
      }
    }
  }
  
  throw lastError!;
}
```

## Class Design

### Class Structure

```typescript
// ✅ GOOD - Well-structured class
export class AgentService {
  // Private fields first
  private readonly repository: AgentRepository;
  private readonly eventBus: EventBus;
  private readonly logger: Logger;
  
  // Constructor
  constructor(
    repository: AgentRepository,
    eventBus: EventBus,
    logger: Logger
  ) {
    this.repository = repository;
    this.eventBus = eventBus;
    this.logger = logger;
  }
  
  // Public methods
  async createAgent(dto: CreateAgentDto): Promise<Agent> {
    this.logger.info('Creating agent', { dto });
    
    const agent = await this.repository.create(dto);
    await this.publishAgentCreated(agent);
    
    return agent;
  }
  
  // Private methods last
  private async publishAgentCreated(agent: Agent): Promise<void> {
    await this.eventBus.publish('agent.created', {
      agentId: agent.id,
      timestamp: new Date()
    });
  }
}
```

### Dependency Injection

```typescript
// ✅ GOOD - Constructor injection
export class TaskProcessor {
  constructor(
    private readonly agentService: AgentService,
    private readonly taskRepository: TaskRepository,
    private readonly metricsCollector: MetricsCollector
  ) {}
  
  async process(taskId: string): Promise<void> {
    const task = await this.taskRepository.findById(taskId);
    const agent = await this.agentService.findBest(task);
    await this.executeTask(task, agent);
  }
}

// ❌ BAD - Hard dependencies
export class TaskProcessor {
  private agentService = new AgentService();
  private taskRepository = new TaskRepository();
  
  async process(taskId: string): Promise<void> {
    // Hard to test and maintain
  }
}
```

## Generics

### Generic Functions

```typescript
// ✅ GOOD - Type-safe generic function
function pick<T, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
}

// Usage
const user = { id: '1', name: 'John', email: '<EMAIL>' };
const publicData = pick(user, ['id', 'name']); // Type: { id: string, name: string }
```

### Generic Classes

```typescript
// ✅ GOOD - Generic repository pattern
export abstract class BaseRepository<T extends BaseEntity> {
  constructor(
    protected readonly collection: string,
    protected readonly db: Database
  ) {}
  
  async findById(id: string): Promise<T | null> {
    return this.db.collection(this.collection).findOne({ id });
  }
  
  async create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T> {
    const entity = {
      ...data,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    } as T;
    
    await this.db.collection(this.collection).insertOne(entity);
    return entity;
  }
}

export class AgentRepository extends BaseRepository<Agent> {
  constructor(db: Database) {
    super('agents', db);
  }
  
  async findByType(type: AgentType): Promise<Agent[]> {
    return this.db.collection(this.collection).find({ type }).toArray();
  }
}
```

## Enums and Constants

### String Enums Preferred

```typescript
// ✅ GOOD - String enum
export enum TaskStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// ✅ GOOD - Const assertion for literals
export const AGENT_TYPES = ['code', 'content', 'data', 'creative'] as const;
export type AgentType = typeof AGENT_TYPES[number];

// ❌ BAD - Numeric enum
enum TaskStatus {
  PENDING,    // 0
  RUNNING,    // 1
  COMPLETED   // 2
}
```

### Constants Organization

```typescript
// ✅ GOOD - Grouped constants
export const API_CONSTANTS = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  DEFAULT_TIMEOUT: 30000,
  MAX_RETRY_ATTEMPTS: 3
} as const;

export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  UNAUTHORIZED: 'UNAUTHORIZED',
  INTERNAL_ERROR: 'INTERNAL_ERROR'
} as const;

// Type from const
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];
```

## Import Organization

### Import Order

```typescript
// 1. Node built-ins
import { readFile } from 'fs/promises';
import { join } from 'path';

// 2. External packages
import express from 'express';
import { z } from 'zod';

// 3. Internal packages
import { Logger } from '@vibematch/logger';
import { Database } from '@vibematch/database';

// 4. Parent imports
import { BaseService } from '../base';

// 5. Sibling imports
import { AgentRepository } from './agent-repository';

// 6. Child imports
import { validateAgent } from './validators';

// 7. Type imports
import type { Request, Response } from 'express';
import type { Agent, Task } from './types';
```

## Comments and Documentation

### JSDoc for Public APIs

```typescript
/**
 * Calculates the match score between an agent and a task.
 * 
 * @param agent - The agent to evaluate
 * @param task - The task requirements
 * @param weights - Optional scoring weights
 * @returns A score between 0 and 1, where 1 is a perfect match
 * 
 * @example
 * ```typescript
 * const score = calculateMatchScore(agent, task);
 * if (score > 0.8) {
 *   await assignTask(agent, task);
 * }
 * ```
 */
export function calculateMatchScore(
  agent: Agent,
  task: Task,
  weights?: ScoringWeights
): number {
  // Implementation
}
```

### Inline Comments

```typescript
// ✅ GOOD - Explains why, not what
function processPayment(amount: number): void {
  // Round to 2 decimal places to avoid floating point issues
  const rounded = Math.round(amount * 100) / 100;
  
  // Stripe requires amounts in cents
  const cents = Math.floor(rounded * 100);
  
  // Process payment...
}

// ❌ BAD - States the obvious
function processPayment(amount: number): void {
  // Round the amount
  const rounded = Math.round(amount * 100) / 100;
  
  // Multiply by 100
  const cents = Math.floor(rounded * 100);
}
```

## Performance Considerations

### Avoid Premature Optimization

```typescript
// ✅ GOOD - Clear and maintainable
const activeAgents = agents.filter(agent => agent.status === 'active');

// ❌ BAD - Premature optimization
const activeAgents: Agent[] = [];
for (let i = 0, len = agents.length; i < len; i++) {
  if (agents[i].status === 'active') {
    activeAgents.push(agents[i]);
  }
}
```

### Use Appropriate Data Structures

```typescript
// ✅ GOOD - Map for lookups
const agentMap = new Map<string, Agent>();
agents.forEach(agent => agentMap.set(agent.id, agent));

// Fast lookup
const agent = agentMap.get(agentId);

// ❌ BAD - Array for lookups
const agent = agents.find(a => a.id === agentId);
```

## Testing Patterns

### Test File Structure

```typescript
// ✅ GOOD - Organized test structure
describe('AgentService', () => {
  let service: AgentService;
  let repository: MockRepository;
  
  beforeEach(() => {
    repository = createMockRepository();
    service = new AgentService(repository);
  });
  
  describe('createAgent', () => {
    it('should create agent with valid data', async () => {
      // Arrange
      const dto = { name: 'Test Agent', type: 'code' };
      
      // Act
      const agent = await service.createAgent(dto);
      
      // Assert
      expect(agent).toMatchObject({
        name: dto.name,
        type: dto.type,
        status: 'pending'
      });
    });
    
    it('should throw ValidationError for invalid data', async () => {
      // Arrange
      const dto = { name: '', type: 'invalid' };
      
      // Act & Assert
      await expect(service.createAgent(dto))
        .rejects
        .toThrow(ValidationError);
    });
  });
});
```

## Common Patterns

### Builder Pattern

```typescript
// ✅ GOOD - Fluent builder pattern
export class TaskBuilder {
  private task: Partial<Task> = {};
  
  withTitle(title: string): this {
    this.task.title = title;
    return this;
  }
  
  withDescription(description: string): this {
    this.task.description = description;
    return this;
  }
  
  withPriority(priority: Priority): this {
    this.task.priority = priority;
    return this;
  }
  
  build(): Task {
    if (!this.task.title) {
      throw new Error('Title is required');
    }
    
    return {
      id: generateId(),
      ...this.task,
      createdAt: new Date()
    } as Task;
  }
}

// Usage
const task = new TaskBuilder()
  .withTitle('Implement feature')
  .withDescription('Add new functionality')
  .withPriority(Priority.HIGH)
  .build();
```

### Factory Pattern

```typescript
// ✅ GOOD - Type-safe factory
export class ServiceFactory {
  constructor(
    private readonly config: Config,
    private readonly logger: Logger
  ) {}
  
  createAgentService(): AgentService {
    const repository = new AgentRepository(this.config.database);
    const eventBus = new EventBus(this.config.eventBus);
    return new AgentService(repository, eventBus, this.logger);
  }
  
  createTaskService(): TaskService {
    const repository = new TaskRepository(this.config.database);
    const agentService = this.createAgentService();
    return new TaskService(repository, agentService, this.logger);
  }
}
```

## Enforcement

### ESLint Rules

See [eslint-config.js](./eslint-config.js) for full configuration.

### Pre-commit Hooks

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.ts": [
      "eslint --fix",
      "prettier --write"
    ]
  }
}
```

---
**Owner**: Development Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26