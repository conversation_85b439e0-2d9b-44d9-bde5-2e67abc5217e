# SSRF Protection Standards

> **Purpose**: Server-Side Request Forgery prevention guidelines  
> **Audience**: Backend developers handling external requests

## Overview
SSRF protection standards for preventing malicious server-side requests. Critical for services that fetch external resources or interact with user-provided URLs.

## Directory Structure
```
ssrf-protection/
├── ssrf-prevention-guide.md  # Core prevention techniques
├── url-validation.md         # URL allowlist/blocklist
├── request-proxying.md       # Safe external requests
└── monitoring-ssrf.md        # Detection and logging
```

## Quick Links
- [SSRF Prevention Guide](./ssrf-prevention-guide.md) - Core techniques
- [URL Validation](./url-validation.md) - Allowlist patterns
- [Request Proxying](./request-proxying.md) - Safe HTTP clients
- [Monitoring SSRF](./monitoring-ssrf.md) - Detection strategies

## Key Protections
1. **URL Validation** - Strict allowlisting
2. **Network Isolation** - Separate request proxy
3. **Protocol Restrictions** - HTTPS only
4. **Internal IP Blocking** - No private ranges
5. **Timeout Controls** - Prevent slowloris

## SSRF Prevention Pattern
### URL Validation
```typescript
const ALLOWED_DOMAINS = ['api.openai.com', 'storage.googleapis.com'];

function validateUrl(url: string): void {
  const parsed = new URL(url);
  
  // Protocol check
  if (parsed.protocol !== 'https:') {
    throw new SecurityError('Only HTTPS allowed');
  }
  
  // Domain allowlist
  if (!ALLOWED_DOMAINS.includes(parsed.hostname)) {
    throw new SecurityError('Domain not allowed');
  }
  
  // No internal IPs
  if (isPrivateIP(parsed.hostname)) {
    throw new SecurityError('Internal IPs blocked');
  }
}
```

### Safe Request Pattern
```typescript
// Use dedicated HTTP client with restrictions
const safeClient = axios.create({
  timeout: 5000,
  maxRedirects: 0,
  validateStatus: (status) => status < 400
});
```

---
**Section Owner**: Security Team  
**Last Updated**: 2025-06-26  
**Parent**: [Security Standards](../)