# Simplified RBAC (Role-Based Access Control)

> **Purpose**: Role-based authorization patterns for VibeMatch  
> **Audience**: Backend developers implementing authorization

## Overview
Simplified RBAC implementation using a three-role system (User, Agent, Admin) with clear permission boundaries. Designed for maintainability and security while avoiding over-complexity.

## Directory Structure
```
simplified-rbac/
├── rbac-design.md          # RBAC architecture
├── role-definitions.md     # Role permissions matrix
├── implementation-guide.md # Code patterns
└── migration-guide.md      # Adding new permissions
```

## Quick Links
- [RBAC Design](./rbac-design.md) - Architecture overview
- [Role Definitions](./role-definitions.md) - Permission matrix
- [Implementation Guide](./implementation-guide.md) - Code examples
- [Migration Guide](./migration-guide.md) - Extending permissions

## Role Structure
### Core Roles
1. **User** - Basic platform access
   - View agents, create tasks, manage own profile
2. **Agent** - Service provider access
   - All user permissions + manage listings, accept tasks
3. **Admin** - Platform administration
   - All permissions + user management, content moderation

## Implementation Pattern
### Permission Check
```typescript
// Middleware for route protection
export const requireRole = (role: Role) => {
  return async (req: Request, res: Response, next: Next) => {
    if (!req.user?.roles.includes(role)) {
      return res.status(403).json({
        error: 'Insufficient permissions'
      });
    }
    next();
  };
};

// Usage
router.post('/admin/users/:id/suspend', 
  requireRole('admin'), 
  suspendUser
);
```

### Resource-level Authorization
```typescript
// Check resource ownership
const canEditTask = (user: User, task: Task): boolean => {
  return task.userId === user.id || user.hasRole('admin');
};
```

---
**Section Owner**: Security Team  
**Last Updated**: 2025-06-26  
**Parent**: [Security Standards](../)