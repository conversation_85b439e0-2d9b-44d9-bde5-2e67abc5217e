# API Authentication Patterns

> **Purpose**: Define authentication and authorization patterns for VibeMatch APIs  
> **Scope**: All API endpoints requiring authentication  
> **Enforcement**: API Gateway authentication middleware, security audits

## Authentication Strategy

### 1. Primary Authentication Method

VibeMatch uses **JWT (JSON Web Tokens)** with Google Identity Platform:

```http
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### JWT Structure
```json
{
  "header": {
    "alg": "RS256",
    "typ": "JWT",
    "kid": "key-id-from-google"
  },
  "payload": {
    "iss": "https://securetoken.google.com/vibematch-prod",
    "aud": "vibematch-prod",
    "auth_time": 1719404400,
    "user_id": "550e8400-e29b-41d4-a716-446655440000",
    "sub": "550e8400-e29b-41d4-a716-446655440000",
    "iat": 1719404400,
    "exp": 1719408000,
    "email": "<EMAIL>",
    "email_verified": true,
    "roles": ["user", "agent_manager"],
    "custom_claims": {
      "organizationId": "org-123",
      "tier": "premium"
    }
  }
}
```

### 2. Authentication Flow

#### Standard Login Flow
```mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant Auth Service
    participant Google Identity
    
    Client->>Auth Service: POST /auth/login {email, password}
    Auth Service->>Google Identity: Verify credentials
    Google Identity-->>Auth Service: User verified
    Auth Service-->>Client: JWT token + refresh token
    Client->>API Gateway: GET /api/v1/agents [Bearer token]
    API Gateway->>API Gateway: Validate JWT
    API Gateway-->>Client: Protected resource
```

#### Token Refresh Flow
```mermaid
sequenceDiagram
    participant Client
    participant Auth Service
    participant Google Identity
    
    Client->>Auth Service: POST /auth/refresh {refresh_token}
    Auth Service->>Google Identity: Validate refresh token
    Google Identity-->>Auth Service: Token valid
    Auth Service-->>Client: New JWT + refresh token
```

### 3. OAuth2 Integration

For third-party integrations, support OAuth2 flow:

#### Authorization Code Flow
```
1. Redirect to authorization:
GET https://auth.vibematch.io/oauth/authorize?
    response_type=code&
    client_id=CLIENT_ID&
    redirect_uri=REDIRECT_URI&
    scope=agents:read tasks:write&
    state=RANDOM_STATE

2. User authorizes, redirected back:
GET https://yourapp.com/callback?
    code=AUTHORIZATION_CODE&
    state=RANDOM_STATE

3. Exchange code for token:
POST https://auth.vibematch.io/oauth/token
{
  "grant_type": "authorization_code",
  "code": "AUTHORIZATION_CODE",
  "client_id": "CLIENT_ID",
  "client_secret": "CLIENT_SECRET",
  "redirect_uri": "REDIRECT_URI"
}

4. Receive tokens:
{
  "access_token": "eyJ...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "..."
}
```

### 4. API Key Authentication

For service-to-service communication:

```http
X-API-Key: vm_live_550e8400e29b41d4a716446655440000
X-API-Secret: vm_secret_660e8400e29b41d4a716446655440000
```

#### API Key Format
- Prefix: `vm_live_` for production, `vm_test_` for testing
- 32 character hex string
- Paired with secret for added security

### 5. Multi-Factor Authentication (MFA)

For sensitive operations, require MFA:

```http
POST /api/v1/payments
Authorization: Bearer {jwt-token}
X-MFA-Token: 123456
```

#### MFA Challenge Flow
```json
// Initial request without MFA
POST /api/v1/payments
Response: 401 Unauthorized
{
  "error": {
    "code": "MFA_REQUIRED",
    "message": "Multi-factor authentication required",
    "mfa_methods": ["totp", "sms", "email"]
  }
}

// Request with MFA token
POST /api/v1/payments
X-MFA-Token: 123456
Response: 200 OK
```

## Authorization Patterns

### 1. Role-Based Access Control (RBAC)

Roles defined in JWT claims:

```typescript
enum Role {
  USER = 'user',
  AGENT = 'agent',
  AGENT_MANAGER = 'agent_manager',
  USER_MANAGER = 'user_manager',
  PLATFORM_MANAGER = 'platform_manager',
  FRAUD_ANALYST = 'fraud_analyst',
  FRAUD_MANAGER = 'fraud_manager',
  COMPLIANCE_OFFICER = 'compliance_officer',
  ADMIN = 'admin'
}
```

#### Authorization Middleware
```typescript
// Express middleware example
const requireRole = (roles: Role[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const userRoles = req.user.roles || [];
    const hasRole = roles.some(role => userRoles.includes(role));
    
    if (!hasRole) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'User lacks required role',
          required_roles: roles
        }
      });
    }
    
    next();
  };
};

// Usage
router.get('/admin/users', 
  authenticate,
  requireRole([Role.USER_MANAGER, Role.ADMIN]),
  getUsers
);
```

### 2. Resource-Based Access Control

Check ownership or permissions on specific resources:

```typescript
// Check resource ownership
const requireOwnership = async (req: Request, res: Response, next: NextFunction) => {
  const resourceId = req.params.id;
  const userId = req.user.sub;
  
  const resource = await getResource(resourceId);
  
  if (resource.ownerId !== userId && !req.user.roles.includes('admin')) {
    return res.status(403).json({
      error: {
        code: 'RESOURCE_ACCESS_DENIED',
        message: 'User does not have access to this resource'
      }
    });
  }
  
  req.resource = resource;
  next();
};
```

### 3. Scope-Based Permissions

For OAuth2 and API keys:

```typescript
// Define scopes
const SCOPES = {
  'agents:read': 'Read agent information',
  'agents:write': 'Create and update agents',
  'agents:delete': 'Delete agents',
  'tasks:read': 'Read task information',
  'tasks:write': 'Create and update tasks',
  'tasks:execute': 'Execute tasks',
  'billing:read': 'View billing information',
  'billing:write': 'Update billing information'
};

// Check scopes
const requireScope = (requiredScopes: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const tokenScopes = req.token.scopes || [];
    const hasScope = requiredScopes.every(scope => tokenScopes.includes(scope));
    
    if (!hasScope) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_SCOPE',
          message: 'Token lacks required scope',
          required_scopes: requiredScopes
        }
      });
    }
    
    next();
  };
};
```

## Security Headers

### Required Security Headers
```http
# Request headers
Authorization: Bearer {token}
X-Request-ID: 550e8400-e29b-41d4-a716-446655440000
X-Client-Version: 1.2.3

# Response headers
X-Request-ID: 550e8400-e29b-41d4-a716-446655440000
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'
```

### Rate Limiting by Authentication Type
```typescript
const rateLimits = {
  anonymous: { requests: 10, window: '1m' },
  authenticated: { requests: 100, window: '1m' },
  api_key: { requests: 1000, window: '1m' },
  premium: { requests: 10000, window: '1m' }
};
```

## Token Management

### 1. Token Expiration
- Access tokens: 1 hour
- Refresh tokens: 30 days
- API keys: No expiration (manual revocation)

### 2. Token Rotation
```typescript
// Automatic token refresh
class TokenManager {
  async getValidToken(): Promise<string> {
    if (this.isTokenExpired()) {
      return this.refreshToken();
    }
    return this.currentToken;
  }
  
  private isTokenExpired(): boolean {
    const payload = this.decodeToken(this.currentToken);
    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();
    const bufferTime = 5 * 60 * 1000; // 5 minutes
    
    return currentTime >= (expirationTime - bufferTime);
  }
}
```

### 3. Token Revocation
```typescript
// Revocation endpoint
POST /auth/revoke
{
  "token": "eyJ...",
  "token_type_hint": "access_token"
}

// Check revocation status
const isTokenRevoked = async (jti: string): Promise<boolean> => {
  return await redis.exists(`revoked_token:${jti}`);
};
```

## Implementation Examples

### 1. Express.js Middleware
```typescript
import { Request, Response, NextFunction } from 'express';
import { verifyIdToken } from './auth';

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      error: {
        code: 'MISSING_AUTH_TOKEN',
        message: 'Authorization header required'
      }
    });
  }
  
  const token = authHeader.substring(7);
  
  try {
    const decodedToken = await verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    return res.status(401).json({
      error: {
        code: 'INVALID_AUTH_TOKEN',
        message: 'Invalid or expired token'
      }
    });
  }
};
```

### 2. API Gateway Configuration
```yaml
# Kong Gateway example
plugins:
  - name: jwt
    config:
      uri_param_names:
        - jwt
      claims_to_verify:
        - exp
        - iat
      key_claim_name: iss
      secret_is_base64: false
      
  - name: acl
    config:
      whitelist:
        - admin
        - user
      hide_groups_header: true
      
  - name: rate-limiting
    config:
      policy: local
      minute: 100
      hour: 10000
```

### 3. Client SDK Authentication
```typescript
class VibeMatchClient {
  private token: string;
  private refreshToken: string;
  
  constructor(private config: ClientConfig) {}
  
  async authenticate(email: string, password: string): Promise<void> {
    const response = await fetch(`${this.config.authUrl}/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });
    
    const { access_token, refresh_token } = await response.json();
    this.token = access_token;
    this.refreshToken = refresh_token;
  }
  
  async request(path: string, options: RequestInit = {}): Promise<Response> {
    const token = await this.getValidToken();
    
    return fetch(`${this.config.apiUrl}${path}`, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
        'X-Client-Version': this.config.version
      }
    });
  }
}
```

## Testing Authentication

### 1. Unit Tests
```typescript
describe('Authentication Middleware', () => {
  it('should reject requests without token', async () => {
    const req = mockRequest({});
    const res = mockResponse();
    const next = jest.fn();
    
    await authenticate(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(401);
    expect(next).not.toHaveBeenCalled();
  });
  
  it('should accept valid token', async () => {
    const token = await generateTestToken({ sub: 'user-123' });
    const req = mockRequest({
      headers: { authorization: `Bearer ${token}` }
    });
    const res = mockResponse();
    const next = jest.fn();
    
    await authenticate(req, res, next);
    
    expect(next).toHaveBeenCalled();
    expect(req.user).toHaveProperty('sub', 'user-123');
  });
});
```

### 2. Integration Tests
```typescript
describe('API Authentication', () => {
  it('should require authentication for protected endpoints', async () => {
    const response = await request(app)
      .get('/api/v1/agents')
      .expect(401);
      
    expect(response.body.error.code).toBe('MISSING_AUTH_TOKEN');
  });
  
  it('should allow access with valid token', async () => {
    const token = await getAuthToken();
    
    const response = await request(app)
      .get('/api/v1/agents')
      .set('Authorization', `Bearer ${token}`)
      .expect(200);
      
    expect(response.body.data).toBeInstanceOf(Array);
  });
});
```

## Security Best Practices

### 1. Token Storage
- **Browser**: Use httpOnly cookies or secure localStorage
- **Mobile**: Use secure keychain/keystore
- **Server**: Never log tokens, use secure storage

### 2. Token Transmission
- Always use HTTPS
- Include in Authorization header, not URL
- Don't expose in client-side code

### 3. Token Validation
- Verify signature
- Check expiration
- Validate issuer and audience
- Check revocation status

### 4. Error Handling
Never reveal token validation details:
```json
// ❌ Bad - reveals too much
{
  "error": "Token expired at 2025-06-26T10:30:00Z"
}

// ✅ Good - generic error
{
  "error": {
    "code": "INVALID_TOKEN",
    "message": "Authentication failed"
  }
}
```

## Monitoring and Alerts

### Authentication Metrics
```typescript
// Track authentication metrics
metrics.increment('auth.login.attempt');
metrics.increment('auth.login.success');
metrics.increment('auth.login.failure', { reason: 'invalid_credentials' });
metrics.increment('auth.token.refresh');
metrics.increment('auth.token.revoked');
```

### Security Alerts
```typescript
// Alert on suspicious activity
if (failedAttempts > 5) {
  alerts.send('Multiple failed login attempts', {
    user_id: userId,
    ip_address: ipAddress,
    attempts: failedAttempts
  });
}
```

---
**Owner**: Security Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26