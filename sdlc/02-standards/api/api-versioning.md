# API Versioning Strategy

> **Purpose**: Define versioning approach for backward compatibility and evolution  
> **Scope**: All public and internal APIs  
> **Enforcement**: API Gateway version routing, deprecation monitoring

## Versioning Strategy

### 1. Version Format
We use **URL path versioning** for clarity and simplicity:
```
https://api.vibematch.io/api/v{major}/resource
```

#### Why URL Versioning?
- **Explicit**: Version is immediately visible
- **Cache-friendly**: Different versions have different URLs
- **Tool-friendly**: Works with all HTTP clients
- **Clear**: No ambiguity about which version is used

### 2. Version Numbering

#### Major Versions Only
- Only major versions in URL: `v1`, `v2`, `v3`
- Minor/patch versions handled without URL change
- Follows semantic versioning principles

#### Version Increments
| Change Type | Version Action | Example |
|------------|----------------|---------|
| Breaking change | New major version | v1 → v2 |
| New optional field | Same version | v1 (add field) |
| New endpoint | Same version | v1 (add endpoint) |
| Deprecation | Same version + warning | v1 (deprecation notice) |
| Bug fix | Same version | v1 (fix behavior) |

### 3. Breaking Changes

#### What Constitutes a Breaking Change?
- Removing an endpoint
- Removing or renaming a field
- Changing field type or format
- Changing authentication method
- Modifying validation rules (stricter)
- Changing error response format

#### Non-Breaking Changes
- Adding optional fields
- Adding new endpoints
- Adding new optional parameters
- Making validation less strict
- Adding new error codes
- Performance improvements

### 4. Version Lifecycle

```mermaid
graph LR
    A[Development] --> B[Beta]
    B --> C[GA/Stable]
    C --> D[Deprecated]
    D --> E[Sunset]
    E --> F[Removed]
```

#### Lifecycle Stages

| Stage | Duration | Support Level | Changes Allowed |
|-------|----------|---------------|-----------------|
| Development | Variable | None | Any changes |
| Beta | 3-6 months | Best effort | Breaking changes with notice |
| GA/Stable | 12+ months | Full support | Non-breaking only |
| Deprecated | 6 months | Security only | None |
| Sunset | 3 months | None | None |
| Removed | - | - | - |

### 5. Deprecation Policy

#### Deprecation Timeline
1. **Announcement** (T-9 months): Deprecation announced
2. **Warning** (T-6 months): API returns deprecation headers
3. **Sunset Notice** (T-3 months): Final migration warning
4. **Removal** (T-0): API version removed

#### Deprecation Headers
```http
Deprecation: true
Sunset: Sat, 31 Dec 2025 23:59:59 GMT
Link: <https://docs.vibematch.io/migrations/v2>; rel="successor-version"
Warning: 299 - "This API version is deprecated. Please migrate to v2"
```

#### Deprecation Response
```json
{
  "data": { ... },
  "warnings": [
    {
      "code": "DEPRECATION_WARNING",
      "message": "API v1 is deprecated and will be removed on 2025-12-31",
      "migration_guide": "https://docs.vibematch.io/migrations/v2"
    }
  ]
}
```

### 6. Version Migration

#### Migration Guide Template
```markdown
# Migrating from v1 to v2

## Overview
- **Deprecation Date**: 2025-06-26
- **Sunset Date**: 2025-12-31
- **Migration Deadline**: 2025-12-31

## Breaking Changes

### 1. Authentication Method Changed
**v1**: API Key in header
```http
X-API-Key: your-api-key
```

**v2**: Bearer token
```http
Authorization: Bearer your-token
```

### 2. Response Format Updated
**v1 Response**:
```json
{
  "id": "123",
  "name": "Agent"
}
```

**v2 Response**:
```json
{
  "data": {
    "id": "123",
    "type": "agent",
    "attributes": {
      "name": "Agent"
    }
  }
}
```

## Migration Steps
1. Update authentication method
2. Update response parsing
3. Test in staging environment
4. Deploy changes
5. Monitor for errors
```

### 7. Multiple Version Support

#### API Gateway Configuration
```yaml
routes:
  - path: /api/v1/*
    backend: service-v1.cluster.local
    deprecated: true
    sunset: "2025-12-31"
    
  - path: /api/v2/*
    backend: service-v2.cluster.local
    current: true
    
  - path: /api/latest/*
    redirect: /api/v2/*
```

#### Code Organization
```
services/
├── agent-service/
│   ├── src/
│   │   ├── v1/
│   │   │   ├── controllers/
│   │   │   ├── models/
│   │   │   └── routes/
│   │   ├── v2/
│   │   │   ├── controllers/
│   │   │   ├── models/
│   │   │   └── routes/
│   │   └── shared/
│   │       └── utils/
```

### 8. Version Discovery

#### Version Endpoint
```http
GET /api/versions

{
  "versions": [
    {
      "version": "v1",
      "status": "deprecated",
      "deprecation_date": "2025-06-26",
      "sunset_date": "2025-12-31",
      "documentation": "https://docs.vibematch.io/api/v1"
    },
    {
      "version": "v2",
      "status": "current",
      "release_date": "2025-01-01",
      "documentation": "https://docs.vibematch.io/api/v2"
    }
  ],
  "current": "v2",
  "minimum_supported": "v1"
}
```

### 9. Client Version Management

#### SDK Versioning
```typescript
// TypeScript SDK
import { VibeMatchClient } from '@vibematch/sdk';

// Explicit version
const client = new VibeMatchClient({
  version: 'v2',
  apiKey: process.env.VIBEMATCH_API_KEY
});

// Auto-select latest stable
const client = new VibeMatchClient({
  version: 'latest',
  apiKey: process.env.VIBEMATCH_API_KEY
});
```

#### Version Compatibility Matrix
| Client Version | API v1 | API v2 | API v3 |
|---------------|--------|--------|--------|
| SDK 1.x | ✅ | ❌ | ❌ |
| SDK 2.x | ⚠️ | ✅ | ❌ |
| SDK 3.x | ❌ | ⚠️ | ✅ |

✅ Full support | ⚠️ Compatibility mode | ❌ Not supported

## Monitoring and Metrics

### Version Usage Metrics
```typescript
// Track version usage
metrics.increment('api.request', {
  version: 'v1',
  endpoint: '/agents',
  client: request.headers['user-agent']
});

// Alert on deprecated version usage
if (version === 'v1' && deprecatedUsageRate > 0.1) {
  alerts.send('High deprecated API usage', {
    version: 'v1',
    usage_rate: deprecatedUsageRate,
    sunset_date: '2025-12-31'
  });
}
```

### Migration Tracking Dashboard
- Current version adoption rate
- Deprecated version usage trends
- Client version distribution
- Migration blockers by endpoint

## Enforcement

### CI/CD Checks
```yaml
version-check:
  - validate-openapi-version
  - check-breaking-changes
  - update-deprecation-notices
  - generate-migration-guide
```

### Code Review Checklist
- [ ] Breaking changes require new major version
- [ ] Deprecation headers added for old endpoints
- [ ] Migration guide updated
- [ ] Version discovery endpoint updated
- [ ] Client SDKs updated to support new version

## Exceptions

Version strategy exceptions require:
1. Business justification (e.g., critical security fix)
2. Architecture team approval
3. Communication plan for affected users
4. Accelerated migration timeline if needed

---
**Owner**: API Architecture Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26