# API Design Guidelines

> **Purpose**: Core REST API design principles and best practices for VibeMatch  
> **Scope**: All HTTP/REST APIs across the platform  
> **Audience**: API developers, architects, and technical leads  
> **Compliance**: RFC 2119 keyword requirements (MUST, SHOULD, MAY)

## URL Structure Standard

All VibeMatch APIs MUST follow this URL structure:
```
https://api.vibematch.io/v1/{service}/{resource}
```

Examples:
```
https://api.vibematch.io/v1/marketplace/agents
https://api.vibematch.io/v1/orchestrator/executions
https://api.vibematch.io/v1/billing/transactions
```

## Core Design Principles

### 1. API First Development

All features MUST be designed API-first:
- Design API contract before implementation
- Use OpenAPI 3.0 specification as source of truth
- Generate server stubs and client SDKs from spec
- API design review required before coding

### 2. Resource-Oriented Architecture

#### Design Around Resources
APIs MUST model business entities as resources:

```
✅ CORRECT:
GET    /agents/{id}          # Retrieve agent
POST   /agents               # Create agent
PUT    /agents/{id}          # Update agent
DELETE /agents/{id}          # Delete agent

❌ INCORRECT:
GET    /getAgent            # Action-based
POST   /agent/create        # Verb in URL
POST   /updateAgent         # Wrong method
```

#### Resource Hierarchy
Express relationships through URL structure:

```
/users/{userId}/preferences               # User's preferences
/agents/{agentId}/tasks                  # Agent's tasks
/tasks/{taskId}/executions               # Task executions
/marketplace/categories/{catId}/agents   # Agents in category
```

### 3. Consistent Naming Conventions

#### URL Naming Rules
- **MUST** use lowercase letters
- **MUST** use hyphens for multi-word resources
- **MUST** use plural nouns for collections
- **MUST NOT** use underscores or camelCase

```
✅ CORRECT:
/credit-transactions
/agent-profiles
/task-executions

❌ INCORRECT:
/creditTransactions    # camelCase
/credit_transactions   # underscores
/agent-profile        # singular
```

#### Field Naming Rules
- **MUST** use camelCase for JSON fields
- **MUST** be descriptive and unambiguous
- **SHOULD** avoid abbreviations

```json
{
  "agentId": "550e8400-e29b-41d4-a716-446655440000",
  "displayName": "CodeAssistant Pro",
  "createdAt": "2025-06-26T10:30:00Z",
  "isActive": true,
  "responseTimeMs": 245
}
```

### 4. Idempotency and Safety

#### Safe Methods (No Side Effects)
- GET: Retrieve resources
- HEAD: Check resource existence
- OPTIONS: Discover allowed methods

#### Idempotent Methods (Same Result)
- GET, HEAD, OPTIONS: Always safe
- PUT: Full replacement
- DELETE: Remove resource
- PATCH: Partial update (when designed properly)

#### Non-Idempotent Methods
- POST: Create new resources
- Custom actions (use sparingly)

### 5. Statelessness

APIs MUST be stateless:
- No server-side session state
- All context in request
- Use tokens for authentication
- Include all needed data

```http
# Stateless request with full context
GET /api/v1/agents?status=active&page=2
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
X-Request-ID: 550e8400-e29b-41d4-a716-446655440000
```

### 6. Standard HTTP Status Codes

APIs MUST use standard HTTP status codes consistently:

#### Success Codes
- **200 OK**: Successful GET, PUT, PATCH
- **201 Created**: Successful POST creating resource
- **204 No Content**: Successful DELETE or action with no response body

#### Client Error Codes
- **400 Bad Request**: Invalid request syntax or parameters
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Valid auth but insufficient permissions
- **404 Not Found**: Resource does not exist
- **409 Conflict**: Request conflicts with current state
- **429 Too Many Requests**: Rate limit exceeded

#### Server Error Codes
- **500 Internal Server Error**: Unhandled server error
- **502 Bad Gateway**: Invalid response from upstream
- **503 Service Unavailable**: Service temporarily unavailable

### 7. Response Format Standards

All API responses MUST follow a consistent structure:

#### Success Response
```json
{
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "type": "agent",
    "attributes": {
      "name": "CodeAssistant Pro",
      "status": "active"
    }
  },
  "meta": {
    "timestamp": "2025-06-26T10:30:00Z",
    "version": "1.0.0"
  }
}
```

#### Collection Response
```json
{
  "data": [
    { "id": "123", "type": "agent", "attributes": {...} },
    { "id": "456", "type": "agent", "attributes": {...} }
  ],
  "meta": {
    "total": 42,
    "page": 2,
    "pageSize": 20
  },
  "links": {
    "first": "/api/v1/agents?page=1",
    "prev": "/api/v1/agents?page=1",
    "self": "/api/v1/agents?page=2",
    "next": "/api/v1/agents?page=3",
    "last": "/api/v1/agents?page=3"
  }
}
```

### 6. Standard HTTP Methods

| Method | Resource Collection | Specific Resource | Request Body | Response Body |
|--------|-------------------|------------------|--------------|---------------|
| GET | List all items | Get one item | No | Yes |
| POST | Create new item | Custom action* | Yes | Yes |
| PUT | Replace collection* | Replace item | Yes | Yes |
| PATCH | Bulk update* | Partial update | Yes | Yes |
| DELETE | Delete all* | Delete item | Optional | Optional |

*Use with extreme caution and clear documentation

### 7. Content Negotiation

#### Request Content Type
```http
Content-Type: application/json        # Standard JSON
Content-Type: application/merge-patch+json  # JSON Patch
Content-Type: multipart/form-data    # File uploads
```

#### Response Content Type
```http
Accept: application/json             # Prefer JSON
Accept: application/json, text/csv   # Multiple formats
Accept: application/vnd.vibematch.v2+json  # Vendor specific
```

### 8. Filtering, Sorting, and Searching

#### Filtering
Use query parameters for field-level filtering:
```
GET /agents?status=active&type=code_assistant
GET /agents?createdAfter=2025-01-01T00:00:00Z
GET /agents?rating[gte]=4.5&rating[lte]=5.0
```

#### Sorting
Use `sort` parameter with `-` prefix for descending:
```
GET /agents?sort=createdAt          # Ascending
GET /agents?sort=-rating            # Descending  
GET /agents?sort=-rating,name       # Multiple fields
```

#### Searching
Use `q` or `search` for full-text search:
```
GET /agents?q=javascript+expert
GET /agents?search=machine+learning&fields=name,description
```

### 9. Partial Responses

#### Field Selection
Allow clients to request specific fields:
```
GET /agents?fields=id,name,rating
GET /agents/123?fields=id,name,owner(id,email)
```

Response includes only requested fields:
```json
{
  "id": "123",
  "name": "CodeAssistant",
  "rating": 4.8
}
```

#### Expansion
Allow inclusion of related resources:
```
GET /agents/123?expand=owner,recentTasks
GET /tasks?expand=agent,creator&fields=id,status
```

### 10. HATEOAS (Hypermedia)

Include relevant links in responses:

```json
{
  "data": {
    "id": "123",
    "name": "CodeAssistant",
    "status": "active"
  },
  "links": {
    "self": "/api/v1/agents/123",
    "tasks": "/api/v1/agents/123/tasks",
    "owner": "/api/v1/users/456",
    "activate": "/api/v1/agents/123/activate",
    "deactivate": "/api/v1/agents/123/deactivate"
  }
}
```

### 11. Batch Operations

#### Batch Create
```http
POST /api/v1/agents/batch
Content-Type: application/json

{
  "operations": [
    { "name": "Agent1", "type": "code_assistant" },
    { "name": "Agent2", "type": "data_analyst" }
  ]
}
```

#### Batch Update
```http
PATCH /api/v1/agents/batch
Content-Type: application/json

{
  "operations": [
    { "id": "123", "status": "active" },
    { "id": "456", "status": "inactive" }
  ]
}
```

Response includes individual results:
```json
{
  "results": [
    { "id": "123", "success": true },
    { "id": "456", "success": false, "error": "Not found" }
  ]
}
```

### 12. Asynchronous Operations

For long-running operations, return 202 Accepted:

```http
POST /api/v1/reports/generate
Content-Type: application/json

{
  "type": "usage_analytics",
  "period": "2025-Q1"
}

HTTP/1.1 202 Accepted
Location: /api/v1/jobs/job-789
Content-Type: application/json

{
  "jobId": "job-789",
  "status": "pending",
  "estimatedCompletionTime": "2025-06-26T11:00:00Z",
  "links": {
    "self": "/api/v1/jobs/job-789",
    "cancel": "/api/v1/jobs/job-789/cancel"
  }
}
```

### 13. API Security

#### Authentication
All APIs MUST require authentication:
```http
Authorization: Bearer {jwt-token}
```

#### Rate Limiting Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
X-RateLimit-Reset-After: 3600
```

#### CORS Headers
```http
Access-Control-Allow-Origin: https://app.vibematch.io
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Authorization, Content-Type
Access-Control-Max-Age: 86400
```

## Design Patterns

### 1. Sub-Resource vs Relationship

#### When to Use Sub-Resources
Use when relationship is strong and lifecycle is dependent:
```
GET /users/123/preferences      # User owns preferences
GET /agents/456/configurations  # Agent owns configs
```

#### When to Use Relationships
Use when resources can exist independently:
```
GET /tasks?agentId=456         # Tasks can exist without agent
GET /agents?ownerId=123        # Agents can change owners
```

### 2. Actions on Resources

For state transitions or complex operations:

```http
# State transitions
POST /agents/123/activate
POST /agents/123/deactivate
POST /tasks/456/cancel

# Complex operations
POST /agents/123/clone
POST /marketplace/purchases
POST /reports/generate
```

### 3. Bulk Operations

Design for efficiency with large datasets:

```http
# Bulk fetch with IDs
GET /agents?ids=123,456,789

# Bulk operations endpoint
POST /agents/bulk
{
  "operations": [
    { "action": "update", "id": "123", "data": {...} },
    { "action": "delete", "id": "456" }
  ]
}
```

## Common Pitfalls to Avoid

### 1. Chatty APIs
❌ Multiple calls for related data
✅ Design resources to minimize round trips

### 2. Inconsistent Naming
❌ Mixed conventions (camelCase, snake_case)
✅ Stick to one convention throughout

### 3. Exposing Internal Details
❌ Database IDs, internal errors
✅ Use UUIDs, sanitized error messages

### 4. Missing Versioning
❌ Breaking changes without version bump
✅ Proper versioning strategy

### 5. Synchronous Everything
❌ Long operations block responses
✅ Use async patterns for heavy operations

## OpenAPI Specification

All APIs MUST have OpenAPI 3.0 specification:

```yaml
openapi: 3.0.0
info:
  title: VibeMatch Agent API
  version: 1.0.0
  description: Agent management and orchestration API
  
servers:
  - url: https://api.vibematch.io/v1
    description: Production
  - url: https://staging-api.vibematch.io/v1
    description: Staging

security:
  - bearerAuth: []

paths:
  /agents:
    get:
      summary: List agents
      operationId: listAgents
      tags: [Agents]
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive]
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentList'
```

## Validation and Testing

### Contract Testing
- Test against OpenAPI specification
- Validate request/response schemas
- Check status codes and headers

### Integration Testing
```typescript
describe('Agent API', () => {
  it('should return 201 for successful creation', async () => {
    const response = await request(app)
      .post('/api/v1/agents')
      .send({ name: 'TestAgent', type: 'code_assistant' })
      .expect(201);
      
    expect(response.body.data).toHaveProperty('id');
    expect(response.headers).toHaveProperty('location');
  });
});
```

## Enforcement

### Design Review Checklist
- [ ] Follows resource-oriented design
- [ ] Uses standard HTTP methods correctly
- [ ] Implements proper error handling
- [ ] Includes OpenAPI specification
- [ ] Has comprehensive examples
- [ ] Considers performance implications
- [ ] Addresses security requirements

### Automated Checks
- OpenAPI linting in CI/CD
- Contract testing against spec
- Performance benchmarks
- Security scanning

---
**Owner**: API Architecture Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26