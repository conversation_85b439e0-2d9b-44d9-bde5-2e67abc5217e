# API Error Handling Standards

> **Purpose**: Standardize error responses across all VibeMatch APIs  
> **Scope**: All error scenarios in REST APIs  
> **Enforcement**: Response interceptors, error middleware validation

## Error Response Format

### 1. Standard Error Structure

All errors must follow this structure:

```json
{
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "Agent with ID '123' not found",
    "details": {
      "resource_type": "agent",
      "resource_id": "123",
      "searched_in": "active_agents"
    },
    "request_id": "req_550e8400-e29b-41d4-a716-446655440000",
    "timestamp": "2025-06-26T10:30:00Z",
    "documentation_url": "https://docs.vibematch.io/errors/RESOURCE_NOT_FOUND"
  }
}
```

### 2. Error Components

| Field | Required | Description | Example |
|-------|----------|-------------|---------|
| code | Yes | Machine-readable error code | `VALIDATION_FAILED` |
| message | Yes | Human-readable description | `Email format is invalid` |
| details | No | Additional context object | `{"field": "email"}` |
| request_id | Yes | Unique request identifier | `req_550e8400...` |
| timestamp | Yes | ISO 8601 error time | `2025-06-26T10:30:00Z` |
| documentation_url | No | Link to error docs | `https://docs...` |

### 3. Error Code Format

#### Naming Convention
- SCREAMING_SNAKE_CASE
- Domain prefix when needed
- Specific but not overly granular

#### Examples
```
VALIDATION_FAILED
RESOURCE_NOT_FOUND
AUTHENTICATION_REQUIRED
INSUFFICIENT_PERMISSIONS
RATE_LIMIT_EXCEEDED
PAYMENT_FAILED
AGENT_UNAVAILABLE
TASK_TIMEOUT
DATABASE_CONNECTION_ERROR
```

### 4. HTTP Status Code Mapping

| Status | When to Use | Error Code Examples |
|--------|-------------|-------------------|
| 400 | Client sent invalid data | `VALIDATION_FAILED`, `MALFORMED_REQUEST` |
| 401 | Missing/invalid auth | `AUTHENTICATION_REQUIRED`, `TOKEN_EXPIRED` |
| 403 | Authenticated but forbidden | `INSUFFICIENT_PERMISSIONS`, `ACCOUNT_SUSPENDED` |
| 404 | Resource doesn't exist | `RESOURCE_NOT_FOUND`, `ENDPOINT_NOT_FOUND` |
| 409 | State conflict | `DUPLICATE_RESOURCE`, `CONCURRENT_MODIFICATION` |
| 422 | Valid syntax, semantic error | `BUSINESS_RULE_VIOLATION`, `INVALID_STATE_TRANSITION` |
| 429 | Too many requests | `RATE_LIMIT_EXCEEDED`, `QUOTA_EXCEEDED` |
| 500 | Server error | `INTERNAL_SERVER_ERROR`, `DATABASE_ERROR` |
| 502 | Bad gateway | `UPSTREAM_SERVICE_ERROR`, `GATEWAY_TIMEOUT` |
| 503 | Service unavailable | `SERVICE_MAINTENANCE`, `CIRCUIT_BREAKER_OPEN` |

## Error Types and Examples

### 1. Validation Errors

```json
{
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Request validation failed",
    "details": {
      "validation_errors": [
        {
          "field": "email",
          "code": "INVALID_FORMAT",
          "message": "Email must be a valid email address"
        },
        {
          "field": "age",
          "code": "OUT_OF_RANGE",
          "message": "Age must be between 18 and 120"
        }
      ]
    },
    "request_id": "req_123",
    "timestamp": "2025-06-26T10:30:00Z"
  }
}
```

### 2. Authentication Errors

```json
{
  "error": {
    "code": "TOKEN_EXPIRED",
    "message": "Authentication token has expired",
    "details": {
      "expired_at": "2025-06-26T09:00:00Z",
      "token_type": "bearer"
    },
    "request_id": "req_124",
    "timestamp": "2025-06-26T10:30:00Z",
    "documentation_url": "https://docs.vibematch.io/auth/token-refresh"
  }
}
```

### 3. Rate Limiting Errors

```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "API rate limit exceeded",
    "details": {
      "limit": 1000,
      "remaining": 0,
      "reset_at": "2025-06-26T11:00:00Z",
      "retry_after": 1800
    },
    "request_id": "req_125",
    "timestamp": "2025-06-26T10:30:00Z"
  }
}
```

Headers:
```http
X-Rate-Limit-Limit: 1000
X-Rate-Limit-Remaining: 0
X-Rate-Limit-Reset: 1719399600
Retry-After: 1800
```

### 4. Business Logic Errors

```json
{
  "error": {
    "code": "INSUFFICIENT_CREDITS",
    "message": "Not enough credits to perform this operation",
    "details": {
      "required_credits": 100,
      "available_credits": 25,
      "operation": "create_agent_team"
    },
    "request_id": "req_126",
    "timestamp": "2025-06-26T10:30:00Z",
    "documentation_url": "https://docs.vibematch.io/billing/credits"
  }
}
```

### 5. Server Errors

```json
{
  "error": {
    "code": "INTERNAL_SERVER_ERROR",
    "message": "An unexpected error occurred",
    "details": {
      "support_reference": "ERR-2025-06-26-ABC123"
    },
    "request_id": "req_127",
    "timestamp": "2025-06-26T10:30:00Z"
  }
}
```

**Note**: Never expose internal details in production errors.

## Error Handling Implementation

### 1. Error Middleware (Express Example)

```typescript
interface ApiError extends Error {
  statusCode: number;
  code: string;
  details?: Record<string, any>;
}

class ValidationError extends Error implements ApiError {
  statusCode = 400;
  code = 'VALIDATION_FAILED';
  
  constructor(message: string, public details?: Record<string, any>) {
    super(message);
  }
}

// Global error handler
app.use((err: ApiError, req: Request, res: Response, next: NextFunction) => {
  const statusCode = err.statusCode || 500;
  const errorResponse = {
    error: {
      code: err.code || 'INTERNAL_SERVER_ERROR',
      message: err.message || 'An unexpected error occurred',
      details: err.details,
      request_id: req.id,
      timestamp: new Date().toISOString(),
      documentation_url: `https://docs.vibematch.io/errors/${err.code}`
    }
  };
  
  // Log error
  logger.error('API Error', {
    error: err,
    request_id: req.id,
    status_code: statusCode
  });
  
  res.status(statusCode).json(errorResponse);
});
```

### 2. Error Creation Helpers

```typescript
// Error factory
export class ApiErrors {
  static notFound(resource: string, id: string) {
    return new ApiError(404, 'RESOURCE_NOT_FOUND', 
      `${resource} with ID '${id}' not found`,
      { resource_type: resource, resource_id: id }
    );
  }
  
  static validation(errors: ValidationError[]) {
    return new ApiError(400, 'VALIDATION_FAILED',
      'Request validation failed',
      { validation_errors: errors }
    );
  }
  
  static unauthorized(reason?: string) {
    return new ApiError(401, 'AUTHENTICATION_REQUIRED',
      reason || 'Authentication required'
    );
  }
  
  static forbidden(resource: string, action: string) {
    return new ApiError(403, 'INSUFFICIENT_PERMISSIONS',
      `You don't have permission to ${action} this ${resource}`,
      { resource, action }
    );
  }
}

// Usage
throw ApiErrors.notFound('agent', '123');
```

### 3. Async Error Handling

```typescript
// Async handler wrapper
const asyncHandler = (fn: Function) => (req: Request, res: Response, next: NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Route with proper error handling
router.get('/agents/:id', asyncHandler(async (req, res) => {
  const agent = await agentService.findById(req.params.id);
  if (!agent) {
    throw ApiErrors.notFound('agent', req.params.id);
  }
  res.json({ data: agent });
}));
```

### 4. Client-Side Error Handling

```typescript
// SDK error handling
class VibeMatchClient {
  async request(options: RequestOptions) {
    try {
      const response = await fetch(options.url, options);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new VibeMatchError(
          errorData.error.code,
          errorData.error.message,
          errorData.error.details,
          response.status
        );
      }
      
      return response.json();
    } catch (error) {
      if (error instanceof VibeMatchError) {
        throw error;
      }
      throw new VibeMatchError(
        'NETWORK_ERROR',
        'Failed to connect to VibeMatch API',
        { original_error: error.message }
      );
    }
  }
}
```

## Error Monitoring

### 1. Error Tracking

```typescript
// Log all errors with context
logger.error('API Error', {
  error_code: err.code,
  status_code: statusCode,
  request_id: req.id,
  user_id: req.user?.id,
  endpoint: req.path,
  method: req.method,
  ip: req.ip,
  user_agent: req.headers['user-agent']
});

// Track error metrics
metrics.increment('api.errors', {
  error_code: err.code,
  status_code: statusCode,
  endpoint: req.path
});
```

### 2. Error Rate Alerts

```yaml
alerts:
  - name: high_error_rate
    condition: rate(api.errors) > 0.05  # 5% error rate
    duration: 5m
    notify:
      - <EMAIL>
      
  - name: critical_errors
    condition: count(api.errors{status_code="500"}) > 10
    duration: 1m
    notify:
      - <EMAIL>
```

## Localization

### Localized Error Messages

```json
{
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Validation de la demande échouée",
    "message_key": "errors.validation_failed",
    "details": {
      "locale": "fr-FR"
    }
  }
}
```

Client determines display language based on:
1. Accept-Language header
2. User preference
3. Default to English

## Testing Error Scenarios

### Unit Test Example

```typescript
describe('AgentController', () => {
  it('should return 404 for non-existent agent', async () => {
    const response = await request(app)
      .get('/api/v1/agents/non-existent')
      .expect(404);
      
    expect(response.body).toMatchObject({
      error: {
        code: 'RESOURCE_NOT_FOUND',
        message: expect.stringContaining('not found'),
        details: {
          resource_type: 'agent',
          resource_id: 'non-existent'
        }
      }
    });
  });
  
  it('should return 400 for invalid input', async () => {
    const response = await request(app)
      .post('/api/v1/agents')
      .send({ name: '' })  // Empty name
      .expect(400);
      
    expect(response.body.error.code).toBe('VALIDATION_FAILED');
    expect(response.body.error.details.validation_errors).toContainEqual({
      field: 'name',
      code: 'REQUIRED',
      message: 'Name is required'
    });
  });
});
```

## Enforcement

### Linting Rules

```json
{
  "rules": {
    "error-response-format": "error",
    "error-code-format": "error",
    "error-status-mapping": "error",
    "sensitive-data-exposure": "error"
  }
}
```

### Response Validation Middleware

```typescript
// Validate all error responses match standard
app.use((req, res, next) => {
  const originalJson = res.json;
  res.json = function(data) {
    if (res.statusCode >= 400 && !isValidErrorFormat(data)) {
      logger.error('Invalid error format', { data });
      data = formatError(data);
    }
    return originalJson.call(this, data);
  };
  next();
});
```

---
**Owner**: API Architecture Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26