# REST API Design Standards

> **Purpose**: Define consistent REST API design patterns for all VibeMatch services  
> **Scope**: All HTTP APIs across the platform  
> **Enforcement**: OpenAPI linting, API Gateway validation

## Core Principles

### 1. Resource-Oriented Design
APIs must be designed around resources (nouns), not actions (verbs).

#### ✅ Good Examples
```
GET    /api/v1/agents                 # List agents
GET    /api/v1/agents/{id}           # Get specific agent
POST   /api/v1/agents                 # Create agent
PUT    /api/v1/agents/{id}           # Update entire agent
PATCH  /api/v1/agents/{id}           # Partial update
DELETE /api/v1/agents/{id}           # Delete agent
```

#### ❌ Bad Examples
```
GET    /api/v1/getAgents             # Action in URL
POST   /api/v1/createNewAgent        # Redundant verb
GET    /api/v1/agents/list           # List is implicit with GET
POST   /api/v1/deleteAgent/{id}      # Wrong method for delete
```

### 2. HTTP Methods Usage

| Method | Purpose | Idempotent | Safe | Request Body | Response Body |
|--------|---------|------------|------|--------------|---------------|
| GET    | Retrieve | Yes | Yes | No | Yes |
| POST   | Create | No | No | Yes | Yes |
| PUT    | Full Update | Yes | No | Yes | Yes |
| PATCH  | Partial Update | No | No | Yes | Yes |
| DELETE | Remove | Yes | No | No | No* |

*DELETE may return the deleted resource for confirmation

### 3. URL Structure

```
https://{domain}/api/{version}/{resource}/{id}/{sub-resource}
```

#### Components
- **Domain**: Service-specific subdomain (e.g., `api.vibematch.io`)
- **Version**: Major version only (e.g., `v1`, `v2`)
- **Resource**: Plural noun (e.g., `agents`, `tasks`)
- **ID**: Resource identifier (UUID preferred)
- **Sub-resource**: Nested resources when relationship is clear

#### Examples
```
https://api.vibematch.io/api/v1/agents
https://api.vibematch.io/api/v1/agents/550e8400-e29b-41d4-a716-************
https://api.vibematch.io/api/v1/agents/550e8400-e29b-41d4-a716-************/tasks
https://api.vibematch.io/api/v1/users/123/preferences
```

### 4. Request/Response Format

#### Request Headers
```http
Content-Type: application/json
Accept: application/json
Authorization: Bearer {token}
X-Request-ID: {uuid}           # For tracing
X-Client-Version: 1.2.3        # Client version
```

#### Response Headers
```http
Content-Type: application/json
X-Request-ID: {uuid}           # Echo from request
X-Rate-Limit-Remaining: 950
X-Rate-Limit-Reset: 1640995200
Cache-Control: no-cache        # Or appropriate caching
```

### 5. Status Codes

| Code | Meaning | When to Use |
|------|---------|-------------|
| 200 | OK | Successful GET, PUT, PATCH |
| 201 | Created | Successful POST creating resource |
| 202 | Accepted | Request accepted for async processing |
| 204 | No Content | Successful DELETE, no body needed |
| 400 | Bad Request | Client error (validation, malformed) |
| 401 | Unauthorized | Missing/invalid authentication |
| 403 | Forbidden | Authenticated but not authorized |
| 404 | Not Found | Resource doesn't exist |
| 409 | Conflict | State conflict (e.g., duplicate) |
| 422 | Unprocessable Entity | Valid syntax but semantic errors |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server fault |
| 503 | Service Unavailable | Temporary unavailability |

### 6. Response Envelope

#### Success Response
```json
{
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "type": "agent",
    "attributes": {
      "name": "CodeAssistant",
      "status": "active"
    }
  },
  "meta": {
    "timestamp": "2025-06-26T10:30:00Z",
    "version": "1.0"
  }
}
```

#### Collection Response
```json
{
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "type": "agent",
      "attributes": { ... }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 157,
    "pages": 8
  },
  "links": {
    "self": "/api/v1/agents?page=1&limit=20",
    "next": "/api/v1/agents?page=2&limit=20",
    "last": "/api/v1/agents?page=8&limit=20"
  }
}
```

### 7. Query Parameters

#### Filtering
```
GET /api/v1/agents?status=active&type=code_assistant
GET /api/v1/agents?created_after=2025-01-01&created_before=2025-06-26
```

#### Sorting
```
GET /api/v1/agents?sort=created_at        # Ascending
GET /api/v1/agents?sort=-created_at       # Descending
GET /api/v1/agents?sort=status,-name      # Multiple sorts
```

#### Pagination
```
GET /api/v1/agents?page=2&limit=50        # Page-based
GET /api/v1/agents?cursor=eyJpZCI6MTAwfQ  # Cursor-based
```

#### Field Selection
```
GET /api/v1/agents?fields=id,name,status  # Sparse fieldsets
GET /api/v1/agents?include=tasks,metrics  # Include relations
```

### 8. Relationship Handling

#### Embedded Resources
```json
{
  "data": {
    "id": "123",
    "type": "agent",
    "attributes": { ... },
    "relationships": {
      "owner": {
        "data": { "type": "user", "id": "456" }
      },
      "tasks": {
        "data": [
          { "type": "task", "id": "789" },
          { "type": "task", "id": "790" }
        ]
      }
    }
  },
  "included": [
    {
      "type": "user",
      "id": "456",
      "attributes": { "name": "John Doe" }
    }
  ]
}
```

### 9. Async Operations

For long-running operations, return 202 Accepted:

```json
{
  "data": {
    "id": "job-123",
    "type": "job",
    "attributes": {
      "status": "pending",
      "progress": 0
    },
    "links": {
      "self": "/api/v1/jobs/job-123"
    }
  }
}
```

Client polls or uses webhooks for completion.

## Enforcement

### OpenAPI Specification
All APIs must have OpenAPI 3.0 specifications:

```yaml
openapi: 3.0.0
info:
  title: VibeMatch Agent API
  version: 1.0.0
paths:
  /agents:
    get:
      summary: List all agents
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive, pending]
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentList'
```

### Linting Rules
```json
{
  "rules": {
    "path-plural": "error",
    "path-no-trailing-slash": "error",
    "path-parameter-schema": "error",
    "operation-id-unique": "error",
    "operation-parameters-unique": "error",
    "response-success-content": "error"
  }
}
```

### API Gateway Validation
- Automatic request validation against OpenAPI spec
- Response format enforcement
- Rate limiting enforcement
- Authentication/authorization checks

## Exceptions Process

Deviations from these standards require:
1. Technical justification documented in API spec
2. Architecture team review and approval
3. Exception logged in `api-exceptions.log`
4. Plan for future compliance

---
**Owner**: API Architecture Team  
**Last Updated**: 2025-06-26  
**Next Review**: 2025-09-26