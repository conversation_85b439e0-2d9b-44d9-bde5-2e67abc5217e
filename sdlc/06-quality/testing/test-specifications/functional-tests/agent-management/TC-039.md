# Test Case TC-039: Agent Listing and Discovery

## Test Information
- **Test ID**: TC-039
- **Objective**: Verify that all active agents can be listed and discovered through the public agent listing endpoint
- **Requirements**: FR-031
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Agent management service is available
- Multiple active agent profiles exist in the system
- Public agent listing endpoint is configured
- Test agents have varied profiles for comprehensive testing

### Test Steps
1. Retrieve all active agents without authentication:
   ```
   GET /api/v1/agents
   ```
2. Verify agent listing response structure:
   - Check response format and pagination
   - Verify agent profile data completeness
   - Confirm only active agents are included
3. Test agent listing with authentication:
   ```
   GET /api/v1/agents
   Authorization: Bearer <token>
   ```
4. Verify agent data in listing:
   - Agent ID and basic profile information
   - Public capabilities and hourly rate
   - Rating and completion statistics
   - Profile image and availability status
5. Test listing performance:
   - Measure response time for agent listing
   - Test with large number of agents (100+)
   - Verify pagination performance
6. Test data privacy in listing:
   - Personal information not exposed
   - Contact details not included
   - Only public profile data returned
7. Test listing freshness and consistency:
   - Create new agent profile
   - Verify it appears in listing
   - Update agent profile
   - Verify changes reflect in listing

### Expected Results
- Public agent listing: 200 OK with agent array
  ```json
  {
    "agents": [
      {
        "id": "agent123",
        "name": "Expert AI Developer",
        "description": "Experienced AI/ML developer...",
        "capabilities": ["Python", "TensorFlow", "Machine Learning"],
        "hourlyRate": 5000,
        "rating": 4.8,
        "completedTasks": 25,
        "profileImage": "https://example.com/avatar.jpg",
        "availability": "available"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "hasMore": true
    }
  }
  ```
- Data completeness: All required public fields present
- Privacy protection: No private/contact information
- Performance: Response time < 500ms for typical queries
- Data freshness: New/updated agents appear within 1 minute
- Consistency: Same results across multiple requests
- Pagination: Proper page boundaries and metadata

### Post-conditions
- Agent marketplace is fully discoverable
- Public agent data is current and accurate
- System performance supports marketplace browsing
- Privacy boundaries are maintained

### Test Data
- **Test Agents**: 20+ active agents with varied profiles
- **Data Fields**: Public profile information only
- **Performance Target**: < 500ms response time
- **Pagination**: 20 agents per page (configurable)