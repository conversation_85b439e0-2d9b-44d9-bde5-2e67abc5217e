# Test Case TC-034: Agent Capabilities Management

## Test Information
- **Test ID**: TC-034
- **Objective**: Verify that agents can define and manage their capabilities list for task matching
- **Requirements**: FR-026
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Agent management service is available
- Test user exists with role "agent"
- Capabilities taxonomy is defined (if applicable)

### Test Steps
1. Create agent profile with capabilities array:
   ```json
   {
     "name": "Full Stack Developer",
     "description": "Experienced full stack developer...",
     "capabilities": ["JavaScript", "React", "Node.js", "PostgreSQL", "AWS"],
     "hourlyRate": 4500
   }
   ```
2. Test capabilities validation:
   - Empty capabilities array
   - Single capability
   - Maximum number of capabilities (if limited)
   - Duplicate capabilities in array
3. Test capability content validation:
   - Valid capability names
   - Capabilities with special characters
   - Very long capability names
   - Empty strings in capabilities array
4. Update capabilities after profile creation:
   ```json
   {
     "capabilities": ["JavaScript", "React", "Vue.js", "TypeScript", "GraphQL"]
   }
   ```
5. Test capabilities in search and matching:
   - Search agents by specific capability
   - Verify agent appears in relevant capability searches
   - Test partial capability matching
6. Test capabilities normalization:
   - Case sensitivity: "javascript" vs "JavaScript"
   - Whitespace handling: " React " vs "React"
   - Common aliases: "JS" vs "JavaScript"

### Expected Results
- Valid capabilities: Profile created successfully
  ```json
  {
    "agent": {
      "id": "agent123",
      "capabilities": ["JavaScript", "React", "Node.js", "PostgreSQL", "AWS"],
      "createdAt": "2025-01-01T10:00:00Z"
    }
  }
  ```
- Empty capabilities: 400 Bad Request
  ```json
  {
    "error": "Agent must have at least one capability",
    "field": "capabilities"
  }
  ```
- Duplicate capabilities: Automatically deduplicated
- Invalid capability content: 400 Bad Request with specific errors
- Capability updates: Successfully updated with new list
- Search functionality: Agent found by capability matches
- Normalization: Consistent formatting applied
  - Case normalization: "javascript" → "JavaScript"
  - Whitespace trimming applied
  - Aliases handled if configured

### Post-conditions
- Agent capabilities are stored and searchable
- Capability data supports effective task matching
- Capabilities list is clean and normalized
- Agent appears in relevant capability-based searches

### Test Data
- **Valid Capabilities**: ["Python", "Machine Learning", "TensorFlow", "API Development"]
- **Invalid Cases**: [], [""], ["Very long capability name exceeding reasonable limits"]
- **Update Cases**: Adding new capabilities, removing existing ones
- **Search Terms**: Individual capabilities, partial matches