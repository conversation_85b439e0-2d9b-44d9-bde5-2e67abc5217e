# Test Case TC-032: Agent Profile Name Validation

## Test Information
- **Test ID**: TC-032
- **Objective**: Verify that agent profile name field validation enforces 3-50 character requirement
- **Requirements**: FR-024
- **Priority**: High
- **Test Type**: Unit/Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Agent management service with validation is available
- Test user exists with role "agent"
- User is authenticated with valid token

### Test Steps
1. Test minimum length requirement (3 characters):
   - Submit agent profile with 2-character name: `"AI"`
   - Submit agent profile with 3-character name: `"<PERSON><PERSON>"`
   - Submit agent profile with 4-character name: `"<PERSON>"`
2. Test maximum length requirement (50 characters):
   - Submit agent profile with 49-character name
   - Submit agent profile with 50-character name
   - Submit agent profile with 51-character name
3. Test edge cases and special characters:
   - Name with only spaces: `"   "`
   - Name with leading/trailing spaces: `" Valid Name "`
   - Name with special characters: `"AI-Bot_v2.0"`
   - Name with Unicode characters: `"智能助手"`
4. Test empty and null values:
   - Submit profile without name field
   - Submit profile with empty name: `""`
   - Submit profile with null name: `null`
5. Test name trimming behavior:
   - Submit name with extra whitespace
   - Verify trimmed length is within bounds
   - Check stored value is trimmed

### Expected Results
- 2-character name: 400 Bad Request
  ```json
  {
    "error": "Agent name must be between 3 and 50 characters",
    "field": "name",
    "value": "AI"
  }
  ```
- 3-character name: Accepted
- 50-character name: Accepted
- 51-character name: 400 Bad Request
  ```json
  {
    "error": "Agent name must be between 3 and 50 characters",
    "field": "name"
  }
  ```
- Spaces-only name: 400 Bad Request
  ```json
  {
    "error": "Agent name cannot be empty or whitespace only",
    "field": "name"
  }
  ```
- Leading/trailing spaces: Trimmed and accepted if valid length
- Special characters: Accepted if within length limits
- Unicode characters: Supported and counted correctly
- Missing/empty/null name: 400 Bad Request with required field error
- Trimming: Automatic whitespace trimming applied

### Post-conditions
- Only valid agent names are stored in database
- Name validation prevents creation of invalid profiles
- Unicode support works correctly for international users
- Whitespace handling is consistent and user-friendly

### Test Data
- **Valid Names**: "Bot", "AI Assistant", "Expert Developer", "智能助手"
- **Invalid Names**: "AI", "", null, "   ", 51+ character string
- **Edge Cases**: Names at exactly 3, 49, 50, 51 characters
- **Unicode**: Chinese, Arabic, emoji characters