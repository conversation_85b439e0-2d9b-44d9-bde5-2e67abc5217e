# Test Case TC-036: Firebase Custom Claims Update for Agent Role

## Test Information
- **Test ID**: TC-036
- **Objective**: Verify that user's Firebase custom claims are updated to include agent role upon agent profile creation
- **Requirements**: FR-028
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Agent management service with Firebase integration is available
- Firebase Admin SDK is configured correctly
- Test user exists with role "User" (not yet agent)
- User is authenticated with valid Firebase token

### Test Steps
1. Verify initial user token claims:
   - Decode current Firebase ID token
   - Verify custom claims contain role "User"
   - Verify no agent-specific claims present
2. Create agent profile:
   ```json
   {
     "name": "Test Agent",
     "description": "Testing agent profile creation and claims update functionality.",
     "capabilities": ["Testing", "Quality Assurance"],
     "hourlyRate": 2500
   }
   ```
3. Verify custom claims update process:
   - Check agent profile creation response
   - Verify claims update is initiated
   - Monitor claims update status
4. Verify updated token claims:
   - Request new Firebase token (refresh or re-authenticate)
   - Decode new token and check custom claims
   - Verify agent role is included in claims
5. Test claims persistence:
   - <PERSON><PERSON> again after claims update
   - Verify agent role persists in new sessions
   - Test token validation with agent claims
6. Test role-based access:
   - Access agent-only endpoints with updated token
   - Verify access is granted with new claims
   - Test that old tokens (without agent claims) still work until expiry

### Expected Results
- Initial token: Contains role "User", no agent claims
- Agent profile creation: 201 Created with confirmation
- Claims update: Process initiated and tracked
- Updated token claims:
  ```json
  {
    "iss": "https://securetoken.google.com/project-id",
    "sub": "user123",
    "email": "<EMAIL>",
    "role": "agent",
    "permissions": ["create_agent_profile", "manage_tasks"],
    "agentId": "agent123",
    "custom_claims_updated_at": "2025-01-01T10:00:00Z"
  }
  ```
- Claims persistence: Agent role maintained across sessions
- Access control: Agent endpoints accessible with updated token
- Token transition: Old tokens work until natural expiry
- Update timing: Claims available within reasonable time (< 5 minutes)

### Post-conditions
- User has agent role in Firebase authentication
- Agent-specific permissions are active
- Role-based access control reflects agent status
- Token lifecycle properly handles role transitions

### Test Data
- **Initial Role**: "User"
- **Target Role**: "agent" 
- **Agent Permissions**: ["create_agent_profile", "manage_tasks", "access_agent_dashboard"]
- **Token Claims**: role, permissions, agentId, custom_claims_updated_at