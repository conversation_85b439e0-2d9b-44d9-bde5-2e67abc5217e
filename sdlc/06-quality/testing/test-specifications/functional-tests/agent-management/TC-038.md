# Test Case TC-038: Duplicate Agent Profile Prevention

## Test Information
- **Test ID**: TC-038
- **Objective**: Verify that users cannot create multiple agent profiles and duplicate attempts are properly rejected
- **Requirements**: FR-030
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Agent management service with duplicate prevention is available
- Test user exists with role "agent"
- User already has an existing agent profile

### Test Steps
1. Verify existing agent profile:
   ```
   GET /api/v1/agents/my-profile
   Authorization: Bearer <agent_token>
   ```
2. Attempt to create second agent profile:
   ```json
   {
     "name": "Second Agent Profile",
     "description": "Attempting to create a duplicate agent profile for the same user.",
     "capabilities": ["Duplicate Testing", "Error Handling"],
     "hourlyRate": 2000
   }
   ```
3. Verify duplicate prevention response:
   - Check 409 Conflict status code
   - Verify appropriate error message
   - Confirm no new agent profile created
4. Test duplicate detection at database level:
   - Check Firestore agent_profiles collection
   - Verify only one profile exists for user
   - Confirm existing profile is unchanged
5. Test edge cases for duplicate detection:
   - Concurrent agent profile creation attempts
   - Profile creation after previous deletion attempt
   - Profile creation with identical vs different data
6. Test system behavior after duplicate attempt:
   - Existing agent profile remains functional
   - User can still update existing profile
   - No system state corruption
7. Verify error logging and monitoring:
   - Duplicate attempt is logged for audit
   - No sensitive data leaked in error response
   - Monitoring alerts are triggered if applicable

### Expected Results
- Existing profile verification: 200 OK with current profile
- Duplicate creation attempt: 409 Conflict
  ```json
  {
    "error": "Agent profile already exists for this user",
    "code": "AGENT_PROFILE_EXISTS",
    "existingProfileId": "agent123"
  }
  ```
- Database state: Only one agent profile per user
- Existing profile: Unchanged and functional
- Concurrent attempts: All duplicates rejected consistently
- Edge cases: Proper handling without data corruption
- Error logging: Duplicate attempts recorded for audit
- System stability: No performance impact from duplicate attempts

### Post-conditions
- User has exactly one agent profile
- Existing agent profile functionality is preserved
- System maintains data integrity
- Duplicate prevention works reliably under all conditions

### Test Data
- **Existing Profile**: agent123 for user123
- **Duplicate Attempt**: Different name/description but same user
- **Concurrent Tests**: 5 simultaneous creation attempts
- **Expected Result**: 1 profile exists, 4+ attempts rejected