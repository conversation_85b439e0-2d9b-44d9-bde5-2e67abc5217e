# Test Case TC-031: Agent Profile Creation

## Test Information
- **Test ID**: TC-031
- **Objective**: Verify that users with role "agent" can create agent profiles with required information
- **Requirements**: FR-023, FR-024, FR-025, FR-026, FR-027, FR-028, FR-029, FR-030
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Agent management service is available
- Test user exists with role "agent"
- User is authenticated with valid token
- Firebase Admin SDK is configured for custom claims

### Test Steps
1. Authenticate agent user and verify role:
   ```
   GET /api/v1/user/profile
   Authorization: Bearer <agent_token>
   ```
2. Create agent profile with valid data:
   ```json
   {
     "name": "Expert AI Developer",
     "description": "Experienced AI/ML developer specializing in neural networks and deep learning applications.",
     "capabilities": ["Python", "TensorFlow", "Machine Learning", "API Development"],
     "hourlyRate": 5000,
     "availability": "full-time",
     "profileImage": "https://example.com/agent-avatar.jpg"
   }
   ```
3. Verify agent profile creation response:
   - Check 201 Created status
   - Validate returned agent profile data
   - Verify agent ID is generated
4. Verify Firebase custom claims update:
   - Check user's Firebase token includes agent role
   - Verify custom claims update within 5 seconds
   - Test new token issuance with agent permissions
5. Test duplicate agent profile prevention:
   - Attempt to create second agent profile for same user
   - Verify 409 Conflict response
6. Verify agent profile in database:
   - Check Firestore agent_profiles collection
   - Validate all required fields are stored
   - Verify timestamps are set correctly

### Expected Results
- Successful creation: 201 Created with agent profile
  ```json
  {
    "agent": {
      "id": "agent123",
      "userId": "user123",
      "name": "Expert AI Developer",
      "description": "Experienced AI/ML developer...",
      "capabilities": ["Python", "TensorFlow", "Machine Learning", "API Development"],
      "hourlyRate": 5000,
      "availability": "full-time",
      "profileImage": "https://example.com/agent-avatar.jpg",
      "rating": 0,
      "completedTasks": 0,
      "totalEarnings": 0,
      "createdAt": "2025-01-01T10:00:00Z",
      "updatedAt": "2025-01-01T10:00:00Z"
    }
  }
  ```
- Firebase claims update: New token includes agent role within 5 seconds
- Duplicate prevention: 409 Conflict
  ```json
  {
    "error": "Agent profile already exists for this user",
    "code": "AGENT_PROFILE_EXISTS"
  }
  ```
- Database verification: Agent document created with all fields
- Performance: Profile creation completes in < 1 second

### Post-conditions
- User can access agent-specific endpoints
- Agent appears in public agent listings
- Agent profile is ready for task matching
- Firebase authentication reflects agent role

### Test Data
- **Agent User**: <EMAIL> (role: "agent")
- **Name**: 3-50 characters
- **Description**: 50-500 characters  
- **Hourly Rate**: Minimum 1000 (10 credits)
- **Capabilities**: Array of skill strings