# Test Case TC-041: Agent Filtering by Hourly Rate Range

## Test Information
- **Test ID**: TC-041
- **Objective**: Verify that agents can be filtered by hourly rate ranges for budget-based discovery
- **Requirements**: FR-033
- **Priority**: Medium
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Agent management service with rate filtering is available
- Multiple agents exist with varied hourly rates
- Rate filtering infrastructure is operational

### Test Steps
1. Test minimum rate filtering:
   ```
   GET /api/v1/agents?minRate=3000
   ```
2. Test maximum rate filtering:
   ```
   GET /api/v1/agents?maxRate=5000
   ```
3. Test rate range filtering:
   ```
   GET /api/v1/agents?minRate=2000&maxRate=6000
   ```
4. Test edge cases:
   - Agents at exact boundary rates
   - Very low/high rate limits
   - Invalid rate parameters (negative, non-numeric)
5. Test rate filtering with other filters:
   ```
   GET /api/v1/agents?capabilities=Python&minRate=3000&maxRate=7000
   ```
6. Test rate conversion and display:
   - Filter by rate in credits vs currency units
   - Verify rate display format in results
   - Test rate sorting within filtered results

### Expected Results
- Rate filtering: Only agents within specified range returned
  ```json
  {
    "agents": [
      {
        "id": "agent123",
        "name": "Mid-Range Developer",
        "hourlyRate": 4500,
        "rateDisplay": {
          "credits": 45,
          "currency": "$0.45"
        }
      }
    ],
    "filters": {
      "minRate": 3000,
      "maxRate": 5000
    }
  }
  ```
- Boundary inclusion: Agents at exact min/max rates included
- Invalid parameters: 400 Bad Request with validation error
- Combined filters: All criteria applied correctly
- Rate display: Consistent formatting across results
- Performance: Efficient range queries using database indexes

### Post-conditions
- Users can find agents within budget constraints
- Rate filtering supports marketplace price discovery
- Filter performance remains optimal with rate ranges

### Test Data
- **Rate Range**: 1000-50000 (10-500 credits)
- **Test Boundaries**: 2000, 3000, 5000, 7000, 10000
- **Combined Filters**: capabilities + rate range