# Test Case TC-035: Agent Hourly Rate Validation

## Test Information
- **Test ID**: TC-035
- **Objective**: Verify that agent hourly rate validation enforces minimum rate of 1000 (10 credits)
- **Requirements**: FR-027
- **Priority**: High
- **Test Type**: Unit/Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Agent management service with rate validation is available
- Test user exists with role "agent"
- Credit system is configured (1 credit = $0.01, 100 smallest units)

### Test Steps
1. Test minimum rate enforcement:
   - Submit hourly rate of 999 (below minimum)
   - Submit hourly rate of 1000 (exactly minimum)
   - Submit hourly rate of 1001 (above minimum)
2. Test rate data types and formats:
   - Submit rate as integer: `5000`
   - Submit rate as float: `5000.50` (should be rejected)
   - Submit rate as string: `"5000"`
   - Submit negative rate: `-1000`
3. Test extreme values:
   - Very high rate: `1000000` (10,000 credits)
   - Maximum allowed rate (if there's a cap)
   - Zero rate: `0`
4. Test rate updates:
   - Create agent with valid rate
   - Update to higher rate
   - Update to lower rate (above minimum)
   - Attempt to update below minimum
5. Test rate display and conversion:
   - Verify rate stored in smallest units (cents)
   - Test rate display in credits (rate/100)
   - Test rate display in dollars (rate/10000)

### Expected Results
- Rate below minimum (999): 400 Bad Request
  ```json
  {
    "error": "Hourly rate must be at least 1000 (10 credits)",
    "field": "hourlyRate",
    "minimum": 1000,
    "provided": 999
  }
  ```
- Minimum rate (1000): Accepted
- Above minimum (1001+): Accepted
- Float values: 400 Bad Request
  ```json
  {
    "error": "Hourly rate must be an integer (in smallest currency units)",
    "field": "hourlyRate"
  }
  ```
- Negative values: 400 Bad Request
- String values: Parsed to integer if valid, rejected if invalid
- Zero rate: 400 Bad Request (below minimum)
- Rate updates: Same validation rules apply
- Rate storage: Stored as integer in smallest units
- Rate display conversions:
  - 5000 units = 50 credits = $0.50

### Post-conditions
- All agent hourly rates meet minimum requirements
- Rate data integrity maintained in database
- Rate calculations work correctly for billing
- Agent marketplace pricing is consistent

### Test Data
- **Valid Rates**: 1000, 1500, 5000, 10000, 50000
- **Invalid Rates**: 999, 0, -1000, 5000.50, "invalid"
- **Minimum**: 1000 (10 credits, $0.10)
- **Conversion**: rate/100 = credits, rate/10000 = dollars