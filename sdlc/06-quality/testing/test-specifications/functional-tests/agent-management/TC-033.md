# Test Case TC-033: Agent Profile Description Validation

## Test Information
- **Test ID**: TC-033
- **Objective**: Verify that agent profile description field validation enforces 50-500 character requirement
- **Requirements**: FR-025
- **Priority**: High
- **Test Type**: Unit/Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Agent management service with validation is available
- Test user exists with role "agent"
- User is authenticated with valid token

### Test Steps
1. Test minimum length requirement (50 characters):
   - Submit description with 49 characters
   - Submit description with exactly 50 characters
   - Submit description with 51 characters
2. Test maximum length requirement (500 characters):
   - Submit description with 499 characters
   - Submit description with exactly 500 characters
   - Submit description with 501 characters
3. Test content validation and formatting:
   - Description with HTML tags
   - Description with markdown formatting
   - Description with special characters and punctuation
   - Description with line breaks and paragraphs
4. Test edge cases:
   - Description with only spaces (50+ characters)
   - Description with repeated characters
   - Description with Unicode content
   - Description with URLs and contact information
5. Test description sanitization:
   - XSS attempts in description
   - Script tags and JavaScript
   - Potentially malicious content

### Expected Results
- 49-character description: 400 Bad Request
  ```json
  {
    "error": "Agent description must be between 50 and 500 characters",
    "field": "description",
    "actualLength": 49
  }
  ```
- 50-character description: Accepted
- 500-character description: Accepted
- 501-character description: 400 Bad Request
  ```json
  {
    "error": "Agent description must be between 50 and 500 characters",
    "field": "description",
    "actualLength": 501
  }
  ```
- HTML/markdown: Properly escaped or stripped
- Special characters: Accepted and preserved
- Line breaks: Normalized and preserved
- Spaces-only: 400 Bad Request (not meaningful content)
- Unicode content: Supported and counted correctly
- URLs/contact info: Accepted (may be flagged for review)
- XSS content: Sanitized and made safe
- Character counting: Accurate for Unicode and special chars

### Post-conditions
- Only meaningful agent descriptions are stored
- Content is safe for display without XSS risks
- Description validation supports international content
- Stored descriptions maintain formatting appropriately

### Test Data
- **Valid Descriptions**: 
  - 50 chars: "Experienced developer with 5+ years in web dev."
  - 500 chars: Comprehensive description with skills and experience
- **Invalid Descriptions**:
  - 49 chars: "Experienced developer with 5+ years in web."
  - 501 chars: Extended description exceeding limit
- **Special Content**: HTML tags, URLs, Unicode, line breaks