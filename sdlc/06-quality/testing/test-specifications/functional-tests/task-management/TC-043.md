# Test Case TC-043: Task Creation with All Required Fields

## Test Information
- **Test ID**: TC-043
- **Objective**: Verify that authenticated users can successfully create tasks with all required fields and proper validation
- **Requirements**: FR-038, FR-039, FR-040, FR-041, FR-042, FR-043, FR-044, FR-045
- **Priority**: Critical
- **Test Type**: Integration
- **Automation**: Yes
- **Dependencies**: User authentication (TC-006), Credit balance validation

## Business Context
- **User Journey**: User creates a new task to post on the marketplace
- **Business Impact**: Core revenue-generating functionality - task creation drives all marketplace activity
- **Revenue Impact**: Direct impact on transaction volume and platform commission revenue

## Test Details

### Preconditions
- User is authenticated with valid Firebase ID token
- User has sufficient credit balance (minimum 100 credits for minimum task budget)
- System is operational and database is accessible
- Current timestamp available for deadline validation

### Test Steps
1. **Authenticate User**
   - Send request with valid Firebase ID token
   - Verify token validation passes
   
2. **Submit Task Creation Request**
   ```json
   POST /api/tasks
   {
     "title": "Create responsive website mockup",
     "description": "Need a professional designer to create responsive website mockups for a fintech startup. Must include mobile and desktop versions with modern UI/UX principles.",
     "requirements": [
       "Experience with Figma or Adobe XD",
       "Portfolio of previous web designs",
       "Understanding of fintech industry"
     ],
     "budget": 15000,
     "deadline": "2024-12-31T23:59:59Z",
     "requiresMultipleAgents": false,
     "attachments": []
   }
   ```
   
3. **Verify Field Validation**
   - Title: 5-100 characters (31 chars - valid)
   - Description: 20-2000 characters (156 chars - valid)
   - Requirements: Array with valid entries
   - Budget: >= 10000 credits (15000 - valid)
   - Deadline: Future timestamp (valid)
   - Multi-agent flag: Boolean (valid)

4. **Verify Task Creation**
   - Task document created in Firestore
   - Task assigned unique ID
   - Initial status set to "draft"
   - Creator ID matches authenticated user
   - Timestamps (createdAt, updatedAt) populated

### Expected Results
- **HTTP Status**: 201 Created
- **Response Structure**:
  ```json
  {
    "success": true,
    "task": {
      "id": "task-uuid",
      "title": "Create responsive website mockup",
      "description": "Need a professional designer...",
      "requirements": [...],
      "budget": 15000,
      "deadline": "2024-12-31T23:59:59Z",
      "requiresMultipleAgents": false,
      "status": "draft",
      "createdBy": "user-id",
      "createdAt": "2024-06-22T10:00:00Z",
      "updatedAt": "2024-06-22T10:00:00Z"
    }
  }
  ```
- **Database State**: 
  - Task document exists in `tasks` collection
  - User's task count incremented
  - No changes to credit balance (escrow happens on posting)

### Error Scenarios

#### Invalid Title Length
- **Input**: Title with 3 characters
- **Expected**: 400 Bad Request, error message "Title must be between 5-100 characters"

#### Invalid Description Length  
- **Input**: Description with 15 characters
- **Expected**: 400 Bad Request, error message "Description must be between 20-2000 characters"

#### Invalid Budget
- **Input**: Budget of 5000 credits
- **Expected**: 400 Bad Request, error message "Budget must be at least 10000 credits (100 credits)"

#### Past Deadline
- **Input**: Deadline in the past
- **Expected**: 400 Bad Request, error message "Deadline must be in the future"

#### Insufficient Credits
- **Input**: User with 50 credits, task budget 15000
- **Expected**: 400 Bad Request, error message "Insufficient credit balance for task budget"

#### Missing Required Fields
- **Input**: Request missing title field
- **Expected**: 400 Bad Request, error message "Title is required"

### Performance Criteria
- **Response Time**: < 500ms for task creation
- **Database Write**: Single atomic operation
- **Concurrent Users**: Support 100 simultaneous task creations
- **Error Rate**: < 1% under normal load

### Security Validations
- **Authorization**: Only authenticated users can create tasks
- **Input Sanitization**: HTML/script tags stripped from text fields
- **SQL Injection**: Parameterized queries prevent injection
- **XSS Protection**: Output encoding applied to user content
- **Audit Logging**: Task creation logged with user ID and timestamp

### Post-conditions
- Task exists in draft status
- User remains authenticated
- Credit balance unchanged (escrow on task posting)
- System ready for task lifecycle operations
- No side effects on other users or tasks