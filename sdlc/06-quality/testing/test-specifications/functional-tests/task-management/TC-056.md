# Test Case TC-056: Task Search and Filtering

## Test Information
- **Test ID**: TC-056
- **Objective**: Verify comprehensive task search and filtering capabilities for different user types and marketplace browsing scenarios
- **Requirements**: FR-048, FR-049, FR-051, NFR-002 (performance), ADR-015 (search optimization)
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes
- **Dependencies**: Search indexing, Task visibility rules, Performance optimization

## Business Context
- **User Journey**: Users search and filter tasks to find relevant work opportunities or track their own projects
- **Business Impact**: Efficient search drives marketplace engagement and improves task-agent matching success rates
- **Revenue Impact**: Better search results increase task completion rates and platform transaction volume

## Test Details

### Preconditions
- Multiple tasks exist in various states and categories
- Search indexing is operational and current
- Different user types are authenticated (task owners, agents, admins)
- Performance monitoring is active
- Elasticsearch/search service is operational

### Test Steps

#### Scenario 1: Basic Text Search for Agents

1. **Simple Keyword Search**
   ```json
   GET /api/tasks/search?q=website%20design&userType=agent
   ```

2. **Verify Search Results**
   ```json
   {
     "query": "website design",
     "totalResults": 23,
     "resultsReturned": 10,
     "searchTime": "0.045s",
     "results": [
       {
         "id": "task-001",
         "title": "Modern Website Design for Tech Startup",
         "description": "Create responsive website design...",
         "budget": 25000,
         "deadline": "2024-12-31T23:59:59Z",
         "status": "open",
         "requiresMultipleAgents": false,
         "relevanceScore": 0.95,
         "matchingSkills": ["website design", "responsive design"]
       }
     ],
     "pagination": {
       "currentPage": 1,
       "totalPages": 3,
       "pageSize": 10,
       "hasNext": true
     }
   }
   ```

3. **Verify Search Relevance**
   - Results ranked by relevance score
   - Tasks with exact keyword matches prioritized
   - Only "open" status tasks visible to agents
   - Budget and deadline information included

#### Scenario 2: Advanced Filtering by Multiple Criteria

1. **Multi-Filter Search Request**
   ```json
   GET /api/tasks/search?filters={
     "skills": ["React.js", "TypeScript"],
     "budgetRange": {"min": 20000, "max": 100000},
     "deadline": {"after": "2024-07-01", "before": "2024-12-31"},
     "taskType": "development",
     "requiresMultipleAgents": false,
     "estimatedDuration": {"max": 30}
   }&sort=budget_desc&limit=20
   ```

2. **Verify Advanced Filtering**
   ```json
   {
     "appliedFilters": {
       "skills": ["React.js", "TypeScript"],
       "budgetRange": {"min": 20000, "max": 100000},
       "deadline": {"after": "2024-07-01T00:00:00Z", "before": "2024-12-31T23:59:59Z"},
       "taskType": "development",
       "requiresMultipleAgents": false
     },
     "totalResults": 8,
     "results": [
       {
         "id": "task-high-budget",
         "budget": 85000,
         "skills": ["React.js", "TypeScript", "Node.js"],
         "deadline": "2024-11-15T17:00:00Z",
         "estimatedDuration": 25
       }
     ],
     "filterStats": {
       "skillMatches": {"React.js": 8, "TypeScript": 8, "Node.js": 3},
       "budgetDistribution": {"20k-40k": 2, "40k-60k": 3, "60k-100k": 3}
     }
   }
   ```

#### Scenario 3: Task Owner's Task Management Search

1. **Owner's Task Search with Status Filtering**
   ```json
   GET /api/tasks/my-tasks?filters={
     "status": ["open", "matched", "in_progress"],
     "createdAfter": "2024-01-01",
     "budgetRange": {"min": 10000}
   }&sort=deadline_asc
   ```

2. **Verify Owner-Specific Results**
   ```json
   {
     "userTasks": {
       "totalTasks": 12,
       "activeTasksCount": 7,
       "completedTasksCount": 3,
       "cancelledTasksCount": 2
     },
     "results": [
       {
         "id": "owner-task-001",
         "title": "Mobile App Development",
         "status": "in_progress",
         "assignedAgent": {
           "id": "agent-uuid",
           "name": "John Developer",
           "progress": 65
         },
         "budget": 50000,
         "deadline": "2024-08-15T17:00:00Z",
         "daysUntilDeadline": 54
       }
     ],
     "statusSummary": {
       "open": 2,
       "matched": 1, 
       "in_progress": 4,
       "under_review": 0
     }
   }
   ```

#### Scenario 4: Geographic and Timezone-Based Search

1. **Location-Based Task Search**
   ```json
   GET /api/tasks/search?filters={
     "location": {
       "timezone": "America/New_York",
       "workingHours": "business_overlap",
       "region": "North America"
     },
     "communicationPreference": "real_time"
   }
   ```

2. **Verify Geographic Filtering**
   - Tasks requiring timezone compatibility highlighted
   - Communication preference matching
   - Regional preference consideration
   - Time zone overlap calculation

#### Scenario 5: Skill-Based Intelligent Search

1. **AI-Enhanced Skill Matching**
   ```json
   POST /api/tasks/intelligent-search
   {
     "agentProfile": {
       "skills": ["Python", "Machine Learning", "Data Analysis"],
       "experience": "intermediate",
       "availability": "part_time",
       "preferredBudget": {"min": 15000}
     },
     "preferences": {
       "taskComplexity": "moderate",
       "timeCommitment": "flexible",
       "industryPreference": ["fintech", "healthcare"]
     }
   }
   ```

2. **Verify Intelligent Matching**
   ```json
   {
     "matchingStrategy": "skill_compatibility_ai",
     "personalizedResults": [
       {
         "task": {
           "id": "ml-task-001",
           "title": "Customer Behavior Analysis Dashboard"
         },
         "matchScore": 0.92,
         "matchReasons": [
           "Strong skill alignment (Python, ML, Data Analysis)",
           "Budget matches preference (18000 credits)",
           "Part-time friendly timeline",
           "Fintech industry alignment"
         ],
         "estimatedEffort": "15-20 hours",
         "learningOpportunities": ["Advanced ML algorithms", "Dashboard development"]
       }
     ],
     "recommendationQuality": "high",
     "totalPersonalizedMatches": 5
   }
   ```

### Expected Results

#### Search Performance Metrics
- **Response Time**: < 200ms for basic text search
- **Advanced Filtering**: < 500ms for multiple filter combinations
- **Large Result Sets**: < 1 second for searches returning 100+ results
- **Real-time Updates**: < 5 seconds for new task availability

#### Search Quality Metrics
- **Relevance Accuracy**: > 85% of results match user intent
- **Filter Precision**: 100% accuracy for applied filters
- **Ranking Quality**: Most relevant results in top 10 positions
- **Personalization Effectiveness**: > 70% user engagement with recommended tasks

### Error Scenarios

#### Invalid Search Parameters
- **Input**: Malformed filter JSON
- **Expected**: 400 Bad Request, "Invalid filter format"

#### Excessive Search Results
- **Input**: Overly broad search returning 10000+ results
- **Expected**: Pagination with performance warning

#### Search Service Unavailable
- **Scenario**: Elasticsearch/search backend down
- **Expected**: Graceful degradation to basic database search

#### Unauthorized Access
- **Input**: Agent attempting to search private task data
- **Expected**: 403 Forbidden, filtered results only

### Performance Criteria
- **Search Index Update**: < 30 seconds for new task availability
- **Concurrent Searches**: Support 200 simultaneous search requests
- **Cache Effectiveness**: 80% cache hit rate for common searches
- **Mobile Performance**: < 300ms response time for mobile searches

### Security Validations
- **Data Privacy**: Users only see tasks they're authorized to view
- **Search Injection**: SQL injection and NoSQL injection prevention
- **Rate Limiting**: Prevent search spam and abuse
- **Sensitive Data**: No exposure of private task details in search results

### Business Rules Enforcement

#### Visibility Rules
- **Draft Tasks**: Only visible to task owner
- **Open Tasks**: Visible to all qualified agents
- **Matched Tasks**: Visible to owner and assigned agent(s)
- **Completed Tasks**: Limited visibility based on privacy settings

#### Search Ranking Factors
1. **Skill Match Accuracy**: 40% weight
2. **Budget Compatibility**: 25% weight  
3. **Timeline Feasibility**: 20% weight
4. **Agent Success History**: 10% weight
5. **Task Popularity**: 5% weight

#### Filtering Capabilities
- **Skill Requirements**: Exact match and related skill matching
- **Budget Range**: Flexible range with currency conversion
- **Timeline**: Deadline and estimated duration filtering
- **Task Complexity**: Based on requirement analysis
- **Industry/Domain**: Categorized task filtering

### Advanced Search Features

#### Auto-complete and Suggestions
- **Skill Suggestions**: Auto-complete for skill names
- **Popular Searches**: Trending search terms
- **Search History**: User's previous search patterns
- **Typo Correction**: Automatic correction of common misspellings

#### Search Analytics
- **Popular Searches**: Most common search terms
- **Conversion Rates**: Search to application rates
- **Filter Usage**: Most used filter combinations
- **Performance Monitoring**: Search speed and relevance tracking

### Post-conditions
- Users can efficiently find relevant tasks based on multiple criteria
- Search results accurately reflect current task availability
- Performance remains optimal under high search volume
- Search analytics provide insights for marketplace optimization
- All search activities properly logged for improvement analysis
- Security and privacy requirements maintained for all search operations