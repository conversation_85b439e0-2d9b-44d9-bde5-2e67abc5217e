# Test Case TC-044: Task Budget Validation and Credit Verification

## Test Information
- **Test ID**: TC-044
- **Objective**: Verify comprehensive budget validation including credit balance verification and escrow preparation
- **Requirements**: FR-042, FR-038, NFR-009 (credit system), NFR-012 (transaction security)
- **Priority**: Critical
- **Test Type**: Integration
- **Automation**: Yes
- **Dependencies**: User authentication, Credit system functionality

## Business Context
- **User Journey**: User attempts to create tasks with various budget scenarios
- **Business Impact**: Prevents financial losses from insufficient funds and ensures marketplace liquidity
- **Revenue Impact**: Critical for maintaining transaction flow and preventing payment failures

## Test Details

### Preconditions
- User is authenticated with valid Firebase ID token
- Credit system is operational
- Test users with different credit balances available
- Database is accessible and transaction logs enabled

### Test Steps

#### Scenario 1: Valid Budget with Sufficient Credits

1. **Setup Test User**
   - User credit balance: 50000 credits (500 USD equivalent)
   - User has previously successful transactions

2. **Submit Task with Valid Budget**
   ```json
   POST /api/tasks
   {
     "title": "Data analysis project",
     "description": "Analyze customer behavior data and provide insights",
     "requirements": ["Python/R experience", "Statistical analysis"],
     "budget": 25000,
     "deadline": "2024-12-31T23:59:59Z",
     "requiresMultipleAgents": false
   }
   ```

3. **Verify Budget Processing**
   - Budget validation passes (>= 10000 minimum)
   - Credit balance check passes (50000 >= 25000)
   - Task creation succeeds
   - Credits remain available (not escrowed until posting)

#### Scenario 2: Minimum Valid Budget

1. **Submit Task with Minimum Budget**
   ```json
   {
     "budget": 10000,
     "title": "Simple logo design",
     "description": "Create a basic logo for small business startup",
     "requirements": ["Basic design skills"],
     "deadline": "2024-12-31T23:59:59Z"
   }
   ```

2. **Verify Minimum Threshold**
   - Budget equals minimum (10000 credits = 100 USD)
   - Validation passes
   - Task created successfully

#### Scenario 3: Below Minimum Budget

1. **Submit Task Below Minimum**
   ```json
   {
     "budget": 5000,
     "title": "Quick task",
     "description": "Very simple task requiring minimal effort",
     "requirements": ["Basic skills"],
     "deadline": "2024-12-31T23:59:59Z"
   }
   ```

2. **Verify Rejection**
   - Budget validation fails
   - Error returned before credit check
   - No database changes

#### Scenario 4: Insufficient Credit Balance

1. **Setup User with Low Balance**
   - User credit balance: 8000 credits

2. **Submit Task Exceeding Balance**
   ```json
   {
     "budget": 15000,
     "title": "Complex development task",
     "description": "Full-stack web application development",
     "requirements": ["Full-stack development"],
     "deadline": "2024-12-31T23:59:59Z"
   }
   ```

3. **Verify Insufficient Funds Handling**
   - Budget validation passes (>= 10000)
   - Credit balance check fails (8000 < 15000)
   - Transaction rejected with clear error message

### Expected Results

#### Valid Budget Scenarios
- **HTTP Status**: 201 Created
- **Response**: Task object with confirmed budget
- **Database**: Task record created with budget amount
- **Credits**: Balance unchanged (escrow on posting)
- **Audit**: Budget validation logged

#### Invalid Budget Scenarios
- **Below Minimum**:
  - HTTP Status: 400 Bad Request
  - Error: "Budget must be at least 10000 credits (100 USD)"
  - No database changes

- **Insufficient Credits**:
  - HTTP Status: 400 Bad Request  
  - Error: "Insufficient credit balance. Required: 15000, Available: 8000"
  - User balance displayed for transparency

### Error Scenarios

#### Zero or Negative Budget
- **Input**: `"budget": 0` or `"budget": -1000`
- **Expected**: 400 Bad Request, "Budget must be positive and at least 10000 credits"

#### Non-Numeric Budget
- **Input**: `"budget": "invalid"`
- **Expected**: 400 Bad Request, "Budget must be a valid number"

#### Extremely Large Budget
- **Input**: `"budget": 999999999999`
- **Expected**: 400 Bad Request, "Budget exceeds maximum allowed limit"

#### Concurrent Budget Checks
- **Scenario**: Multiple simultaneous task creations by same user
- **Expected**: Accurate credit balance validation for each request

### Performance Criteria
- **Credit Balance Check**: < 100ms response time
- **Budget Validation**: < 50ms processing time
- **Concurrent Validations**: Support 50 simultaneous checks per user
- **Database Queries**: Optimized single query for balance verification

### Security Validations
- **Authorization**: Credit balance only visible to account owner
- **Data Integrity**: Budget amounts cannot be tampered with
- **Audit Trail**: All budget validations logged with timestamps
- **Rate Limiting**: Prevent budget validation spam attacks
- **Encryption**: Credit data encrypted in transit and at rest

### Business Rules Validation
- **Minimum Budget**: Enforced at 10000 credits (100 USD)
- **Credit Conversion**: 100 credits = 1 USD consistently applied
- **Balance Check**: Real-time credit balance verification
- **Escrow Logic**: Credits reserved but not deducted until task posting
- **Commission Calculation**: 15% platform fee calculated on budget

### Post-conditions
- Valid tasks created with confirmed budgets
- Invalid requests rejected with clear error messages
- Credit balances remain unchanged during task creation
- Audit trail maintained for all budget validations
- System ready for task posting and escrow operations