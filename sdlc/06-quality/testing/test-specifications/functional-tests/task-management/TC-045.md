# Test Case TC-045: Deadline and Timeline Validation

## Test Information
- **Test ID**: TC-045
- **Objective**: Verify comprehensive deadline validation including timezone handling, business rules, and timeline feasibility
- **Requirements**: FR-043, FR-038, NFR-002 (performance), NFR-015 (timezone support)
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes
- **Dependencies**: System time synchronization, Timezone services

## Business Context
- **User Journey**: User sets realistic deadlines for task completion to ensure successful marketplace matching
- **Business Impact**: Prevents unrealistic deadlines that lead to failed matches and poor user experience
- **Revenue Impact**: Proper timeline management improves task completion rates and agent satisfaction

## Test Details

### Preconditions
- User is authenticated with valid Firebase ID token
- System time is synchronized (UTC)
- Timezone conversion services are operational
- Database contains current timestamp references

### Test Steps

#### Scenario 1: Valid Future Deadline - Standard Case

1. **Submit Task with Valid Future Deadline**
   ```json
   POST /api/tasks
   {
     "title": "Website development project",
     "description": "Create a modern responsive website with authentication",
     "requirements": ["React.js", "Node.js", "Authentication"],
     "budget": 50000,
     "deadline": "2024-12-31T23:59:59Z",
     "requiresMultipleAgents": false
   }
   ```

2. **Verify Deadline Processing**
   - Deadline is parsed as valid ISO 8601 format
   - Deadline is confirmed to be in the future
   - Timezone conversion handled correctly
   - Task creation succeeds

#### Scenario 2: Minimum Valid Deadline - 24 Hour Buffer

1. **Submit Task with Near-Future Deadline**
   ```json
   {
     "deadline": "2024-06-23T12:00:00Z",
     "title": "Quick design review",
     "description": "Review existing design mockups and provide feedback",
     "requirements": ["Design review experience"],
     "budget": 12000
   }
   ```

2. **Verify Minimum Timeline**
   - Deadline is at least 24 hours from creation
   - Business rule validation passes
   - Task allows sufficient agent response time

#### Scenario 3: Past Deadline Rejection

1. **Submit Task with Past Deadline**
   ```json
   {
     "deadline": "2024-06-20T10:00:00Z",
     "title": "Historical task",
     "description": "This deadline has already passed",
     "requirements": ["Any skill"],
     "budget": 15000
   }
   ```

2. **Verify Past Deadline Handling**
   - Deadline validation fails
   - Clear error message provided
   - No task creation occurs

#### Scenario 4: Too-Near-Future Deadline

1. **Submit Task with Insufficient Lead Time**
   ```json
   {
     "deadline": "2024-06-22T11:30:00Z",
     "title": "Urgent task",
     "description": "Task needed in 30 minutes",
     "requirements": ["Immediate availability"],
     "budget": 20000
   }
   ```

2. **Verify Business Rule Enforcement**
   - Deadline is in future but too soon
   - Minimum 24-hour requirement validation
   - Rejection with guidance on minimum timeline

### Expected Results

#### Valid Deadline Cases
- **HTTP Status**: 201 Created
- **Response Structure**:
  ```json
  {
    "success": true,
    "task": {
      "id": "task-uuid",
      "deadline": "2024-12-31T23:59:59Z",
      "timeRemaining": "6 months, 9 days",
      "createdAt": "2024-06-22T10:00:00Z"
    }
  }
  ```
- **Database**: Task stored with normalized UTC deadline
- **Timeline**: Sufficient time for agent matching and completion

#### Invalid Deadline Cases

- **Past Deadline**:
  - HTTP Status: 400 Bad Request
  - Error: "Deadline must be in the future. Current time: 2024-06-22T10:00:00Z"

- **Too Soon**:
  - HTTP Status: 400 Bad Request
  - Error: "Deadline must be at least 24 hours from now to allow adequate agent response time"

### Error Scenarios

#### Invalid Date Format
- **Input**: `"deadline": "invalid-date"`
- **Expected**: 400 Bad Request, "Invalid deadline format. Use ISO 8601 format (YYYY-MM-DDTHH:mm:ssZ)"

#### Missing Timezone Information
- **Input**: `"deadline": "2024-12-31 23:59:59"`
- **Expected**: 400 Bad Request, "Deadline must include timezone information (Z for UTC or +/-HH:mm offset)"

#### Far Future Deadline
- **Input**: `"deadline": "2030-01-01T00:00:00Z"`
- **Expected**: 400 Bad Request, "Deadline cannot be more than 2 years in the future"

#### Leap Year and Date Edge Cases
- **Input**: `"deadline": "2024-02-29T12:00:00Z"` (valid leap year)
- **Expected**: Accepted and processed correctly
- **Input**: `"deadline": "2025-02-29T12:00:00Z"` (invalid - not leap year)
- **Expected**: 400 Bad Request, "Invalid date"

### Performance Criteria
- **Deadline Validation**: < 50ms processing time
- **Timezone Conversion**: < 100ms for all supported timezones
- **Date Parsing**: Handle ISO 8601 format variations efficiently
- **Concurrent Validations**: Support 200 simultaneous deadline checks

### Security Validations
- **Input Sanitization**: Prevent date injection attacks
- **Timezone Validation**: Only accept valid timezone identifiers
- **System Clock**: Protect against time-based manipulation attacks
- **Audit Logging**: Log all deadline validation attempts

### Business Rules Validation

#### Timeline Feasibility
- **Minimum Lead Time**: 24 hours required
- **Maximum Lead Time**: 2 years maximum
- **Business Hours**: Consider agent availability patterns
- **Holiday Handling**: Account for major holidays in deadline planning

#### Agent Matching Considerations
- **Response Time**: Allow agents time to review and bid
- **Quality Assurance**: Ensure adequate time for quality work
- **Revision Cycles**: Account for feedback and revision time
- **Buffer Time**: Include buffer for unexpected delays

### Timezone Support Testing

#### Multiple Timezone Inputs
- **UTC**: `"2024-12-31T23:59:59Z"`
- **EST**: `"2024-12-31T18:59:59-05:00"`
- **PST**: `"2024-12-31T15:59:59-08:00"`
- **GMT+1**: `"2025-01-01T00:59:59+01:00"`

#### Expected Behavior
- All timezone formats normalized to UTC in database
- Consistent deadline comparison regardless of input timezone
- User's local timezone preserved for display purposes

### Post-conditions
- Valid tasks created with properly validated deadlines
- Invalid deadlines rejected with clear error messages
- All deadlines stored in UTC format in database
- Timeline feasibility ensures realistic task completion expectations
- System maintains accurate time references for matching algorithms