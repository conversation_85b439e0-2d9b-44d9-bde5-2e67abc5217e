# Test Case TC-048: Task Status Transitions and State Machine Validation

## Test Information
- **Test ID**: TC-048
- **Objective**: Verify proper enforcement of task state transitions following the defined state machine: draft → open → matched → in_progress → completed
- **Requirements**: FR-046, FR-045, NFR-003 (reliability), ADR-008 (state management)
- **Priority**: Critical
- **Test Type**: Integration
- **Automation**: Yes
- **Dependencies**: Task creation, Agent matching, Credit system

## Business Context
- **User Journey**: Task progresses through defined lifecycle stages ensuring proper workflow and preventing invalid state changes
- **Business Impact**: Maintains data integrity and prevents system inconsistencies that could lead to payment disputes
- **Revenue Impact**: Critical for ensuring proper billing and commission calculations based on task completion

## Test Details

### Preconditions
- User is authenticated with valid Firebase ID token
- Agent profiles exist in the system
- Credit system is operational
- Task state machine rules are configured
- Database transaction support is enabled

### Test Steps

#### Scenario 1: Complete Valid State Progression

1. **Create Task in Draft State**
   ```json
   POST /api/tasks
   {
     "title": "Logo Design Project",
     "description": "Create modern logo for tech startup",
     "requirements": ["Graphic design", "Logo experience"],
     "budget": 15000,
     "deadline": "2024-12-31T23:59:59Z"
   }
   ```
   **Verify**: Task created with status "draft"

2. **Transition Draft → Open**
   ```json
   PUT /api/tasks/{taskId}/status
   {
     "status": "open",
     "action": "post_task"
   }
   ```
   **Verify**: 
   - Status updated to "open"
   - Task visible in public marketplace
   - Credits escrowed from user balance

3. **Transition Open → Matched**
   ```json
   PUT /api/tasks/{taskId}/status
   {
     "status": "matched",
     "action": "accept_match",
     "agentId": "agent-uuid",
     "matchScore": 0.87
   }
   ```
   **Verify**:
   - Status updated to "matched"
   - Agent assigned to task
   - Task removed from public marketplace

4. **Transition Matched → In Progress**
   ```json
   PUT /api/tasks/{taskId}/status
   {
     "status": "in_progress",
     "action": "start_work",
     "startedAt": "2024-06-22T10:00:00Z"
   }
   ```
   **Verify**:
   - Status updated to "in_progress"
   - Work start timestamp recorded
   - Progress tracking initiated

5. **Transition In Progress → Completed**
   ```json
   PUT /api/tasks/{taskId}/status
   {
     "status": "completed",
     "action": "submit_deliverable",
     "deliverableUrl": "https://storage.googleapis.com/deliverables/logo-final.zip",
     "completedAt": "2024-06-25T16:30:00Z"
   }
   ```
   **Verify**:
   - Status updated to "completed"
   - Deliverable attached
   - Credits released to agent
   - Commission calculated and deducted

#### Scenario 2: Invalid State Transition Attempts

1. **Invalid: Draft → Matched (Skip Open)**
   ```json
   PUT /api/tasks/{taskId}/status
   {
     "status": "matched",
     "action": "invalid_transition"
   }
   ```
   **Expected**: 400 Bad Request, "Invalid state transition from 'draft' to 'matched'"

2. **Invalid: Open → Completed (Skip States)**
   ```json
   PUT /api/tasks/{taskId}/status
   {
     "status": "completed",
     "action": "invalid_skip"
   }
   ```
   **Expected**: 400 Bad Request, "Cannot transition from 'open' to 'completed' without intermediate states"

3. **Invalid: Completed → In Progress (Backward)**
   ```json
   PUT /api/tasks/{taskId}/status
   {
     "status": "in_progress",
     "action": "revert_completion"
   }
   ```
   **Expected**: 400 Bad Request, "Cannot revert task from 'completed' to 'in_progress'"

#### Scenario 3: State-Specific Business Rules

1. **Draft State Rules**
   - Task modifications allowed
   - No credit escrow
   - Not visible to agents
   - Can be deleted by owner

2. **Open State Rules**
   - Task modifications restricted
   - Credits escrowed
   - Visible in marketplace
   - Cannot be deleted

3. **Matched State Rules**
   - No task modifications
   - Agent assignment locked
   - Communication channels opened
   - Cancellation requires approval

4. **In Progress Rules**
   - Work tracking active
   - Progress updates allowed
   - Timeline monitoring enabled
   - Milestone tracking available

5. **Completed Rules**
   - Task immutable
   - Payment processing triggered
   - Rating/review enabled
   - Audit trail finalized

### Expected Results

#### Valid Transition Response
```json
{
  "success": true,
  "task": {
    "id": "task-uuid",
    "status": "in_progress",
    "previousStatus": "matched",
    "transitionedAt": "2024-06-22T10:00:00Z",
    "transitionedBy": "system",
    "stateHistory": [
      {
        "status": "draft",
        "timestamp": "2024-06-20T09:00:00Z",
        "triggeredBy": "user-uuid"
      },
      {
        "status": "open", 
        "timestamp": "2024-06-20T10:00:00Z",
        "triggeredBy": "user-uuid"
      },
      {
        "status": "matched",
        "timestamp": "2024-06-21T14:30:00Z", 
        "triggeredBy": "matching-engine"
      },
      {
        "status": "in_progress",
        "timestamp": "2024-06-22T10:00:00Z",
        "triggeredBy": "agent-uuid"
      }
    ]
  }
}
```

#### State Machine Validation
- **Audit Trail**: Complete history of state changes
- **Rollback Prevention**: No backward transitions allowed
- **Business Rules**: State-specific operations enforced
- **Data Consistency**: All related entities updated atomically

### Error Scenarios

#### Concurrent State Changes
- **Scenario**: Two simultaneous status update requests
- **Expected**: One succeeds, other receives 409 Conflict
- **Verification**: Database consistency maintained

#### Missing Required Data
- **Scenario**: Transition to "matched" without agent assignment
- **Expected**: 400 Bad Request, "Agent assignment required for matched status"

#### Insufficient Permissions
- **Scenario**: Non-owner attempts to transition task status
- **Expected**: 403 Forbidden, "Only task owner or assigned agent can change status"

### Performance Criteria
- **State Transition Time**: < 100ms for simple transitions
- **Database Consistency**: ACID compliance for all state changes
- **Concurrent Handling**: Support 50 simultaneous state changes
- **Audit Performance**: State history queries < 200ms

### Security Validations
- **Authorization**: Verify user permissions for state transitions
- **Input Validation**: Sanitize all status change parameters
- **Audit Logging**: Record all state transition attempts
- **Data Integrity**: Prevent tampering with state history
- **Role Verification**: Ensure appropriate role for each transition type

### Business Rules Enforcement

#### Credit Management by State
- **Draft**: No credits affected
- **Open**: Credits escrowed (held but not charged)
- **Matched**: Credits remain escrowed
- **In Progress**: Credits still escrowed
- **Completed**: Credits released to agent minus platform commission

#### Visibility Rules
- **Draft**: Only visible to task owner
- **Open**: Visible to all eligible agents
- **Matched**: Visible to task owner and matched agent
- **In Progress**: Visible to participants and admins
- **Completed**: Visible to participants, ratings public

### Post-conditions
- Task status accurately reflects current lifecycle stage
- All state transitions properly logged with timestamps
- Business rules enforced for each state
- Related data (credits, visibility, permissions) updated consistently
- System ready for next lifecycle operation
- Audit trail complete and immutable
- Performance metrics recorded for state transition times