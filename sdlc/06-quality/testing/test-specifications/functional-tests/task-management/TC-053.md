# Test Case TC-053: Illegal State Transition Prevention

## Test Information
- **Test ID**: TC-053
- **Objective**: Verify comprehensive prevention of invalid task state transitions and maintain data integrity through robust state machine enforcement
- **Requirements**: FR-046, NFR-003 (data integrity), NFR-008 (error handling), ADR-008 (state management)
- **Priority**: Critical
- **Test Type**: Integration
- **Automation**: Yes
- **Dependencies**: Task state machine, Database transaction controls, Error handling system

## Business Context
- **User Journey**: System prevents users from making invalid state changes that could corrupt task data or create inconsistent states
- **Business Impact**: Maintains platform reliability and prevents data corruption that could lead to payment disputes or system failures
- **Revenue Impact**: Data integrity prevents financial inconsistencies and maintains user trust in platform operations

## Test Details

### Preconditions
- Tasks exist in various valid states (draft, open, matched, in_progress, completed)
- State machine rules are properly configured
- Database transaction controls are active
- Error handling and logging systems are operational
- User authentication and authorization are functional

### Test Steps

#### Scenario 1: Forward State Skipping Prevention

1. **Attempt Draft → Matched (Skip Open State)**
   ```json
   PUT /api/tasks/{draftTaskId}/status
   {
     "status": "matched",
     "agentId": "agent-uuid",
     "reason": "direct_assignment"
   }
   ```

2. **Verify Rejection**
   - HTTP Status: 400 Bad Request
   - Error Message: "Invalid state transition: cannot move from 'draft' to 'matched' without intermediate 'open' state"
   - Task status remains "draft"
   - No database changes occur

3. **Attempt Open → In Progress (Skip Matched State)**
   ```json
   PUT /api/tasks/{openTaskId}/status
   {
     "status": "in_progress",
     "agentId": "agent-uuid",
     "startTime": "2024-06-22T10:00:00Z"
   }
   ```

4. **Verify Rejection**
   - HTTP Status: 400 Bad Request
   - Error Message: "Invalid state transition: task must be in 'matched' state before starting work"
   - Task status remains "open"
   - No agent assignment occurs

#### Scenario 2: Backward State Transition Prevention

1. **Attempt Completed → In Progress (Backward)**
   ```json
   PUT /api/tasks/{completedTaskId}/status
   {
     "status": "in_progress",
     "reason": "reopen_for_revisions"
   }
   ```

2. **Verify Rejection**
   - HTTP Status: 400 Bad Request
   - Error Message: "Invalid state transition: cannot revert from 'completed' to 'in_progress'"
   - Suggested alternative: "Use revision request workflow for completed tasks"
   - Task status remains "completed"

3. **Attempt In Progress → Open (Backward)**
   ```json
   PUT /api/tasks/{inProgressTaskId}/status
   {
     "status": "open",
     "reason": "agent_unavailable"
   }
   ```

4. **Verify Rejection**
   - HTTP Status: 400 Bad Request
   - Error Message: "Invalid state transition: cannot revert from 'in_progress' to 'open'"
   - Suggested alternative: "Use task reassignment or cancellation workflow"
   - Task status remains "in_progress"

#### Scenario 3: Invalid State Value Prevention

1. **Attempt Invalid Status Value**
   ```json
   PUT /api/tasks/{taskId}/status
   {
     "status": "invalid_status",
     "reason": "testing"
   }
   ```

2. **Verify Rejection**
   - HTTP Status: 400 Bad Request
   - Error Message: "Invalid status value 'invalid_status'. Valid statuses: draft, open, matched, in_progress, completed, cancelled"
   - Task status unchanged

3. **Attempt Empty Status**
   ```json
   PUT /api/tasks/{taskId}/status
   {
     "status": "",
     "reason": "empty_test"
   }
   ```

4. **Verify Rejection**
   - HTTP Status: 400 Bad Request
   - Error Message: "Status field is required and cannot be empty"

#### Scenario 4: Concurrent State Change Prevention

1. **Simulate Concurrent Status Updates**
   ```javascript
   // Simulate two simultaneous requests
   const request1 = fetch('/api/tasks/task-uuid/status', {
     method: 'PUT',
     body: JSON.stringify({status: 'open', reason: 'posting_task'})
   });
   
   const request2 = fetch('/api/tasks/task-uuid/status', {
     method: 'PUT', 
     body: JSON.stringify({status: 'matched', agentId: 'agent-uuid'})
   });
   
   Promise.all([request1, request2]);
   ```

2. **Verify Concurrency Control**
   - One request succeeds (first to acquire lock)
   - Second request receives 409 Conflict
   - Database maintains consistency
   - Task has single, valid final state

#### Scenario 5: Missing Required Data Prevention

1. **Attempt Matched Status Without Agent**
   ```json
   PUT /api/tasks/{openTaskId}/status
   {
     "status": "matched",
     "reason": "assignment_test"
     // Missing required agentId
   }
   ```

2. **Verify Data Validation**
   - HTTP Status: 400 Bad Request
   - Error Message: "Agent ID is required when transitioning to 'matched' status"
   - Task status remains "open"

3. **Attempt In Progress Without Start Time**
   ```json
   PUT /api/tasks/{matchedTaskId}/status
   {
     "status": "in_progress",
     "agentId": "agent-uuid"
     // Missing required startTime
   }
   ```

4. **Verify Timestamp Validation**
   - HTTP Status: 400 Bad Request
   - Error Message: "Start time is required when transitioning to 'in_progress' status"
   - Task status remains "matched"

#### Scenario 6: Business Rule Violation Prevention

1. **Attempt Completion Without Deliverables**
   ```json
   PUT /api/tasks/{inProgressTaskId}/status
   {
     "status": "completed",
     "completionTime": "2024-06-22T16:00:00Z"
     // Missing required deliverables
   }
   ```

2. **Verify Business Rule Enforcement**
   - HTTP Status: 400 Bad Request
   - Error Message: "Cannot complete task without submitting deliverables"
   - Task status remains "in_progress"

3. **Attempt State Change Without Permissions**
   ```json
   // Non-owner user attempts to change task status
   PUT /api/tasks/{taskId}/status
   {
     "status": "open",
     "reason": "unauthorized_change"
   }
   ```

4. **Verify Authorization**
   - HTTP Status: 403 Forbidden
   - Error Message: "Insufficient permissions to change task status"
   - Task status unchanged

### Expected Results

#### State Transition Validation Response
```json
{
  "error": true,
  "code": "INVALID_STATE_TRANSITION",
  "message": "Invalid state transition from 'draft' to 'matched'",
  "details": {
    "currentStatus": "draft",
    "attemptedStatus": "matched",
    "validNextStates": ["open"],
    "requiredSteps": [
      "1. Transition to 'open' status",
      "2. Allow agent matching",
      "3. Transition to 'matched' when agent accepts"
    ]
  },
  "suggestions": {
    "correctWorkflow": "Use POST /api/tasks/{id}/publish to move task to 'open' status",
    "documentation": "https://docs.vibematch.com/task-lifecycle"
  },
  "timestamp": "2024-06-22T14:30:00Z",
  "requestId": "req-uuid"
}
```

#### State Machine Validation Matrix
| From State  | To State    | Valid | Required Data | Business Rules |
|-------------|-------------|-------|---------------|----------------|
| draft       | open        | ✓     | None          | Task validation |
| draft       | matched     | ✗     | N/A           | Must go through open |
| draft       | in_progress | ✗     | N/A           | Must go through open→matched |
| open        | matched     | ✓     | agentId       | Agent available |
| open        | draft       | ✗     | N/A           | No backward transitions |
| matched     | in_progress | ✓     | startTime     | Agent confirmation |
| in_progress | completed   | ✓     | deliverables  | Work completed |
| completed   | any         | ✗     | N/A           | Final state |

### Error Scenarios

#### Database Transaction Rollback
- **Scenario**: State change partially succeeds then fails
- **Expected**: Complete transaction rollback, original state restored
- **Verification**: No partial data corruption

#### Invalid JSON Payload
- **Input**: Malformed JSON in request body
- **Expected**: 400 Bad Request, "Invalid JSON format"

#### Missing Authentication
- **Input**: Request without valid authentication token
- **Expected**: 401 Unauthorized, "Authentication required"

#### System Error During Validation
- **Scenario**: Database unavailable during state validation
- **Expected**: 503 Service Unavailable, retry mechanism activated

### Performance Criteria
- **Validation Time**: < 50ms for state transition validation
- **Concurrency Handling**: Support 100 concurrent state change attempts
- **Database Locking**: Minimal lock duration to prevent bottlenecks
- **Error Response**: < 100ms for validation error responses
- **Audit Logging**: < 200ms for complete audit trail recording

### Security Validations
- **Input Sanitization**: All status values sanitized against injection attacks
- **Authorization Checks**: Verify user permissions for each state transition
- **Audit Logging**: All transition attempts logged with user context
- **Rate Limiting**: Prevent state transition spam attacks
- **Data Integrity**: Cryptographic verification of state change authenticity

### Business Rules Enforcement

#### State-Specific Validations
- **Draft → Open**: Validate task completeness and budget availability
- **Open → Matched**: Verify agent eligibility and availability
- **Matched → In Progress**: Confirm agent acceptance and readiness
- **In Progress → Completed**: Validate deliverable submission
- **Any → Cancelled**: Check cancellation policy compliance

#### Cross-Service Consistency
- **Credit System**: Ensure credit status matches task status
- **Notification System**: Prevent notifications for invalid transitions
- **Matching Engine**: Maintain consistency with matching results
- **Analytics**: Ensure accurate metrics despite invalid attempts

### Monitoring and Alerting

#### Error Pattern Detection
- **High Error Rate**: Alert if >5% of state transitions fail
- **Repeated Invalid Attempts**: Flag users making many invalid transitions
- **System Integrity**: Monitor for state inconsistencies
- **Performance Degradation**: Alert on slow validation performance

#### Audit and Compliance
- **Complete Audit Trail**: All transition attempts logged
- **Data Integrity Reports**: Regular validation of state consistency
- **Compliance Monitoring**: Ensure state transitions meet business requirements
- **Forensic Capabilities**: Detailed logs for dispute resolution

### Post-conditions
- All invalid state transitions properly rejected
- Task data integrity maintained across all operations
- Complete audit trail of all transition attempts
- Error messages provide clear guidance for valid workflows
- System performance remains optimal despite validation overhead
- Business rules consistently enforced across all scenarios
- Database consistency maintained under all failure conditions