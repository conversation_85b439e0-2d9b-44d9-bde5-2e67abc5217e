# Test Case TC-029: User Notification Preferences

## Test Information
- **Test ID**: TC-029
- **Objective**: Verify that users can manage their notification preferences and receive appropriate notifications
- **Requirements**: User notification and preference management
- **Priority**: Low
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- User management service with notification preferences is available
- Notification service is configured and operational
- Test user exists with authenticated session

### Test Steps
1. Retrieve current notification preferences:
   ```
   GET /api/v1/user/preferences/notifications
   Authorization: Bearer <token>
   ```
2. Update notification preferences:
   ```json
   {
     "emailNotifications": {
       "taskUpdates": true,
       "matchingResults": false,
       "paymentConfirmations": true,
       "systemAnnouncements": true
     },
     "pushNotifications": {
       "taskUpdates": true,
       "messages": true,
       "matchingResults": true
     },
     "frequency": "immediate"
   }
   ```
3. Test notification delivery based on preferences:
   - Generate task update event
   - Verify email sent if enabled, not sent if disabled
   - Test push notification delivery
4. Test opt-out compliance:
   - Disable all marketing notifications
   - Verify no marketing emails are sent
   - Ensure critical notifications still delivered
5. Test notification frequency settings:
   - Set frequency to "daily digest"
   - Verify notifications are batched appropriately
   - Test "immediate" vs "daily" vs "weekly" options
6. Test mandatory notifications:
   - Security alerts (cannot be disabled)
   - Account changes (required for security)
   - Legal notices (compliance required)
7. Test notification delivery channels:
   - Email notification format and content
   - Push notification format
   - In-app notification display

### Expected Results
- Preferences retrieval: Current settings returned
  ```json
  {
    "emailNotifications": {
      "taskUpdates": true,
      "matchingResults": true,
      "paymentConfirmations": true,
      "systemAnnouncements": false,
      "marketing": false
    },
    "pushNotifications": {
      "enabled": true,
      "taskUpdates": true,
      "messages": true
    },
    "frequency": "immediate"
  }
  ```
- Preference updates: 200 OK with confirmation
- Notification delivery: Respects user preferences
- Opt-out compliance: Marketing notifications stopped
- Mandatory notifications: Always delivered regardless of preferences
- Frequency settings: Notifications batched per user choice
- Delivery channels: Notifications formatted appropriately for channel
- Unsubscribe links: Present in all marketing emails

### Post-conditions
- User notification preferences are saved and respected
- System complies with email marketing regulations
- Critical notifications always reach users
- User experience is customized per preferences

### Test Data
- **Notification Types**: taskUpdates, matchingResults, paymentConfirmations, systemAnnouncements, marketing
- **Delivery Channels**: email, push, in-app
- **Frequency Options**: immediate, daily, weekly
- **Mandatory Types**: security alerts, account changes, legal notices