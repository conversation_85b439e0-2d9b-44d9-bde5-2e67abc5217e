# Test Case TC-021: User Profile Viewing

## Test Information
- **Test ID**: TC-021
- **Objective**: Verify that authenticated users can view their own profile data
- **Requirements**: FR-019
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- User management service is available
- Test user exists with complete profile data
- User is authenticated with valid token

### Test Steps
1. Authenticate test user and obtain valid token:
   ```json
   {
     "email": "<EMAIL>",
     "password": "TestPass123!"
   }
   ```
2. Request user profile using authenticated endpoint:
   ```
   GET /api/v1/user/profile
   Authorization: Bearer <token>
   ```
3. Verify response contains complete profile data
4. Test profile data structure and field presence:
   - User ID
   - Email address
   - Display name
   - Role
   - Profile image URL (if set)
   - Account timestamps
5. Test data accuracy by comparing with database:
   - Verify email matches authentication
   - Check display name is current
   - Validate timestamps are accurate
6. Test access control:
   - Attempt to access without authentication
   - Verify proper error response
7. Test profile with minimal data:
   - User with only required fields
   - Verify optional fields show as null/empty

### Expected Results
- Authenticated request: 200 OK with complete profile
  ```json
  {
    "user": {
      "id": "user123",
      "email": "<EMAIL>",
      "displayName": "Profile View User",
      "role": "User",
      "profileImage": "https://example.com/avatar.jpg",
      "createdAt": "2025-01-01T10:00:00Z",
      "updatedAt": "2025-01-02T15:30:00Z"
    }
  }
  ```
- Unauthenticated request: 401 Unauthorized
  ```json
  {
    "error": "Authentication required",
    "code": "AUTH_REQUIRED"
  }
  ```
- Profile data matches database records exactly
- Optional fields (profileImage) show null if not set
- Response time < 200ms for profile retrieval
- All timestamps in ISO 8601 UTC format
- No sensitive data (password hash) exposed

### Post-conditions
- User profile data is accessible to authenticated user
- Security boundaries prevent unauthorized access
- Data integrity is maintained in response

### Test Data
- **Test User**: <EMAIL>
- **Required Fields**: id, email, displayName, role, createdAt, updatedAt
- **Optional Fields**: profileImage
- **Sensitive Fields**: password hash (should not be exposed)