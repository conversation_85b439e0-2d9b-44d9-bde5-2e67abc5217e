# Test Case TC-027: User Profile Privacy and Access Control

## Test Information
- **Test ID**: TC-027
- **Objective**: Verify that user profile data has proper privacy controls and access restrictions
- **Requirements**: Privacy and access control requirements
- **Priority**: High
- **Test Type**: Security/Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- User management service with privacy controls is available
- Multiple test users exist with different roles
- Privacy settings are configurable

### Test Steps
1. Test own profile access (standard user):
   ```
   GET /api/v1/user/profile
   Authorization: Bearer <user_token>
   ```
2. Test accessing other user's private profile:
   ```
   GET /api/v1/users/{other_user_id}/profile
   Authorization: Bearer <user_token>
   ```
3. Test public profile information access:
   ```
   GET /api/v1/users/{user_id}/public-profile
   ```
4. Test admin access to user profiles:
   ```
   GET /api/v1/users/{user_id}/profile
   Authorization: Bearer <admin_token>
   ```
5. Test profile privacy settings:
   - Update privacy settings to limit profile visibility
   - Test impact on public profile endpoint
   - Verify private information is hidden
6. Test role-based profile access:
   - Agent accessing user profiles
   - Moderator accessing profiles for review
   - Customer support accessing for assistance
7. Test sensitive data protection:
   - Email address visibility
   - Contact information protection
   - Financial data access restrictions

### Expected Results
- Own profile access: Full profile data returned
- Other user private access: 403 Forbidden
  ```json
  {
    "error": "Access denied to user profile",
    "code": "PROFILE_ACCESS_DENIED"
  }
  ```
- Public profile: Limited information only
  ```json
  {
    "user": {
      "id": "user123",
      "displayName": "Public User",
      "publicProfileImage": "https://example.com/avatar.jpg"
    }
  }
  ```
- Admin access: Full profile data with admin context
- Privacy settings: Respected in all access scenarios
- Role-based access: Appropriate data returned per role
- Sensitive data: Protected according to privacy rules
- Unauthenticated access: Public data only, if any

### Post-conditions
- User privacy is maintained across all access patterns
- Role-based access control functions correctly
- Sensitive information is properly protected
- Public profiles provide appropriate level of information

### Test Data
- **Standard User**: <EMAIL>
- **Admin User**: <EMAIL>
- **Public Profile Fields**: displayName, publicProfileImage
- **Private Profile Fields**: email, phone, address, financial data