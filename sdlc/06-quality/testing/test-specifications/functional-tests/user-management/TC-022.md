# Test Case TC-022: User Profile Updates - Display Name and Profile Image

## Test Information
- **Test ID**: TC-022
- **Objective**: Verify that users can update their displayName and profileImage fields
- **Requirements**: FR-020
- **Priority**: Medium
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- User management service is available
- Test user exists with authenticated session
- File upload service is available for profile images

### Test Steps
1. Authenticate test user and get current profile:
   ```
   GET /api/v1/user/profile
   Authorization: Bearer <token>
   ```
2. Update display name only:
   ```json
   {
     "displayName": "Updated Display Name"
   }
   ```
3. Verify display name update:
   - Check response confirms update
   - Retrieve profile to verify change persisted
   - Verify updatedAt timestamp changed
4. Update profile image URL:
   ```json
   {
     "profileImage": "https://example.com/new-avatar.jpg"
   }
   ```
5. Update both fields simultaneously:
   ```json
   {
     "displayName": "New Name",
     "profileImage": "https://example.com/avatar2.jpg"
   }
   ```
6. Test validation rules:
   - Empty display name
   - Display name too long (>100 characters)
   - Invalid profile image URL format
   - Profile image URL too long
7. Test concurrent updates:
   - Make simultaneous update requests
   - Verify last update wins scenario
8. Test partial updates:
   - Send only displayName (profileImage unchanged)
   - Send only profileImage (displayName unchanged)

### Expected Results
- Successful update: 200 OK with updated profile
  ```json
  {
    "user": {
      "id": "user123",
      "email": "<EMAIL>",
      "displayName": "Updated Display Name",
      "role": "User",
      "profileImage": "https://example.com/new-avatar.jpg",
      "updatedAt": "2025-01-01T10:30:00Z"
    }
  }
  ```
- Validation errors: 400 Bad Request
  - Empty display name: "Display name cannot be empty"
  - Name too long: "Display name must be 100 characters or less"
  - Invalid URL: "Profile image must be a valid URL"
- Partial updates: Only specified fields change
- Concurrent updates: Last request wins
- updatedAt timestamp: Changes with each successful update
- createdAt timestamp: Remains unchanged
- Response time < 300ms for profile updates

### Post-conditions
- User profile reflects all successful updates
- Database shows updated values and timestamp
- No unintended fields were modified
- User can see changes in subsequent profile views

### Test Data
- **Test User**: <EMAIL>
- **Valid Display Name**: "Updated Display Name" (3-100 chars)
- **Valid Profile Image**: https://example.com/avatar.jpg
- **Invalid Cases**: "", "Very long name exceeding 100 characters...", "invalid-url"