# Test Case TC-024: User Timestamp Tracking

## Test Information
- **Test ID**: TC-024
- **Objective**: Verify that user creation and update timestamps are properly tracked and maintained
- **Requirements**: FR-022
- **Priority**: Low
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- User management service is available
- Database is configured with timestamp tracking
- System clock is synchronized

### Test Steps
1. Register new user and capture creation time:
   ```json
   {
     "email": "<EMAIL>",
     "password": "TestPass123!",
     "displayName": "Timestamp User"
   }
   ```
2. Verify createdAt timestamp in registration response:
   - Check timestamp format (ISO 8601 UTC)
   - Verify timestamp is recent (within last minute)
   - Confirm updatedAt equals createdAt initially
3. Retrieve user profile and verify timestamps:
   ```
   GET /api/v1/user/profile
   Authorization: Bearer <token>
   ```
4. Update user profile and check timestamp behavior:
   ```json
   {
     "displayName": "Updated Timestamp User"
   }
   ```
5. Verify updatedAt timestamp changes:
   - updatedAt is newer than original value
   - createdAt remains unchanged
   - Time difference reflects update time
6. Test multiple updates in sequence:
   - Update profile multiple times
   - Verify updatedAt advances with each update
   - Verify createdAt never changes
7. Test timestamp precision and format:
   - Check millisecond precision
   - Verify UTC timezone
   - Validate ISO 8601 format compliance
8. Test timestamp immutability:
   - Attempt to manually set timestamps in update
   - Verify system-generated timestamps are used

### Expected Results
- New user registration: Both timestamps set to current time
  ```json
  {
    "user": {
      "id": "user123",
      "email": "<EMAIL>",
      "displayName": "Timestamp User",
      "createdAt": "2025-01-01T10:00:00.123Z",
      "updatedAt": "2025-01-01T10:00:00.123Z"
    }
  }
  ```
- Profile updates: Only updatedAt changes
  ```json
  {
    "user": {
      "id": "user123",
      "displayName": "Updated Timestamp User",
      "createdAt": "2025-01-01T10:00:00.123Z",
      "updatedAt": "2025-01-01T10:05:30.456Z"
    }
  }
  ```
- Timestamp format: ISO 8601 with milliseconds and UTC (Z suffix)
- Timestamp accuracy: Within 1 second of actual operation time
- createdAt immutability: Never changes after initial creation
- updatedAt progression: Always increases with updates
- Manual timestamp attempts: Ignored, system values used

### Post-conditions
- Accurate audit trail of user account lifecycle
- Reliable timestamps for business logic and analytics
- Data integrity maintained for temporal queries
- Compliance with timestamp tracking requirements

### Test Data
- **Test User**: <EMAIL>
- **Timestamp Format**: YYYY-MM-DDTHH:mm:ss.sssZ
- **Update Scenarios**: displayName, profileImage updates
- **Manual Timestamp Attempts**: createdAt, updatedAt in request payload