# Test Case TC-008: Login Rate Limiting Implementation

## Test Information
- **Test ID**: TC-008
- **Objective**: Verify that login rate limiting restricts users to 5 login attempts per 15 minutes
- **Requirements**: FR-012
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Rate limiting service is configured
- Test user exists with valid credentials
- Clean slate (no previous failed attempts)

### Test Steps
1. Perform 5 consecutive failed login attempts:
   ```json
   {
     "email": "<EMAIL>",
     "password": "WrongPassword"
   }
   ```
2. Record response times and status codes for each attempt
3. Attempt 6th login (should be rate limited):
   - Use same incorrect password
   - Verify rate limit response
4. Attempt login with correct password after rate limit:
   ```json
   {
     "email": "<EMAIL>",
     "password": "TestPass123!"
   }
   ```
5. Wait 15 minutes and test rate limit reset:
   - Attempt login with incorrect password
   - Verify rate limiting has reset
6. Test successful login within limit:
   - Make 3 failed attempts
   - Make 1 successful attempt
   - Verify successful login resets counter
7. Test rate limiting per email address:
   - <NAME_EMAIL>
   - Verify <EMAIL> is not affected

### Expected Results
- First 5 failed attempts: 401 Unauthorized with authentication failure message
- 6th attempt: 429 Too Many Requests with rate limit message:
  ```json
  {
    "error": "Too many login attempts",
    "code": "RATE_LIMIT_EXCEEDED",
    "retryAfter": 900
  }
  ```
- Correct password during rate limit: Still blocked with 429 status
- After 15 minutes: Rate limit resets, login attempts allowed
- Successful login: Resets failed attempt counter
- Rate limiting is per-email-address, not global
- Response includes `Retry-After` header with seconds until reset

### Post-conditions
- Failed login attempts are tracked per email address
- Rate limit automatically resets after 15 minutes
- Successful authentication clears failed attempt counter
- System maintains security against brute force attacks

### Test Data
- **Test Email**: <EMAIL>
- **Invalid Password**: WrongPassword
- **Valid Password**: TestPass123!
- **Rate Limit**: 5 attempts per 15 minutes (900 seconds)
- **Other Email**: <EMAIL>