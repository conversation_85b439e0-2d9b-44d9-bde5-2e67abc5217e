# Test Case TC-004: Email Uniqueness Enforcement

## Test Information
- **Test ID**: TC-004
- **Objective**: Verify that email uniqueness is enforced across all users, preventing duplicate registrations
- **Requirements**: FR-008
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Firestore database is accessible
- Test user already exists with email `<EMAIL>`

### Test Steps
1. Attempt to register new user with existing email:
   - Send POST request to `/api/v1/auth/register` with:
   ```json
   {
     "email": "<EMAIL>",
     "password": "NewPass123!",
     "displayName": "Duplicate User"
   }
   ```
2. Verify immediate rejection without database write
3. Test case sensitivity:
   - Attempt registration with `<EMAIL>`
   - Attempt registration with `<EMAIL>`
4. Test email normalization:
   - Attempt registration with `<EMAIL>`
   - Attempt registration with `<EMAIL>` (whitespace)
5. Verify existing user functionality remains intact:
   - Existing user can still login
   - Existing user profile remains unchanged

### Expected Results
- Duplicate email registration returns 409 Conflict status
- Error message: "Email address already registered"
- Case-insensitive email matching (uppercase/mixed case rejected)
- Email normalization prevents circumvention with tags/whitespace
- Response time < 200ms (quick database lookup)
- No new user document created in database
- Existing user functionality unaffected
- Consistent behavior across all duplicate scenarios

### Post-conditions
- Database maintains email uniqueness constraint
- Original user account remains secure and functional
- System prevents account hijacking through duplicate emails

### Test Data
- **Existing Email**: <EMAIL>
- **Duplicate Attempts**: <EMAIL>, <EMAIL>, <EMAIL>
- **Normalization Tests**: <EMAIL>, ` <EMAIL> `