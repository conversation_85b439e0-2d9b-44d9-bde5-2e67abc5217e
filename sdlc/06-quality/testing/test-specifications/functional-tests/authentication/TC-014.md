# Test Case TC-014: Refresh Token Revocation on Logout

## Test Information
- **Test ID**: TC-014
- **Objective**: Verify that refresh tokens are added to the revoked collection upon user logout
- **Requirements**: FR-018
- **Priority**: Medium
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Firestore database with revoked_tokens collection configured
- Test user is authenticated with valid ID and refresh tokens
- Access to monitor Firestore revoked_tokens collection

### Test Steps
1. Authenticate user and capture refresh token:
   ```json
   {
     "email": "<EMAIL>",
     "password": "TestPass123!"
   }
   ```
2. Record refresh token value for verification
3. Initiate logout request:
   ```
   POST /api/v1/auth/logout
   Authorization: Bearer <id_token>
   ```
4. Verify logout response is successful
5. Check revoked_tokens collection in Firestore:
   - Verify refresh token is added to collection
   - Check document structure and fields
   - Verify TTL is set appropriately
6. Test refresh token usage after revocation:
   - Attempt to use revoked refresh token
   - Verify rejection with appropriate error
7. Test that ID token is also tracked if required:
   - Check if ID token is also added to revoked collection
   - Verify both tokens are properly tracked
8. Test collection cleanup behavior:
   - Verify old revoked tokens are cleaned up per TTL
   - Check that collection doesn't grow indefinitely

### Expected Results
- Logout successful: 200 OK response
- Revoked collection entry created:
  ```json
  {
    "tokenId": "hash_of_refresh_token",
    "tokenType": "refresh",
    "userId": "user123",
    "revokedAt": "2025-01-01T10:00:00Z",
    "expiresAt": "2025-01-08T10:00:00Z"
  }
  ```
- Revoked refresh token usage: 401 Unauthorized
  ```json
  {
    "error": "Refresh token has been revoked",
    "code": "REFRESH_TOKEN_REVOKED"
  }
  ```
- TTL set appropriately (e.g., 7 days from revocation)
- Document includes all required metadata
- Revocation check happens before token refresh processing
- Collection maintains proper indices for efficient lookup

### Post-conditions
- Refresh token cannot be reused for authentication
- Revoked token is tracked in database for security
- TTL ensures automatic cleanup of expired revocations
- User must obtain new tokens through re-authentication

### Test Data
- **Test User**: <EMAIL>
- **Collection**: revoked_tokens
- **TTL Duration**: 7 days (or as configured)
- **Token Types**: refresh (required), id (optional)