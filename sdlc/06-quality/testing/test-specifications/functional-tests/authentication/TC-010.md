# Test Case TC-010: Firebase ID Token Validation for Protected Endpoints

## Test Information
- **Test ID**: TC-010
- **Objective**: Verify that Firebase ID tokens are properly validated for all protected endpoints
- **Requirements**: FR-014
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Protected endpoints are configured with token validation
- Test user exists with valid authentication token
- Multiple protected endpoints available for testing

### Test Steps
1. Obtain valid Firebase ID token through authentication
2. Test valid token access to protected endpoints:
   - GET `/api/v1/user/profile` (user profile)
   - POST `/api/v1/tasks` (create task)
   - GET `/api/v1/agents` (list agents)
   - PUT `/api/v1/user/profile` (update profile)
3. Test invalid token scenarios:
   - No token provided (missing Authorization header)
   - Malformed token (invalid JWT format)
   - Expired token (past expiry time)
   - Token with invalid signature
   - Token with wrong issuer/audience
4. Test edge cases:
   - Token with extra whitespace
   - Token with wrong Bearer prefix
   - Token with missing Bearer prefix
   - Empty Authorization header
5. Verify token validation response consistency:
   - All protected endpoints use same validation logic
   - Consistent error messages and status codes
6. Test token validation performance:
   - Measure response time for token validation
   - Verify validation doesn't introduce significant latency

### Expected Results
- Valid token access: 200 OK with requested resource
- No token provided: 401 Unauthorized
  ```json
  {
    "error": "Authentication required",
    "code": "AUTH_REQUIRED"
  }
  ```
- Malformed token: 401 Unauthorized
  ```json
  {
    "error": "Invalid token format",
    "code": "INVALID_TOKEN"
  }
  ```
- Expired token: 401 Unauthorized
  ```json
  {
    "error": "Token expired",
    "code": "TOKEN_EXPIRED"
  }
  ```
- Invalid signature: 401 Unauthorized
  ```json
  {
    "error": "Invalid token signature",
    "code": "INVALID_SIGNATURE"
  }
  ```
- All protected endpoints enforce token validation consistently
- Token validation adds < 50ms to request processing time

### Post-conditions
- Security boundaries are properly enforced
- Only authenticated users can access protected resources
- Token validation is consistent across all endpoints

### Test Data
- **Valid Token**: Fresh Firebase ID token from authentication
- **Expired Token**: Token with past expiry time
- **Malformed Token**: "invalid.jwt.token"
- **Protected Endpoints**: /api/v1/user/profile, /api/v1/tasks, /api/v1/agents