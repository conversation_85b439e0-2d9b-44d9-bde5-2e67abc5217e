# Test Case TC-011: Token Refresh Mechanism

## Test Information
- **Test ID**: TC-011
- **Objective**: Verify that the token refresh mechanism works correctly to maintain user sessions
- **Requirements**: FR-015
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Firebase Authentication is configured with refresh tokens
- Test user exists with valid credentials
- Token refresh endpoint is available

### Test Steps
1. Authenticate user and capture both ID token and refresh token:
   ```json
   {
     "email": "<EMAIL>",
     "password": "TestPass123!"
   }
   ```
2. Verify initial token response contains refresh token
3. Use refresh token to get new ID token:
   ```json
   {
     "refreshToken": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopq..."
   }
   ```
4. Verify new ID token is issued:
   - New token has different JWT signature
   - New token has updated issue time (`iat`)
   - New token has new expiry time (1 hour from new issue)
   - User claims remain consistent
5. Test refresh token reuse:
   - Use same refresh token again
   - Verify new ID token is issued each time
6. Test invalid refresh token scenarios:
   - Use expired refresh token
   - Use malformed refresh token
   - Use refresh token from different user
   - Use already revoked refresh token
7. Verify old ID token behavior after refresh:
   - Old token should still work until its expiry
   - Both old and new tokens can be used simultaneously

### Expected Results
- Initial authentication: Returns both ID token and refresh token
- Valid refresh request: 200 OK with new ID token
  ```json
  {
    "idToken": "eyJhbGciOiJSUzI1NiIs...",
    "refreshToken": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopq...",
    "expiresIn": "3600"
  }
  ```
- New ID token has fresh expiry (1 hour from refresh time)
- Invalid refresh token: 401 Unauthorized
  ```json
  {
    "error": "Invalid refresh token",
    "code": "INVALID_REFRESH_TOKEN"
  }
  ```
- Refresh token can be reused multiple times
- Old ID tokens remain valid until their original expiry
- Refresh operation completes in < 300ms

### Post-conditions
- User session can be extended without re-authentication
- Multiple valid ID tokens can exist simultaneously
- Security is maintained through proper token lifecycle management

### Test Data
- **Test User**: <EMAIL>
- **Valid Refresh Token**: From successful authentication
- **Invalid Refresh Token**: Expired or malformed token
- **Token Lifetime**: ID tokens 1 hour, refresh tokens longer-lived