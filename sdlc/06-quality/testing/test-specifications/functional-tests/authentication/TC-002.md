# Test Case TC-002: Email Format Validation During Registration

## Test Information
- **Test ID**: TC-002
- **Objective**: Verify that email format validation follows RFC 5322 standards during user registration
- **Requirements**: FR-002
- **Priority**: High
- **Test Type**: Unit
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Email validation service is operational

### Test Steps
1. Test valid email formats:
   - Send registration request with `<EMAIL>`
   - Send registration request with `<EMAIL>`
   - Send registration request with `<EMAIL>`
   - Send registration request with `<EMAIL>`
2. Test invalid email formats:
   - Send registration request with `invalid-email`
   - Send registration request with `@domain.com`
   - Send registration request with `user@`
   - Send registration request with `user@domain`
   - Send registration request with `<EMAIL>`
   - Send registration request with `<EMAIL>`
3. Test edge cases:
   - Send registration request with email containing 254 characters (valid)
   - Send registration request with email containing 255 characters (invalid)
   - Send registration request with local part containing 64 characters (valid)
   - Send registration request with local part containing 65 characters (invalid)

### Expected Results
- Valid email formats (Step 1): Accept with 201 status or proceed to password validation
- Invalid email formats (Step 2): Reject with 400 Bad Request and specific error message
- Edge cases (Step 3): Validate according to RFC 5322 length limits
- Error response includes field validation details
- Error message format: "Invalid email format"

### Post-conditions
- Only valid email formats proceed to full registration
- Invalid emails are rejected before database operations
- Appropriate error messages guide user correction

### Test Data
- **Valid Emails**: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
- **Invalid Emails**: invalid-email, @domain.com, user@, user@domain, <EMAIL>, <EMAIL>
- **Edge Case Emails**: 254-char email, 255-char email, 64-char local part, 65-char local part