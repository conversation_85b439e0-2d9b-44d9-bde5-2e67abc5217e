# Test Case TC-003: Password Complexity Rules Enforcement

## Test Information
- **Test ID**: TC-003
- **Objective**: Verify that password complexity rules are enforced according to FR-003 specifications
- **Requirements**: FR-003
- **Priority**: High
- **Test Type**: Unit
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Password validation service is operational

### Test Steps
1. Test minimum length requirement (8 characters):
   - Submit password with 7 characters: `Pass12!`
   - Submit password with 8 characters: `Password1!`
   - Submit password with 9 characters: `Password12!`
2. Test uppercase letter requirement:
   - Submit password without uppercase: `password123!`
   - Submit password with uppercase: `Password123!`
3. Test lowercase letter requirement:
   - Submit password without lowercase: `PASSWORD123!`
   - Submit password with lowercase: `Password123!`
4. Test number requirement:
   - Submit password without numbers: `Password!`
   - Submit password with numbers: `Password123!`
5. Test special character requirement:
   - Submit password without special chars: `Password123`
   - Test each valid special character: `!@#$%^&*()_+-=[]{}|;:,.<>?`
   - Submit password with invalid special char: `Password123€`
6. Test combination requirements:
   - Submit password meeting all requirements: `MyPass123!`
   - Submit password missing multiple requirements: `password`

### Expected Results
- Passwords not meeting minimum length: 400 Bad Request with "Password must be at least 8 characters"
- Passwords missing uppercase: 400 Bad Request with "Password must contain at least 1 uppercase letter"
- Passwords missing lowercase: 400 Bad Request with "Password must contain at least 1 lowercase letter" 
- Passwords missing numbers: 400 Bad Request with "Password must contain at least 1 number"
- Passwords missing special chars: 400 Bad Request with "Password must contain at least 1 special character"
- Valid passwords: Proceed to next validation step or complete registration
- Invalid special characters: 400 Bad Request with list of allowed special characters
- Multiple violations: Return all applicable error messages

### Post-conditions
- Only passwords meeting all complexity requirements proceed
- Clear error messages guide users to create compliant passwords
- Password validation occurs before any database operations

### Test Data
- **Valid Passwords**: MyPass123!, SecureP@ss1, Complex1#Pass
- **Invalid Passwords**: pass123!, PASSWORD123!, Password!, password123, Pa55w0rd€
- **Special Characters**: !@#$%^&*()_+-=[]{}|;:,.<>?