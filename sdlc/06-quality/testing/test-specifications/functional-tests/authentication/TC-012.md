# Test Case TC-012: Revoked Tokens Collection with TTL

## Test Information
- **Test ID**: TC-012
- **Objective**: Verify that revoked tokens are maintained in a collection with appropriate TTL to prevent reuse
- **Requirements**: FR-016
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Firestore database has revoked_tokens collection configured
- T<PERSON> (time-to-live) is set for revoked_tokens documents
- Test user exists with valid authentication

### Test Steps
1. Authenticate user and obtain refresh token:
   ```json
   {
     "email": "<EMAIL>",
     "password": "TestPass123!"
   }
   ```
2. Initiate logout to revoke the refresh token
3. Verify token is added to revoked_tokens collection:
   - Check Firestore revoked_tokens collection
   - Verify document contains token hash/identifier
   - Verify document has TTL field set
4. Attempt to use revoked refresh token:
   - Try to refresh with revoked token
   - Verify request is rejected
5. Test TTL functionality:
   - Check document expiry time in collection
   - Verify TTL is set to appropriate duration
   - Simulate TTL expiry (if possible in test environment)
6. Test token validation against revoked collection:
   - Use valid non-revoked token (should work)
   - Use revoked token (should fail)
   - Verify check happens before token refresh
7. Test collection performance:
   - Add multiple revoked tokens
   - Verify lookup performance remains acceptable
   - Test cleanup of expired documents

### Expected Results
- Token revocation: Document created in revoked_tokens collection
- Revoked collection document structure:
  ```json
  {
    "tokenId": "hash_of_refresh_token",
    "userId": "user123",
    "revokedAt": "2025-01-01T10:00:00Z",
    "expiresAt": "2025-01-08T10:00:00Z"
  }
  ```
- Revoked token usage: 401 Unauthorized
  ```json
  {
    "error": "Token has been revoked",
    "code": "TOKEN_REVOKED"
  }
  ```
- TTL properly configured (e.g., 7 days after revocation)
- Expired documents automatically removed from collection
- Token validation checks revoked collection efficiently
- Collection lookup adds < 20ms to validation time

### Post-conditions
- Revoked tokens cannot be reused for authentication
- Collection maintains security without growing indefinitely
- System performance remains acceptable with TTL cleanup

### Test Data
- **Test User**: <EMAIL>
- **TTL Duration**: 7 days (configurable)
- **Collection Name**: revoked_tokens
- **Document Fields**: tokenId, userId, revokedAt, expiresAt