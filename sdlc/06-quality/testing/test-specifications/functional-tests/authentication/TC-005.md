# Test Case TC-005: Default User Role Assignment

## Test Information
- **Test ID**: TC-005
- **Objective**: Verify that new users are created with default role "User" and cannot escalate privileges during registration
- **Requirements**: FR-009
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Firestore database is accessible
- Role-based access control system is active

### Test Steps
1. Register user without specifying role:
   ```json
   {
     "email": "<EMAIL>",
     "password": "Password123!",
     "displayName": "New User"
   }
   ```
2. Verify user document in Firestore has role "User"
3. Attempt privilege escalation during registration:
   ```json
   {
     "email": "<EMAIL>",
     "password": "Password123!",
     "displayName": "Hacker",
     "role": "admin"
   }
   ```
4. Attempt registration with invalid roles:
   ```json
   {
     "email": "<EMAIL>",
     "password": "Password123!",
     "displayName": "Invalid User",
     "role": "superuser"
   }
   ```
5. Verify Firebase custom claims are set correctly:
   - Check ID token contains role claim
   - Verify role claim value is "User"
6. Test role-based endpoint access:
   - Attempt to access admin-only endpoint
   - Verify access is denied

### Expected Results
- Normal registration: User created with role "User"
- Role field in registration payload: Ignored, role still set to "User"
- Invalid role attempts: Role ignored, default "User" assigned
- Firebase custom claims: Include role "User"
- Database consistency: User document role matches Firebase claims
- Access control: New users cannot access privileged endpoints
- Security: No privilege escalation possible during registration
- Error logging: Privilege escalation attempts are logged

### Post-conditions
- All new users have role "User" regardless of input
- Firebase authentication tokens reflect correct role
- System security boundaries are maintained
- Audit trail exists for privilege escalation attempts

### Test Data
- **Normal Registration**: <EMAIL>, Password123!
- **Privilege Escalation**: <EMAIL>, role: "admin"
- **Invalid Role**: <EMAIL>, role: "superuser"