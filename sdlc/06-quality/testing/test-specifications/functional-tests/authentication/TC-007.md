# Test Case TC-007: Firebase ID Token Issuance and Expiry

## Test Information
- **Test ID**: TC-007
- **Objective**: Verify that Firebase ID tokens are issued with correct 1-hour expiry upon successful authentication
- **Requirements**: FR-011
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Firebase Authentication is configured
- Test user exists with valid credentials
- Token validation service is operational

### Test Steps
1. Authenticate user and capture token:
   ```json
   {
     "email": "<EMAIL>",
     "password": "TestPass123!"
   }
   ```
2. Decode JWT token and verify structure:
   - Header contains correct algorithm (RS256)
   - Payload contains required claims
   - Signature is valid
3. Verify token expiry claim:
   - `exp` claim is present
   - Expiry time is exactly 1 hour (3600 seconds) from `iat`
   - Current time is before expiry time
4. Verify token claims contain:
   - `iss` (issuer): Firebase project ID
   - `aud` (audience): Firebase project ID
   - `sub` (subject): User ID
   - `email`: User email address
   - `role`: User role custom claim
5. Test token usage over time:
   - Use token immediately after issue (should work)
   - Use token at 30 minutes (should work)
   - Use token at 59 minutes (should work)
   - Use token at 61 minutes (should fail)
6. Verify token validation on protected endpoints:
   - Valid token within expiry: Access granted
   - Expired token: Access denied with 401 status

### Expected Results
- Token issued with proper JWT structure
- Expiry (`exp`) set to `iat + 3600` (1 hour)
- All required claims present and correct
- Token works for authentication within 1-hour window
- Token automatically expires after 1 hour
- Expired token rejection with appropriate error:
  ```json
  {
    "error": "Token expired",
    "code": "AUTH_TOKEN_EXPIRED"
  }
  ```
- Token validation respects time boundaries precisely

### Post-conditions
- Token lifecycle management works correctly
- Security boundary enforced at 1-hour mark
- System prompts for re-authentication after expiry

### Test Data
- **Test User**: <EMAIL>
- **Token Lifetime**: 3600 seconds (1 hour)
- **Validation Times**: 0min, 30min, 59min, 61min