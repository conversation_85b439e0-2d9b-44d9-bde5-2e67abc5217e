# Test Case TC-009: User Profile Data Return with Authentication

## Test Information
- **Test ID**: TC-009
- **Objective**: Verify that user profile data is returned along with authentication tokens upon successful login
- **Requirements**: FR-013
- **Priority**: Medium
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Test user exists with complete profile:
  - Email: `<EMAIL>`
  - Display Name: `Profile Test User`
  - Role: `User`
  - Profile Image: `https://example.com/avatar.jpg`
  - Created timestamp
  - Updated timestamp

### Test Steps
1. Authenticate user with valid credentials:
   ```json
   {
     "email": "<EMAIL>",
     "password": "TestPass123!"
   }
   ```
2. Verify authentication response structure contains:
   - Authentication token (Firebase ID token)
   - Complete user profile data
3. Validate user profile fields in response:
   - User ID (should match Firestore document ID)
   - Email address (should match login email)
   - Display name (should be complete)
   - Role (should be current role)
   - Profile image URL (if set)
   - Account timestamps (createdAt, updatedAt)
4. Verify sensitive data is excluded:
   - Password hash is not included
   - Internal system fields are not exposed
5. Test with user having minimal profile:
   - User with only email and password
   - Verify optional fields are null/empty
6. Verify profile data freshness:
   - Update user profile
   - Login again
   - Verify updated data is returned

### Expected Results
- Successful login returns 200 OK with complete response:
  ```json
  {
    "token": "eyJhbGciOiJSUzI1NiIs...",
    "user": {
      "id": "user123",
      "email": "<EMAIL>",
      "displayName": "Profile Test User",
      "role": "User",
      "profileImage": "https://example.com/avatar.jpg",
      "createdAt": "2025-01-01T10:00:00Z",
      "updatedAt": "2025-01-02T15:30:00Z"
    }
  }
  ```
- All expected profile fields are present
- Sensitive data (password, internal fields) excluded
- Optional fields show null/empty for minimal profiles
- Profile data reflects current state (not cached stale data)
- Response time < 500ms including profile data retrieval

### Post-conditions
- Client receives complete profile information for user session
- Profile data is current and accurate
- Security boundaries maintained (no sensitive data exposed)

### Test Data
- **Test User**: <EMAIL>
- **Complete Profile**: Display name, profile image, timestamps
- **Minimal Profile**: Email and password only
- **Updated Profile**: Modified display name or image