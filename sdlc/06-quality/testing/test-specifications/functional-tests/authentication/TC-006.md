# Test Case TC-006: User Authentication with Email and Password

## Test Information
- **Test ID**: TC-006
- **Objective**: Verify that users can successfully authenticate using email and password credentials
- **Requirements**: FR-010, FR-011, FR-013
- **Priority**: Critical
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Firebase Authentication is configured
- Test user exists with valid credentials:
  - Email: `<EMAIL>`
  - Password: `TestPass123!`

### Test Steps
1. Send valid login request:
   ```json
   {
     "email": "<EMAIL>",
     "password": "TestPass123!"
   }
   ```
2. Verify successful authentication response
3. Validate Firebase ID token in response:
   - Token is valid JWT format
   - Token expiry is 1 hour from issue time
   - Token contains correct user claims
4. Verify user profile data is returned:
   - User ID matches registered user
   - Email address is correct
   - Display name is included
   - Role information is present
5. Test case sensitivity:
   - Login with uppercase email: `<EMAIL>`
   - Login with mixed case email: `<EMAIL>`
6. Verify token can be used for authenticated requests:
   - Make request to protected endpoint with token
   - Verify access is granted

### Expected Results
- Valid credentials: 200 OK response
- Response includes valid Firebase ID token
- Token expiry set to 1 hour (3600 seconds) from issue
- User profile data returned in response:
  ```json
  {
    "token": "eyJhbGciOiJSUzI1NiIs...",
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "displayName": "Test User",
      "role": "User"
    }
  }
  ```
- Case-insensitive email matching works correctly
- Token validates successfully for protected endpoints
- Response time < 500ms for login requests

### Post-conditions
- User session is established with valid token
- Token can be used for subsequent authenticated requests
- User login activity is logged for security monitoring

### Test Data
- **Valid Email**: <EMAIL>
- **Valid Password**: TestPass123!
- **Case Variations**: <EMAIL>, <EMAIL>