# Test Case TC-001: User Registration with Valid Email and Password

## Test Information
- **Test ID**: TC-001
- **Objective**: Verify that users can successfully register with valid email and password
- **Requirements**: FR-001, FR-002, FR-003, FR-008, FR-009
- **Priority**: Critical
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service is available
- Firestore database is accessible
- Email validation service is operational
- No existing user with test email

### Test Steps
1. Send POST request to `/api/v1/auth/register` with payload:
   ```json
   {
     "email": "<EMAIL>",
     "password": "TestPass123!",
     "displayName": "Test User"
   }
   ```
2. Verify API response status is 201 Created
3. Verify response includes user ID and Firebase token
4. Verify user is created in Firestore with correct data structure
5. Verify user role is set to "User" by default
6. Verify timestamps (createdAt, updatedAt) are populated
7. Verify email uniqueness by attempting to register same email again

### Expected Results
- Registration request succeeds with 201 status
- Response contains valid Firebase ID token with 1-hour expiry
- User document created in Firestore users collection
- User role set to "User" by default
- Timestamps populated with current UTC time
- Duplicate email registration returns 409 Conflict error
- Password is hashed and not stored in plaintext

### Post-conditions
- New user exists in authentication system
- User can authenticate with provided credentials
- Email is reserved and cannot be reused

### Test Data
- **Valid Email**: <EMAIL>
- **Valid Password**: TestPass123!
- **Display Name**: Test User