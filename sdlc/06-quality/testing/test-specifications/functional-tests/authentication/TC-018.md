# Test Case TC-018: Firebase Custom Claims Integration

## Test Information
- **Test ID**: TC-018
- **Objective**: Verify that Firebase custom claims are properly set and updated for user roles and permissions
- **Requirements**: FR-009, FR-028, FR-029
- **Priority**: High
- **Test Type**: Integration
- **Automation**: Yes

## Test Details
### Preconditions
- API Gateway service is running
- Authentication service with Firebase Admin SDK configured
- Custom claims management is implemented
- Test users exist for role testing

### Test Steps
1. Test default custom claims on registration:
   - Register new user
   - Decode Firebase ID token
   - Verify custom claims contain default role "User"
2. Test role assignment to existing user:
   - Assign "agent" role to test user
   - Request new token or wait for token refresh
   - Verify custom claims updated within 5 seconds
3. Test custom claims in token payload:
   - Authenticate user with specific role
   - Decode JWT token payload
   - Verify custom claims structure:
     ```json
     {
       "role": "agent",
       "permissions": ["create_agent_profile", "manage_tasks"],
       "custom_claims_updated_at": "2025-01-01T10:00:00Z"
     }
     ```
4. Test custom claims propagation:
   - Update user role in system
   - Force token refresh
   - Verify new token reflects updated role
   - Test that old tokens still work until expiry
5. Test role-based endpoint access:
   - Use token with "User" role
   - Attempt to access agent-only endpoint
   - Verify access denied
   - Switch to "agent" role token
   - Verify access granted
6. Test custom claims validation:
   - Attempt to manually set custom claims in request
   - Verify claims are server-controlled only
   - Test token tampering detection

### Expected Results
- New user registration: Default custom claims with role "User"
- Role assignment: Claims updated within 5 seconds (FR-029)
- Token structure includes custom claims:
  ```json
  {
    "iss": "https://securetoken.google.com/project-id",
    "sub": "user123",
    "role": "agent",
    "permissions": ["create_agent_profile"],
    "custom_claims_updated_at": "2025-01-01T10:00:00Z"
  }
  ```
- Role updates: New tokens reflect changes immediately
- Old tokens: Continue working until natural expiry
- Access control: Enforced based on custom claims
- Security: Claims cannot be manipulated by clients
- Token validation: Fails for tampered custom claims
- Performance: Claims update completes in < 5 seconds

### Post-conditions
- User roles are properly reflected in authentication tokens
- Role-based access control works correctly
- Custom claims maintain security and integrity
- Token lifecycle management handles role changes

### Test Data
- **Default Role**: User
- **Test Roles**: agent, admin, moderator
- **Permissions**: create_agent_profile, manage_tasks, admin_access
- **Update Timeout**: 5 seconds maximum