# Test Case TC-300: Complete User Journey - Registration to Task Completion

## Test Information
- **Test ID**: TC-300
- **Integration Type**: End-to-End User Workflow
- **Objective**: Verify complete user journey from registration through task completion and payment
- **Systems Under Test**: Authentication, User Management, Task Management, Matching Engine, Credit System, Payment Processing
- **Priority**: Critical
- **Test Type**: Integration - End-to-End
- **Automation**: Yes

## Integration Scenario
### Complete User Journey Workflow
1. **User Registration**: Create new user account with email verification
2. **Profile Setup**: Complete user profile with preferences and payment method
3. **Credit Purchase**: Buy credits for task creation
4. **Task Creation**: Create a task with specific requirements
5. **Agent Discovery**: Browse and filter available agents
6. **Agent Selection**: Choose agent and initiate matching
7. **Task Assignment**: Confirm match and assign task to agent
8. **Task Execution**: Monitor task progress and communication
9. **Task Completion**: Receive deliverables and approve completion
10. **Payment Processing**: Credit transfer to agent with platform commission
11. **Rating and Review**: Provide feedback on agent performance
12. **Task Archive**: Complete workflow and archive task

### Data Flow Validation
- User data consistency across all services
- Task state synchronization between services
- Credit balance accuracy throughout workflow
- Audit trail completeness for all transactions
- Communication log integrity

## Test Environment
### Service Dependencies
- **Authentication Service**: User registration and login
- **User Management Service**: Profile and preferences
- **Task Management Service**: Task lifecycle management
- **Matching Engine**: Agent discovery and selection
- **Credit System**: Balance management and transactions
- **Payment Service**: External payment processing (Stripe)
- **Notification Service**: Email and system notifications
- **Audit Service**: Activity and transaction logging

### External Dependencies
- **Google Identity Platform**: Authentication provider
- **Firestore**: Data persistence layer
- **Stripe**: Payment processing
- **Email Service**: Notification delivery
- **Cloud Storage**: File attachments

## Test Data Requirements
### User Profiles
- **New User**: <EMAIL>
- **Valid Payment Method**: Test credit card (Stripe test mode)
- **User Preferences**: Content creation, intermediate skill level
- **Initial Credits**: 0 (will purchase during test)

### Agent Pool
- **Available Agents**: 10+ agents in "Content Creator" category
- **Skill Levels**: Mix of beginner, intermediate, expert
- **Availability**: Various availability windows
- **Ratings**: Range from 3.5 to 5.0 stars

### Task Requirements
- **Task Type**: Blog post creation
- **Estimated Duration**: 2-3 hours
- **Budget Range**: $50-100
- **Deadline**: 7 days from creation
- **Attachments**: Brand guidelines document

## Execution Steps
### 1. User Registration and Setup
```javascript
// User registration
let registrationData = {
  email: `testuser+e2e+${Date.now()}@example.com`,
  password: 'SecurePass123!',
  displayName: 'E2E Test User'
};

let registerResponse = http.post(
  `${baseUrl}/api/v1/auth/register`,
  JSON.stringify(registrationData),
  { headers: { 'Content-Type': 'application/json' } }
);

check(registerResponse, {
  'registration successful': (r) => r.status === 201,
  'auth token received': (r) => JSON.parse(r.body).token !== undefined,
});

let authToken = JSON.parse(registerResponse.body).token;
let userId = JSON.parse(registerResponse.body).userId;
```

### 2. Profile Completion
```javascript
// Complete user profile
let profileData = {
  bio: 'Small business owner looking for content creation help',
  location: 'San Francisco, CA',
  preferences: {
    categories: ['Content Creator'],
    skillLevel: 'Intermediate',
    communicationStyle: 'Professional'
  }
};

let profileResponse = http.put(
  `${baseUrl}/api/v1/users/${userId}`,
  JSON.stringify(profileData),
  { headers: { 'Authorization': `Bearer ${authToken}`, 'Content-Type': 'application/json' } }
);

check(profileResponse, {
  'profile updated successfully': (r) => r.status === 200,
});
```

### 3. Credit Purchase
```javascript
// Purchase credits
let creditPurchase = {
  amount: 100, // $100 in credits
  paymentMethodId: 'pm_card_visa', // Stripe test card
};

let purchaseResponse = http.post(
  `${baseUrl}/api/v1/credits/purchase`,
  JSON.stringify(creditPurchase),
  { headers: { 'Authorization': `Bearer ${authToken}`, 'Content-Type': 'application/json' } }
);

check(purchaseResponse, {
  'credit purchase successful': (r) => r.status === 200,
  'transaction ID received': (r) => JSON.parse(r.body).transactionId !== undefined,
});
```

### 4. Task Creation
```javascript
// Create task
let taskData = {
  title: 'Blog Post: 10 Tips for Small Business Marketing',
  description: 'Need a 1500-word blog post about marketing strategies for small businesses',
  category: 'Content Creator',
  skillLevel: 'Intermediate',
  estimatedDuration: 180, // 3 hours
  maxBudget: 75,
  deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
  requirements: [
    'SEO-optimized content',
    'Engaging and actionable tips',
    'Professional tone'
  ]
};

let taskResponse = http.post(
  `${baseUrl}/api/v1/tasks`,
  JSON.stringify(taskData),
  { headers: { 'Authorization': `Bearer ${authToken}`, 'Content-Type': 'application/json' } }
);

check(taskResponse, {
  'task created successfully': (r) => r.status === 201,
  'task ID received': (r) => JSON.parse(r.body).taskId !== undefined,
});

let taskId = JSON.parse(taskResponse.body).taskId;
```

### 5. Agent Discovery and Selection
```javascript
// Find matching agents
let matchingRequest = {
  taskId: taskId,
  requirements: {
    category: 'Content Creator',
    skillLevel: 'Intermediate',
    maxBudget: 75
  }
};

let matchingResponse = http.post(
  `${baseUrl}/api/v1/matching/find-agents`,
  JSON.stringify(matchingRequest),
  { headers: { 'Authorization': `Bearer ${authToken}`, 'Content-Type': 'application/json' } }
);

check(matchingResponse, {
  'matching successful': (r) => r.status === 200,
  'agents recommended': (r) => JSON.parse(r.body).agents.length > 0,
});

let recommendedAgents = JSON.parse(matchingResponse.body).agents;
let selectedAgent = recommendedAgents[0]; // Select top recommendation
```

### 6. Task Assignment
```javascript
// Assign task to selected agent
let assignmentData = {
  taskId: taskId,
  agentId: selectedAgent.id,
  agreedBudget: selectedAgent.proposedRate
};

let assignmentResponse = http.post(
  `${baseUrl}/api/v1/tasks/${taskId}/assign`,
  JSON.stringify(assignmentData),
  { headers: { 'Authorization': `Bearer ${authToken}`, 'Content-Type': 'application/json' } }
);

check(assignmentResponse, {
  'task assignment successful': (r) => r.status === 200,
  'escrow created': (r) => JSON.parse(r.body).escrowId !== undefined,
});
```

## Success Criteria
### Functional Requirements
- **Registration**: User account created with valid authentication
- **Profile**: User profile completed and saved
- **Credits**: Successfully purchased and balance updated
- **Task Creation**: Task created with all requirements
- **Matching**: Appropriate agents recommended
- **Assignment**: Task assigned to agent with escrow
- **Communication**: Notification sent to agent
- **Audit Trail**: All actions logged with timestamps

### Data Consistency
- **User Data**: Consistent across all services
- **Task State**: Properly synchronized between services
- **Credit Balance**: Accurate after purchase and escrow
- **Agent Availability**: Updated after task assignment
- **Audit Logs**: Complete record of all transactions

### Performance Requirements
- **End-to-End Time**: Complete workflow <5 minutes
- **Individual Operations**: Each step <2 seconds
- **Data Propagation**: Cross-service updates <1 second
- **Notification Delivery**: Email notifications <30 seconds

## Post-conditions
### System State Validation
- User account active and fully configured
- Task in "ASSIGNED" status
- Agent task queue updated
- Credit balance reflects purchase and escrow
- Audit trail complete and accessible
- Notifications delivered to all parties

### Data Integrity Checks
- User profile data consistent across services
- Task data synchronized between task and matching services
- Financial data accurate in credit and payment services
- Communication logs properly maintained
- File attachments successfully stored and accessible

## Error Handling Scenarios
### Service Failure Scenarios
- Authentication service unavailable during registration
- Payment service failure during credit purchase
- Matching engine failure during agent discovery
- Task service failure during assignment
- Notification service failure during communication

### Data Validation Scenarios
- Invalid user registration data
- Insufficient credits for task creation
- No available agents matching criteria
- Agent unavailable during assignment
- Payment method declined

### Recovery Validation
- Partial workflow completion handling
- Transaction rollback on failure
- User notification of failures
- Cleanup of incomplete transactions
- System state consistency after failures

## Monitoring and Observability
### Key Metrics
- **Workflow Completion Rate**: % of users completing full journey
- **Abandonment Points**: Where users drop off most frequently
- **Service Response Times**: Performance of each step
- **Error Rates**: Failure points in the workflow
- **Data Consistency**: Cross-service data integrity

### Tracing and Logging
- **Distributed Tracing**: End-to-end request tracking
- **Correlation IDs**: Track requests across services
- **Business Events**: Key workflow milestone logging
- **Error Logging**: Detailed failure information
- **Performance Metrics**: Timing for each workflow step