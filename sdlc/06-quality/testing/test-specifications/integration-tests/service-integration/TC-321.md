# Test Case TC-321: Internal Service Communication and Data Flow

## Test Information
- **Test ID**: TC-321
- **Integration Type**: Service-to-Service Integration
- **Objective**: Verify seamless communication and data flow between internal microservices
- **Systems Under Test**: API Gateway, Authentication Service, User Service, Task Service, Agent Service, Credit Service
- **Priority**: Critical
- **Test Type**: Integration - Service Communication
- **Automation**: Yes

## Integration Scenario
### Service Communication Flow
1. **API Gateway Routing**: Route requests to appropriate services
2. **Authentication Propagation**: JWT token validation across services
3. **Service Discovery**: Services locate and communicate with each other
4. **Data Synchronization**: User profile updates propagate to dependent services
5. **Event Publishing**: Services publish events for cross-service notifications
6. **Event Consumption**: Services react to relevant business events
7. **Circuit Breaker Testing**: Graceful degradation when services are unavailable
8. **Request Tracing**: Distributed tracing across service boundaries

### Data Flow Patterns
- **User Profile Updates**: User Service → Task Service → Agent Service
- **Task Creation**: Task Service → Matching Service → Agent Service
- **Credit Transactions**: Credit Service → Audit Service → Notification Service
- **Authentication Events**: Auth Service → User Service → Audit Service

## Test Environment
### Service Architecture
```yaml
services:
  api-gateway:
    port: 8080
    routes: ["/api/v1/*"]
    
  auth-service:
    port: 8081
    endpoints: ["/auth/*"]
    
  user-service:
    port: 8082
    endpoints: ["/users/*"]
    
  task-service:
    port: 8083
    endpoints: ["/tasks/*"]
    
  agent-service:
    port: 8084
    endpoints: ["/agents/*"]
    
  credit-service:
    port: 8085
    endpoints: ["/credits/*"]
```

### Communication Protocols
- **HTTP/REST**: Synchronous service communication
- **Google Pub/Sub**: Asynchronous event messaging
- **gRPC**: High-performance internal communication (if implemented)
- **Service Mesh**: Traffic management and security (if implemented)

## Execution Steps
### 1. Service Discovery and Health Validation
```javascript
// Test service discovery and health endpoints
let services = [
  { name: 'auth-service', port: 8081, health: '/health' },
  { name: 'user-service', port: 8082, health: '/health' },
  { name: 'task-service', port: 8083, health: '/health' },
  { name: 'agent-service', port: 8084, health: '/health' },
  { name: 'credit-service', port: 8085, health: '/health' }
];

services.forEach(service => {
  let healthResponse = http.get(`http://${service.name}:${service.port}${service.health}`);
  check(healthResponse, {
    [`${service.name} is healthy`]: (r) => r.status === 200,
    [`${service.name} responds quickly`]: (r) => r.timings.duration < 100,
  });
});
```

### 2. Authentication Token Propagation
```javascript
// Test JWT token propagation across services
let authResponse = http.post(
  'http://api-gateway:8080/api/v1/auth/login',
  JSON.stringify({
    email: '<EMAIL>',
    password: 'TestPass123!'
  }),
  { headers: { 'Content-Type': 'application/json' } }
);

check(authResponse, {
  'authentication successful': (r) => r.status === 200,
  'JWT token received': (r) => JSON.parse(r.body).token !== undefined,
});

let jwtToken = JSON.parse(authResponse.body).token;
let authHeaders = { 
  'Authorization': `Bearer ${jwtToken}`,
  'Content-Type': 'application/json'
};

// Test token validation across services
let userProfileResponse = http.get(
  'http://api-gateway:8080/api/v1/users/profile',
  { headers: authHeaders }
);

check(userProfileResponse, {
  'user service accepts token': (r) => r.status === 200,
  'user data returned': (r) => JSON.parse(r.body).userId !== undefined,
});
```

### 3. Cross-Service Data Synchronization
```javascript
// Test user profile update propagation
let profileUpdate = {
  displayName: 'Updated Integration Test User',
  bio: 'Testing cross-service data synchronization',
  preferences: {
    categories: ['Content Creator', 'Data Analyst'],
    notifications: true
  }
};

let updateResponse = http.put(
  'http://api-gateway:8080/api/v1/users/profile',
  JSON.stringify(profileUpdate),
  { headers: authHeaders }
);

check(updateResponse, {
  'profile update successful': (r) => r.status === 200,
});

// Wait for propagation
sleep(2);

// Verify update propagated to task service
let taskServiceResponse = http.get(
  'http://task-service:8083/internal/user-profile/integration-test-user-id',
  { headers: { 'X-Internal-Request': 'true' } }
);

check(taskServiceResponse, {
  'profile synced to task service': (r) => r.status === 200,
  'updated data available': (r) => JSON.parse(r.body).displayName === 'Updated Integration Test User',
});
```

### 4. Event Publishing and Consumption
```javascript
// Test event-driven communication
let taskCreation = {
  title: 'Integration Test Task',
  description: 'Testing event-driven service communication',
  category: 'Content Creator',
  estimatedDuration: 60,
  maxBudget: 50
};

let taskResponse = http.post(
  'http://api-gateway:8080/api/v1/tasks',
  JSON.stringify(taskCreation),
  { headers: authHeaders }
);

check(taskResponse, {
  'task creation successful': (r) => r.status === 201,
  'task ID returned': (r) => JSON.parse(r.body).taskId !== undefined,
});

let taskId = JSON.parse(taskResponse.body).taskId;

// Wait for event processing
sleep(3);

// Verify matching service received task creation event
let matchingResponse = http.get(
  `http://matching-service:8086/internal/task-status/${taskId}`,
  { headers: { 'X-Internal-Request': 'true' } }
);

check(matchingResponse, {
  'matching service aware of task': (r) => r.status === 200,
  'task ready for matching': (r) => JSON.parse(r.body).status === 'READY_FOR_MATCHING',
});
```

### 5. Circuit Breaker and Fallback Testing
```javascript
// Test circuit breaker behavior
// Simulate agent service failure
let agentServiceDown = true; // This would be actual service shutdown in real test

// Attempt to get agent recommendations with fallback
let fallbackResponse = http.get(
  'http://api-gateway:8080/api/v1/tasks/123/agent-recommendations',
  { headers: authHeaders }
);

check(fallbackResponse, {
  'circuit breaker activated': (r) => r.status === 503 || r.status === 200,
  'fallback response provided': (r) => {
    if (r.status === 200) {
      let body = JSON.parse(r.body);
      return body.source === 'fallback' || body.cached === true;
    }
    return true; // 503 is acceptable when service is down
  },
});
```

### 6. Distributed Tracing Validation
```javascript
// Test request tracing across services
let traceId = `trace-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
let tracingHeaders = {
  ...authHeaders,
  'X-Trace-ID': traceId,
  'X-Request-ID': `req-${Date.now()}`
};

// Make request that spans multiple services
let complexOperation = http.post(
  'http://api-gateway:8080/api/v1/matching/find-agent',
  JSON.stringify({
    taskId: taskId,
    requirements: {
      category: 'Content Creator',
      skillLevel: 'Intermediate'
    }
  }),
  { headers: tracingHeaders }
);

check(complexOperation, {
  'complex operation successful': (r) => r.status === 200,
  'trace ID propagated': (r) => r.headers['X-Trace-ID'] === traceId,
});

// Verify trace spans were created
sleep(1);
let traceQuery = http.get(
  `http://tracing-service:8087/traces/${traceId}`,
  { headers: { 'X-Internal-Request': 'true' } }
);

check(traceQuery, {
  'trace recorded': (r) => r.status === 200,
  'multiple spans present': (r) => JSON.parse(r.body).spans.length >= 3,
  'service boundaries tracked': (r) => {
    let spans = JSON.parse(r.body).spans;
    let services = spans.map(span => span.serviceName);
    return services.includes('api-gateway') && 
           services.includes('task-service') && 
           services.includes('matching-service');
  },
});
```

## Success Criteria
### Communication Requirements
- **Service Discovery**: All services discoverable and responsive
- **Authentication**: JWT tokens validated consistently across services
- **Request Routing**: API Gateway correctly routes to target services
- **Response Times**: Inter-service calls complete within 50ms
- **Error Handling**: Graceful degradation when services are unavailable

### Data Consistency
- **Profile Synchronization**: User updates propagate within 2 seconds
- **Event Processing**: Business events processed within 5 seconds
- **State Consistency**: Data consistent across service boundaries
- **Transaction Integrity**: Distributed transactions maintain ACID properties

### Observability
- **Distributed Tracing**: Complete request traces across services
- **Logging Correlation**: Log entries correlated with trace IDs
- **Metrics Collection**: Service metrics properly aggregated
- **Health Monitoring**: Service health accurately reported

## Service Communication Patterns
### Synchronous Communication
```javascript
// Test request-response pattern
let syncRequest = {
  method: 'POST',
  url: '/api/v1/internal/validate-user',
  payload: { userId: 'test-user-123' },
  timeout: 5000,
  retries: 3
};

// Expected: HTTP 200 with validation result
```

### Asynchronous Messaging
```javascript
// Test event publishing
let eventPayload = {
  eventType: 'USER_PROFILE_UPDATED',
  userId: 'test-user-123',
  timestamp: new Date().toISOString(),
  changes: ['displayName', 'preferences']
};

// Expected: Event published to Pub/Sub topic
// Expected: Consuming services receive event within 5 seconds
```

### Circuit Breaker Patterns
```javascript
// Test circuit breaker states
let circuitBreakerTests = [
  { serviceName: 'agent-service', expectedState: 'CLOSED' },
  { serviceName: 'matching-service', expectedState: 'CLOSED' },
  { serviceName: 'credit-service', expectedState: 'CLOSED' }
];

// Expected: Circuit breakers in CLOSED state (normal operation)
// Expected: OPEN state when service failures exceed threshold
// Expected: HALF_OPEN state during recovery testing
```

## Error Scenarios and Recovery
### Service Unavailability
- Individual service shutdown
- Network partition between services
- Database connection failures
- External service dependencies failing

### Data Inconsistency
- Concurrent update conflicts
- Event processing failures
- Partial transaction failures
- Message delivery failures

### Performance Degradation
- Service response time increases
- Database query timeouts
- Memory pressure in services
- Network latency spikes

## Monitoring and Alerting
### Service Health Metrics
- **Response Time**: 95th percentile inter-service call latency
- **Error Rate**: Percentage of failed service-to-service calls
- **Throughput**: Requests per second between services
- **Availability**: Service uptime and reachability

### Communication Quality
- **Message Delivery**: Event delivery success rate
- **Data Consistency**: Cross-service data synchronization timing
- **Circuit Breaker State**: Service protection mechanism status
- **Trace Completeness**: Percentage of requests with complete traces

### Business Impact
- **Feature Availability**: End-user feature availability during service issues
- **Data Accuracy**: Consistency of user-facing data across services
- **Performance Impact**: User experience during service communication issues