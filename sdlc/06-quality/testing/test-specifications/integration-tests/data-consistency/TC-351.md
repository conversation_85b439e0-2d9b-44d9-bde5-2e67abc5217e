# Test Case TC-351: Cross-Service Data Consistency and Integrity

## Test Information
- **Test ID**: TC-351
- **Integration Type**: Data Consistency Validation
- **Objective**: Verify data consistency and integrity across all microservices and storage systems
- **Systems Under Test**: User Service, Task Service, Agent Service, Credit Service, Audit Service, Firestore Database
- **Priority**: Critical
- **Test Type**: Integration - Data Consistency
- **Automation**: Yes

## Integration Scenario
### Data Consistency Validation Flow
1. **Initial Data State**: Establish baseline data across all services
2. **Concurrent Updates**: Perform simultaneous updates from multiple services
3. **Transaction Verification**: Validate ACID properties of distributed transactions
4. **Event Propagation**: Verify data changes propagate correctly via events
5. **Read Consistency**: Validate eventual consistency across read replicas
6. **Conflict Resolution**: Test handling of conflicting concurrent updates
7. **Rollback Scenarios**: Verify data rollback on transaction failures
8. **Audit Trail Verification**: Confirm complete audit logging of data changes

### Critical Data Relationships
- **User Profile ↔ Task Ownership**: User data consistency with task assignments
- **Agent Profile ↔ Task Assignments**: Agent availability and task allocations
- **Credit Balance ↔ Transactions**: Credit system financial integrity
- **Task Status ↔ Orchestration State**: Task progress and orchestration coordination
- **Rating Data ↔ Agent Profiles**: Review scores and agent reputation

## Test Environment
### Data Sources
- **Primary Database**: Firestore main collections
- **Service Caches**: Redis/in-memory caches per service
- **Event Store**: Pub/Sub message history
- **Audit Database**: Transaction and change logs
- **Search Indexes**: Derived data for agent/task discovery

### Test Data Setup
```javascript
// Consistent test user across services
let testUser = {
  userId: 'consistency-test-user-001',
  email: '<EMAIL>',
  displayName: 'Data Consistency Test User',
  credits: 100.00,
  createdAt: '2024-01-01T00:00:00Z'
};

// Related test data
let testAgent = {
  agentId: 'consistency-test-agent-001',
  userId: 'consistency-test-user-001',
  category: 'Content Creator',
  rating: 4.5,
  availability: true
};

let testTask = {
  taskId: 'consistency-test-task-001',
  userId: 'consistency-test-user-001',
  assignedAgentId: 'consistency-test-agent-001',
  status: 'IN_PROGRESS',
  budget: 50.00
};
```

## Execution Steps
### 1. Initial Data Consistency Verification
```javascript
// Verify initial data state across all services
let consistencyChecks = [];

// Check user data consistency
let userServiceData = http.get(`${baseUrl}/api/v1/users/consistency-test-user-001`);
let taskServiceUserData = http.get(`${baseUrl}/api/v1/internal/tasks/user/consistency-test-user-001`);
let creditServiceUserData = http.get(`${baseUrl}/api/v1/internal/credits/user/consistency-test-user-001`);

check(userServiceData, {
  'user service has user data': (r) => r.status === 200,
  'user profile complete': (r) => JSON.parse(r.body).displayName === 'Data Consistency Test User',
});

check(taskServiceUserData, {
  'task service has user reference': (r) => r.status === 200,
  'user reference consistent': (r) => JSON.parse(r.body).userId === 'consistency-test-user-001',
});

check(creditServiceUserData, {
  'credit service has user balance': (r) => r.status === 200,
  'credit balance accurate': (r) => JSON.parse(r.body).balance === 100.00,
});
```

### 2. Concurrent Update Testing
```javascript
// Test concurrent updates to user profile
let concurrentUpdates = [];

// Update 1: Change display name via User Service
concurrentUpdates.push(
  http.asyncRequest('PUT', `${baseUrl}/api/v1/users/consistency-test-user-001`, {
    headers: authHeaders,
    body: JSON.stringify({
      displayName: 'Updated Name from User Service',
      bio: 'Updated bio text'
    })
  })
);

// Update 2: Change preferences via User Service (different field)
concurrentUpdates.push(
  http.asyncRequest('PUT', `${baseUrl}/api/v1/users/consistency-test-user-001/preferences`, {
    headers: authHeaders,
    body: JSON.stringify({
      categories: ['Content Creator', 'Data Analyst'],
      notifications: false
    })
  })
);

// Update 3: Credit transaction via Credit Service
concurrentUpdates.push(
  http.asyncRequest('POST', `${baseUrl}/api/v1/credits/transaction`, {
    headers: authHeaders,
    body: JSON.stringify({
      userId: 'consistency-test-user-001',
      amount: -25.00,
      type: 'TASK_PAYMENT',
      description: 'Test concurrent transaction'
    })
  })
);

// Wait for all updates to complete
let results = http.batch(concurrentUpdates);

// Verify all updates succeeded
results.forEach((result, index) => {
  check(result, {
    [`concurrent update ${index + 1} successful`]: (r) => [200, 201].includes(r.status),
  });
});
```

### 3. Transaction Integrity Validation
```javascript
// Test distributed transaction integrity
let transactionTest = {
  taskId: 'consistency-test-task-001',
  agentId: 'consistency-test-agent-001',
  amount: 50.00,
  escrowTransaction: true
};

// Start complex transaction involving multiple services
let transactionResponse = http.post(
  `${baseUrl}/api/v1/tasks/consistency-test-task-001/complete`,
  JSON.stringify({
    deliverables: ['test-document.pdf'],
    clientApproval: true,
    agentId: 'consistency-test-agent-001'
  }),
  { headers: authHeaders }
);

check(transactionResponse, {
  'transaction completed successfully': (r) => r.status === 200,
  'transaction ID returned': (r) => JSON.parse(r.body).transactionId !== undefined,
});

// Verify data consistency after transaction
sleep(3); // Allow for event processing

// Check task status updated
let taskStatus = http.get(`${baseUrl}/api/v1/tasks/consistency-test-task-001`);
check(taskStatus, {
  'task status updated': (r) => JSON.parse(r.body).status === 'COMPLETED',
});

// Check credit balance updated
let creditBalance = http.get(`${baseUrl}/api/v1/credits/balance`, { headers: authHeaders });
check(creditBalance, {
  'credit balance decreased': (r) => JSON.parse(r.body).balance === 50.00, // 100 - 50
});

// Check agent received payment
let agentEarnings = http.get(`${baseUrl}/api/v1/agents/consistency-test-agent-001/earnings`);
check(agentEarnings, {
  'agent earnings increased': (r) => JSON.parse(r.body).totalEarnings >= 45.00, // 50 - commission
});
```

### 4. Event Propagation Consistency
```javascript
// Test event-driven data propagation
let eventTest = {
  eventType: 'AGENT_RATING_UPDATED',
  agentId: 'consistency-test-agent-001',
  newRating: 4.8,
  reviewCount: 25
};

// Trigger rating update
let ratingUpdate = http.post(
  `${baseUrl}/api/v1/agents/consistency-test-agent-001/rating`,
  JSON.stringify({
    rating: 4.8,
    review: 'Excellent work on the task',
    taskId: 'consistency-test-task-001'
  }),
  { headers: authHeaders }
);

check(ratingUpdate, {
  'rating update successful': (r) => r.status === 200,
});

// Wait for event propagation
sleep(5);

// Verify rating propagated to all services
let agentProfile = http.get(`${baseUrl}/api/v1/agents/consistency-test-agent-001`);
let searchResults = http.get(`${baseUrl}/api/v1/search/agents?category=Content+Creator`);
let analyticsData = http.get(`${baseUrl}/api/v1/analytics/agent-performance/consistency-test-agent-001`);

check(agentProfile, {
  'agent profile rating updated': (r) => JSON.parse(r.body).rating === 4.8,
});

check(searchResults, {
  'search index rating updated': (r) => {
    let agents = JSON.parse(r.body).agents;
    let testAgent = agents.find(a => a.id === 'consistency-test-agent-001');
    return testAgent && testAgent.rating === 4.8;
  },
});

check(analyticsData, {
  'analytics rating updated': (r) => JSON.parse(r.body).currentRating === 4.8,
});
```

### 5. Conflict Resolution Testing
```javascript
// Test optimistic concurrency control
let conflictTest = {
  resourceId: 'consistency-test-agent-001',
  version: 1
};

// Simulate two clients trying to update agent availability simultaneously
let update1 = {
  availability: false,
  reason: 'Taking a break',
  version: 1
};

let update2 = {
  availability: true,
  reason: 'Ready for new tasks',
  version: 1
};

// Send both updates simultaneously
let updateResults = http.batch([
  http.asyncRequest('PUT', `${baseUrl}/api/v1/agents/consistency-test-agent-001/availability`, {
    headers: authHeaders,
    body: JSON.stringify(update1)
  }),
  http.asyncRequest('PUT', `${baseUrl}/api/v1/agents/consistency-test-agent-001/availability`, {
    headers: authHeaders,
    body: JSON.stringify(update2)
  })
]);

// One should succeed, one should fail with conflict
let successCount = updateResults.filter(r => r.status === 200).length;
let conflictCount = updateResults.filter(r => r.status === 409).length;

check(updateResults, {
  'one update succeeded': () => successCount === 1,
  'one update conflicted': () => conflictCount === 1,
});
```

### 6. Rollback Scenario Testing
```javascript
// Test transaction rollback on failure
let rollbackTest = {
  taskId: 'consistency-test-task-rollback',
  invalidData: true // This will cause validation failure
};

// Attempt transaction that should fail and rollback
let failedTransaction = http.post(
  `${baseUrl}/api/v1/tasks/create-with-payment`,
  JSON.stringify({
    title: 'Rollback Test Task',
    description: 'This task creation should fail',
    category: 'Invalid Category', // Invalid category should cause failure
    budget: 1000000, // Exceeds user balance
    userId: 'consistency-test-user-001'
  }),
  { headers: authHeaders }
);

check(failedTransaction, {
  'transaction failed as expected': (r) => r.status >= 400,
});

// Verify no partial data was created
let taskCheck = http.get(`${baseUrl}/api/v1/tasks/consistency-test-task-rollback`);
let creditCheck = http.get(`${baseUrl}/api/v1/credits/balance`, { headers: authHeaders });

check(taskCheck, {
  'no partial task created': (r) => r.status === 404,
});

check(creditCheck, {
  'credit balance unchanged': (r) => JSON.parse(r.body).balance === 50.00, // From previous test
});
```

## Success Criteria
### Data Consistency Requirements
- **Cross-Service Consistency**: Data identical across all service views
- **Event Propagation**: Changes propagate within 5 seconds
- **Transaction Integrity**: ACID properties maintained for distributed transactions
- **Conflict Resolution**: Concurrent updates handled correctly
- **Rollback Completeness**: Failed transactions leave no partial data

### Performance Consistency
- **Read Consistency**: All reads return consistent data within 1 second of update
- **Write Consistency**: Updates visible across services within 3 seconds
- **Event Processing**: Events processed and applied within 5 seconds
- **Conflict Detection**: Conflicts detected within 100ms

### Audit and Compliance
- **Complete Audit Trail**: All data changes logged with full context
- **Change Attribution**: Every change traceable to specific user/service
- **Data Lineage**: History of data changes maintained
- **Compliance Reporting**: Regulatory reports show consistent data

## Data Integrity Validation
### Database Constraint Verification
```javascript
// Test referential integrity
let integrityChecks = [
  // Verify task references valid user
  {
    query: 'SELECT COUNT(*) FROM tasks t LEFT JOIN users u ON t.userId = u.id WHERE u.id IS NULL',
    expected: 0,
    description: 'No orphaned tasks'
  },
  
  // Verify agent references valid user
  {
    query: 'SELECT COUNT(*) FROM agents a LEFT JOIN users u ON a.userId = u.id WHERE u.id IS NULL',
    expected: 0,
    description: 'No orphaned agents'
  },
  
  // Verify credit transactions reference valid users
  {
    query: 'SELECT COUNT(*) FROM transactions t LEFT JOIN users u ON t.userId = u.id WHERE u.id IS NULL',
    expected: 0,
    description: 'No orphaned transactions'
  }
];
```

### Financial Data Integrity
```javascript
// Verify credit system integrity
let financialIntegrity = {
  // Sum of all transactions should equal current balances
  balanceReconciliation: true,
  
  // No negative balances (unless explicitly allowed)
  negativeBalanceCheck: true,
  
  // All transactions have corresponding audit entries
  auditTrailCompleteness: true,
  
  // Commission calculations are accurate
  commissionAccuracy: true
};
```

## Error Scenarios
### Data Corruption Scenarios
- Partial update failures leaving inconsistent state
- Network failures during distributed transactions
- Database constraint violations
- Event processing failures

### Recovery Validation
- System automatically detects and repairs inconsistencies
- Manual reconciliation procedures work correctly
- Backup and restore maintains data integrity
- Point-in-time recovery produces consistent state

### Monitoring and Detection
- Real-time data consistency monitoring
- Automated alerts for integrity violations
- Regular consistency verification jobs
- Performance impact measurement of consistency checks