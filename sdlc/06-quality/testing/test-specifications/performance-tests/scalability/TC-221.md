# Test Case TC-221: System Throughput and Concurrent User Scalability

## Test Information
- **Test ID**: TC-221
- **Category**: Performance - Scalability
- **Objective**: Verify system processes 1000 req/min with 100 concurrent users maintaining SLA
- **Requirements**: NFR-005, NFR-006, NFR-040, NFR-041
- **Priority**: Critical
- **Test Type**: Performance - Scalability
- **Automation**: Yes

## Performance Criteria
### Throughput Targets
- **Sustained Throughput**: 1000 requests/minute (NFR-005)
- **Concurrent Users**: 100 simultaneous users (NFR-006)
- **Error Rate**: <1% (NFR-005)
- **Response Time**: p95 <100ms maintained under load (NFR-005)

### Scaling Targets
- **Auto-scaling Trigger**: CPU >70% (NFR-040)
- **Scale Range**: 1 to 10 instances (NFR-041)
- **Scale-up Time**: <2 minutes
- **Scale-down Time**: <5 minutes

## Test Configuration
### Load Pattern
- **Phase 1**: Ramp-up to 25 concurrent users (5 minutes)
- **Phase 2**: Ramp-up to 50 concurrent users (5 minutes)
- **Phase 3**: Ramp-up to 75 concurrent users (5 minutes)
- **Phase 4**: Ramp-up to 100 concurrent users (5 minutes)
- **Phase 5**: Sustained load at 100 users (30 minutes)
- **Phase 6**: Ramp-down (10 minutes)

### Request Distribution
- **GET Operations**: 60% (user profiles, agent listings, task details)
- **POST Operations**: 25% (task creation, user registration)
- **PUT Operations**: 12% (profile updates, task status changes)
- **DELETE Operations**: 3% (task cancellation, account deletion)

## Test Environment
### Infrastructure Requirements
- Production-like environment with auto-scaling enabled
- Load balancer with health checks configured
- Multiple service instances ready for scaling
- Database connection pooling configured
- Monitoring and alerting systems active

### Service Configuration
- **Initial Instances**: 2 instances per service
- **Maximum Instances**: 10 instances per service
- **CPU Threshold**: 70% for scale-up trigger
- **Memory Threshold**: 80% for scale-up trigger
- **Scale-down Cooldown**: 5 minutes

## Execution Steps
1. **Environment Setup**
   - Deploy services with auto-scaling configuration
   - Configure load balancer and health checks
   - Set up comprehensive monitoring dashboards
   - Prepare realistic user journey scenarios

2. **Gradual Load Increase**
   - Execute phased load increase pattern
   - Monitor auto-scaling behavior at each phase
   - Track response time degradation points
   - Validate error rate thresholds

3. **Sustained Load Testing**
   - Maintain 100 concurrent users for 30 minutes
   - Monitor system stability and performance
   - Validate consistent throughput delivery
   - Check resource utilization patterns

4. **Scale-down Validation**
   - Gradually reduce load
   - Verify scale-down behavior
   - Ensure no service disruption during scaling
   - Confirm resource cleanup

## Success Criteria
### Performance Requirements
- **Throughput**: Sustained 1000+ requests/minute
- **Concurrent Users**: 100 users supported simultaneously
- **Error Rate**: <1% throughout all phases
- **Response Time**: p95 <100ms maintained
- **Availability**: 99.9% uptime during test

### Scaling Requirements
- **Auto-scaling**: Triggers correctly at 70% CPU
- **Scale-up**: Completes within 2 minutes
- **Scale-down**: Completes within 5 minutes
- **Service Continuity**: No dropped requests during scaling
- **Resource Efficiency**: Optimal instance utilization

## Test Scripts
### Sample Scalability Test
```javascript
export let options = {
  stages: [
    { duration: '5m', target: 25 },   // Phase 1
    { duration: '5m', target: 50 },   // Phase 2
    { duration: '5m', target: 75 },   // Phase 3
    { duration: '5m', target: 100 },  // Phase 4
    { duration: '30m', target: 100 }, // Phase 5 - Sustained
    { duration: '10m', target: 0 },   // Phase 6 - Ramp-down
  ],
  thresholds: {
    http_req_duration: ['p(95)<100'],
    http_req_failed: ['rate<0.01'],
    http_reqs: ['rate>16.6'], // 1000 req/min = 16.6 req/sec
  },
};

export default function() {
  const baseUrl = 'http://api.vibe-match.com';
  const headers = { 'Content-Type': 'application/json' };
  
  // Simulate realistic user journey
  let userSession = {
    userId: randomUserId(),
    sessionId: randomSessionId(),
  };
  
  // 1. User profile retrieval (GET - 60% of requests)
  if (Math.random() < 0.6) {
    let profileResponse = http.get(`${baseUrl}/api/v1/users/${userSession.userId}`);
    check(profileResponse, {
      'profile retrieval success': (r) => r.status === 200,
      'profile response time ok': (r) => r.timings.duration < 100,
    });
  }
  
  // 2. Task creation (POST - 25% of requests)
  else if (Math.random() < 0.85) { // 25% of remaining 40%
    let taskPayload = {
      title: `Scalability Test Task ${Date.now()}`,
      description: 'Task created during scalability testing',
      category: 'Content Creator',
      estimatedDuration: 60,
      maxBudget: 50
    };
    
    let taskResponse = http.post(
      `${baseUrl}/api/v1/tasks`,
      JSON.stringify(taskPayload),
      { headers }
    );
    
    check(taskResponse, {
      'task creation success': (r) => r.status === 201,
      'task creation response time ok': (r) => r.timings.duration < 100,
    });
  }
  
  // 3. Profile update (PUT - 12% of requests)
  else if (Math.random() < 0.92) { // 12% of remaining 15%
    let updatePayload = {
      displayName: `Updated User ${Date.now()}`,
      bio: 'Updated during scalability testing'
    };
    
    let updateResponse = http.put(
      `${baseUrl}/api/v1/users/${userSession.userId}`,
      JSON.stringify(updatePayload),
      { headers }
    );
    
    check(updateResponse, {
      'profile update success': (r) => r.status === 200,
      'profile update response time ok': (r) => r.timings.duration < 100,
    });
  }
  
  // Add realistic think time
  sleep(Math.random() * 2 + 1); // 1-3 seconds
}
```

## Monitoring Focus Areas
### System Metrics
- **CPU Utilization**: Per service and overall
- **Memory Usage**: Heap and non-heap memory
- **Network I/O**: Bandwidth utilization
- **Database Connections**: Pool usage and wait times
- **Request Queue Depth**: Load balancer queue size

### Scaling Behavior
- **Instance Count**: Track scaling events
- **Scaling Latency**: Time to provision new instances
- **Health Check Status**: Service availability during scaling
- **Load Distribution**: Request distribution across instances
- **Resource Utilization**: Efficiency of scaled instances

## Failure Scenarios
### Performance Degradation
- Throughput drops below 1000 requests/minute
- Error rate exceeds 1%
- Response time p95 exceeds 100ms
- System becomes unresponsive under load

### Scaling Failures
- Auto-scaling fails to trigger at 70% CPU
- Scale-up takes longer than 2 minutes
- Scale-down causes service disruption
- Instances fail health checks after scaling
- Load balancer fails to distribute requests

## Recovery Validation
- System performance returns to baseline after load reduction
- All scaled instances are properly decommissioned
- No memory leaks or resource retention
- Database connections properly cleaned up
- Monitoring systems remain functional