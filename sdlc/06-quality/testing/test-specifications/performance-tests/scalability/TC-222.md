# Test Case TC-222: Database Performance and Scaling Validation

## Test Information
- **Test ID**: TC-222
- **Category**: Performance - Database Scalability
- **Objective**: Verify database performance with increasing data volumes and query optimization
- **Requirements**: NFR-042, NFR-043, NFR-007, NFR-008
- **Priority**: Critical
- **Test Type**: Performance - Database
- **Automation**: Yes

## Performance Criteria
### Query Performance Targets
- **Indexed Queries**: <10ms p95 (NFR-042)
- **Complex Aggregations**: <100ms p95
- **Full-text Search**: <50ms p95
- **Write Operations**: <25ms p95

### Data Volume Targets
- **Agent Records**: 1,000 agents (NFR-007)
- **Task Records**: 10,000 tasks/month (NFR-008)
- **User Records**: Support for 10,000+ users
- **Transaction Records**: 100,000+ credit transactions

## Test Configuration
### Load Pattern
- **Phase 1**: Baseline with 1K records (5 minutes)
- **Phase 2**: Scale to 10K records (10 minutes)
- **Phase 3**: Scale to 100K records (15 minutes)
- **Phase 4**: Scale to 1M records (20 minutes)
- **Phase 5**: Sustained load with full dataset (30 minutes)

### Query Mix
- **Simple Selects**: 40% (user profiles, agent details)
- **Filtered Searches**: 30% (agent discovery, task filtering)
- **Aggregations**: 15% (analytics, reporting)
- **Full-text Search**: 10% (content search)
- **Write Operations**: 5% (inserts, updates)

## Test Environment
### Database Configuration
- **Firestore Native Mode** configured for production
- **Composite Indexes** created for all query patterns
- **Single-field Indexes** optimized
- **Connection Pooling** configured with appropriate limits
- **Monitoring** enabled for query performance

### Data Distribution
- **Users**: 10,000 user records with realistic profiles
- **Agents**: 1,000 agent records with skills and availability
- **Tasks**: 100,000 task records across all categories
- **Transactions**: 500,000 credit transaction records
- **Ratings**: 250,000 rating and review records

## Execution Steps
1. **Environment Setup**
   - Deploy Firestore with production configuration
   - Create all required indexes
   - Load test data in phases
   - Configure monitoring and alerting

2. **Incremental Data Loading**
   - Execute queries with increasing data volumes
   - Monitor query performance at each phase
   - Validate index effectiveness
   - Track memory and CPU utilization

3. **Query Pattern Validation**
   - Execute diverse query patterns
   - Test cursor-based pagination (NFR-043)
   - Validate compound query performance
   - Check full-text search efficiency

4. **Write Performance Testing**
   - Test batch write operations
   - Validate transaction consistency
   - Check concurrent write handling
   - Monitor write throughput

## Success Criteria
### Query Performance
- **Simple Queries**: p95 <10ms consistently
- **Filtered Queries**: p95 <25ms with proper indexes
- **Aggregations**: p95 <100ms for complex operations
- **Pagination**: Cursor-based pagination working efficiently
- **Index Usage**: 100% of queries using appropriate indexes

### Write Performance
- **Insert Operations**: p95 <25ms
- **Update Operations**: p95 <30ms
- **Batch Operations**: Process 100 records <500ms
- **Transaction Consistency**: 100% ACID compliance
- **Concurrent Writes**: No deadlocks or conflicts

## Test Scripts
### Sample Database Performance Test
```javascript
export let options = {
  stages: [
    { duration: '5m', target: 10 },   // Baseline
    { duration: '10m', target: 25 },  // 10K records
    { duration: '15m', target: 50 },  // 100K records
    { duration: '20m', target: 75 },  // 1M records
    { duration: '30m', target: 100 }, // Sustained full load
  ],
  thresholds: {
    http_req_duration: ['p(95)<50'],
    http_req_failed: ['rate<0.01'],
  },
};

export default function() {
  const baseUrl = 'http://api.vibe-match.com';
  const headers = { 'Content-Type': 'application/json' };
  
  // Simple indexed query (40% of requests)
  if (Math.random() < 0.4) {
    let userQuery = http.get(`${baseUrl}/api/v1/users/${randomUserId()}`);
    check(userQuery, {
      'user query success': (r) => r.status === 200,
      'user query fast': (r) => r.timings.duration < 10,
    });
  }
  
  // Filtered search with compound index (30% of requests)
  else if (Math.random() < 0.7) {
    let agentFilters = {
      category: 'Content Creator',
      minRating: 4.0,
      availability: true,
      location: 'US-CA'
    };
    
    let filterQuery = http.get(
      `${baseUrl}/api/v1/agents?` + new URLSearchParams(agentFilters)
    );
    
    check(filterQuery, {
      'filtered query success': (r) => r.status === 200,
      'filtered query fast': (r) => r.timings.duration < 25,
      'results returned': (r) => JSON.parse(r.body).agents.length > 0,
    });
  }
  
  // Aggregation query (15% of requests)
  else if (Math.random() < 0.85) {
    let analyticsQuery = http.get(
      `${baseUrl}/api/v1/analytics/agent-performance?timeRange=30d&groupBy=category`
    );
    
    check(analyticsQuery, {
      'analytics query success': (r) => r.status === 200,
      'analytics query acceptable': (r) => r.timings.duration < 100,
      'aggregated data returned': (r) => JSON.parse(r.body).summary !== undefined,
    });
  }
  
  // Full-text search (10% of requests)
  else if (Math.random() < 0.95) {
    let searchQuery = http.get(
      `${baseUrl}/api/v1/search?q=content+creation&type=agents&limit=20`
    );
    
    check(searchQuery, {
      'search query success': (r) => r.status === 200,
      'search query fast': (r) => r.timings.duration < 50,
      'search results relevant': (r) => JSON.parse(r.body).results.length > 0,
    });
  }
  
  // Write operation (5% of requests)
  else {
    let taskData = {
      title: `DB Test Task ${Date.now()}`,
      description: 'Database performance testing task',
      category: 'Content Creator',
      userId: randomUserId(),
      estimatedDuration: 30,
      maxBudget: 75
    };
    
    let writeQuery = http.post(
      `${baseUrl}/api/v1/tasks`,
      JSON.stringify(taskData),
      { headers }
    );
    
    check(writeQuery, {
      'write operation success': (r) => r.status === 201,
      'write operation fast': (r) => r.timings.duration < 25,
      'task ID returned': (r) => JSON.parse(r.body).taskId !== undefined,
    });
  }
  
  sleep(0.1); // Small pause to prevent overwhelming the database
}
```

## Index Performance Validation
### Required Indexes
- **Users**: email, createdAt, status
- **Agents**: category, rating, availability, location
- **Tasks**: userId, status, createdAt, category, priority
- **Transactions**: userId, timestamp, amount, type
- **Composite Indexes**: 
  - Agents: (category, rating, availability)
  - Tasks: (userId, status, createdAt)
  - Transactions: (userId, type, timestamp)

### Index Monitoring
- Query execution plans analysis
- Index hit rates and efficiency
- Index maintenance overhead
- Storage overhead per index
- Query optimizer behavior

## Pagination Testing
### Cursor-based Pagination (NFR-043)
- **Page Size**: Test with 10, 20, 50, 100 records per page
- **Deep Pagination**: Navigate to page 100+ efficiently
- **Sorting**: Maintain consistent ordering with pagination
- **Performance**: Each page load <50ms regardless of depth

### Pagination Scenarios
```javascript
// Test cursor-based pagination
let firstPage = http.get(`${baseUrl}/api/v1/tasks?limit=20`);
let cursor = JSON.parse(firstPage.body).nextCursor;

let secondPage = http.get(`${baseUrl}/api/v1/tasks?limit=20&cursor=${cursor}`);
check(secondPage, {
  'pagination consistent': (r) => r.status === 200,
  'pagination fast': (r) => r.timings.duration < 50,
});
```

## Failure Scenarios
### Query Performance Degradation
- Queries exceeding 10ms p95 for simple operations
- Index scans instead of index seeks
- Full collection scans on large datasets
- Pagination performance degradation with depth

### Write Performance Issues
- Write operations exceeding 25ms p95
- Transaction deadlocks or conflicts
- Batch operation failures
- Consistency violations

### Resource Constraints
- Database connection pool exhaustion
- Memory limits reached during operations
- CPU spikes during complex queries
- Storage I/O bottlenecks

## Recovery Validation
- Query performance returns to baseline after load
- Index efficiency maintained after heavy usage
- Write performance stable after load testing
- No data corruption or consistency issues
- Connection pools properly managed and released