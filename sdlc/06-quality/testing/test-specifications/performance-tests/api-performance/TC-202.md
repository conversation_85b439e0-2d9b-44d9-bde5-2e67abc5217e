# Test Case TC-202: POST/PUT Endpoint Response Time Validation

## Test Information
- **Test ID**: TC-202
- **Category**: Performance - API Response Time
- **Objective**: Verify POST/PUT endpoints meet 100ms p95 response time requirement
- **Requirements**: NFR-003, FR-010, FR-026, FR-032, FR-035
- **Priority**: Critical
- **Test Type**: Performance
- **Automation**: Yes

## Performance Criteria
### Response Time Targets
- **p50**: ≤50ms
- **p95**: ≤100ms
- **p99**: ≤200ms
- **Max Acceptable**: 400ms

### Throughput Targets
- **Sustained Rate**: 200 requests/minute
- **Peak Rate**: 400 requests/minute
- **Concurrent Users**: 50 simultaneous write operations
- **Error Rate**: <1%

## Test Configuration
### Load Pattern
- **Ramp-up**: 1 minute to reach full load
- **Sustained Duration**: 8 minutes at full load
- **Ramp-down**: 1 minute to reduce load
- **Think Time**: 2-5 seconds between requests

### Test Data
- **<PERSON><PERSON><PERSON> Mix**:
  - Task creation: 35%
  - User profile updates: 25%
  - Agent profile updates: 20%
  - Task status updates: 15%
  - Agent status changes: 5%

## Test Environment
### Infrastructure Requirements
- Production-like environment with write replication
- Database transaction logging enabled
- Validation middleware configured
- File upload capabilities (for attachments)
- Audit logging enabled

### Endpoints Under Test
- `POST /api/v1/tasks`
- `PUT /api/v1/users/{userId}`
- `PUT /api/v1/agents/{agentId}`
- `PUT /api/v1/tasks/{taskId}/status`
- `POST /api/v1/agents/{agentId}/availability`

## Execution Steps
1. **Environment Setup**
   - Configure database for write-heavy operations
   - Set up validation middleware
   - Enable comprehensive monitoring
   - Prepare realistic payload data

2. **Load Generation**
   - Execute mixed write operation scenarios
   - Include validation failures (10% of requests)
   - Test with varying payload sizes
   - Monitor database write performance

3. **Validation**
   - Analyze write operation response times
   - Verify data consistency after operations
   - Check validation error handling
   - Monitor transaction rollback scenarios

## Success Criteria
- **Response Time**: p95 ≤100ms, p99 ≤200ms
- **Error Rate**: <1% for valid requests
- **Data Consistency**: 100% consistency after writes
- **Transaction Success**: >99% for valid operations
- **Resource Usage**: Database write capacity within limits

## Test Scripts
### Sample Write Performance Test
```javascript
export let options = {
  stages: [
    { duration: '1m', target: 50 },
    { duration: '8m', target: 50 },
    { duration: '1m', target: 0 },
  ],
  thresholds: {
    http_req_duration: ['p(95)<100', 'p(99)<200'],
    http_req_failed: ['rate<0.01'],
  },
};

export default function() {
  const headers = { 'Content-Type': 'application/json' };
  
  // Task creation
  let taskPayload = {
    title: `Performance Test Task ${Date.now()}`,
    description: 'Test task for performance validation',
    category: 'Content Creator',
    priority: 'Medium',
    estimatedDuration: 30
  };
  
  let taskResponse = http.post(
    'http://api.vibe-match.com/api/v1/tasks',
    JSON.stringify(taskPayload),
    { headers: headers }
  );
  
  check(taskResponse, {
    'task creation status is 201': (r) => r.status === 201,
    'task creation response time < 100ms': (r) => r.timings.duration < 100,
    'task ID returned': (r) => JSON.parse(r.body).taskId !== undefined,
  });
  
  // User profile update
  let userUpdatePayload = {
    displayName: `Updated User ${Date.now()}`,
    bio: 'Updated bio for performance testing'
  };
  
  let userResponse = http.put(
    `http://api.vibe-match.com/api/v1/users/${randomUserId()}`,
    JSON.stringify(userUpdatePayload),
    { headers: headers }
  );
  
  check(userResponse, {
    'user update status is 200': (r) => r.status === 200,
    'user update response time < 100ms': (r) => r.timings.duration < 100,
  });
}
```

## Data Validation Tests
### Payload Variations
- **Small Payloads**: <1KB (user updates)
- **Medium Payloads**: 1-10KB (task creation with description)
- **Large Payloads**: 10-100KB (agent profiles with portfolios)
- **Invalid Payloads**: Test validation error handling

### Database Impact Monitoring
- Transaction duration
- Lock contention
- Write throughput
- Replication lag
- Index update performance

## Failure Scenarios
- Response time degradation beyond 100ms p95
- Database transaction failures
- Validation middleware bottlenecks
- Memory exhaustion from large payloads
- Database deadlock scenarios

## Recovery Validation
- System performance returns to baseline
- No database corruption or inconsistencies
- Transaction logs properly maintained
- Error handling gracefully degrades performance