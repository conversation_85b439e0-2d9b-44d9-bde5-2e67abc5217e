# Test Case TC-118: MFA Backup Codes

## Objective
Verify that the system generates and accepts backup codes for MFA recovery.

## Requirement
- **FR-183**: System shall generate 8 backup codes of 8 characters each for MFA recovery.

## Preconditions
- A user account is enrolled in MFA.

## Test Steps
1. The user navigates to their security settings and requests to view their backup codes.
2. The user logs out and attempts to log in again.
3. When prompted for an MFA code, the user selects the option to use a backup code.
4. The user enters one of the generated backup codes.
5. The user attempts to use the same backup code a second time.

## Expected Results
- The system shall display a list of 8 unique, 8-character backup codes.
- The user shall be successfully authenticated after entering a valid backup code.
- The used backup code shall be invalidated and cannot be used again.
- An attempt to reuse a backup code shall result in a login failure.

## Priority
- High

## Status
- **TBD**
