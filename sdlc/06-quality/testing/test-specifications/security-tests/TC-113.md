# Test Case TC-113: Task Access Rules

## Objective
Verify that the system enforces task access rules correctly.

## Requirement
- **FR-143**: System shall enforce task access rules (owner, matched agent, managers only).

## Preconditions
- A task exists, created by "UserA".
- The task has been matched with "AgentA".
- User accounts exist for "UserA", "AgentA", "<PERSON>r<PERSON>" (another user), and "PlatformManager".

## Test Steps
1. As "User<PERSON>" (the owner), attempt to view the task.
2. As "Agent<PERSON>" (the matched agent), attempt to view the task.
3. As "PlatformManager", attempt to view the task.
4. As "User<PERSON>" (an unrelated user), attempt to view the task.

## Expected Results
- "UserA" shall be able to successfully view the task.
- "AgentA" shall be able to successfully view the task.
- "PlatformManager" shall be able to successfully view the task.
- "UserB" shall receive a 403 Forbidden or 404 Not Found error.

## Status
- **TBD**
