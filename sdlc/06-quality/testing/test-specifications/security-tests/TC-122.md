# Test Case TC-122: Progressive Delays Between Failed Logins

## Objective
Verify that the system implements progressive delays between failed login attempts.

## Requirement
- **FR-187**: System shall implement progressive delays between failed login attempts.

## Preconditions
- A user account exists.

## Test Steps
1. Attempt to log in with an incorrect password.
2. Immediately attempt to log in again with an incorrect password.
3. Repeat this process 5 times, measuring the time it takes for the system to respond to each attempt.

## Expected Results
- The system shall enforce increasing delays between each failed login attempt.
- The delays should be approximately 1s, 2s, 4s, 8s, and 16s for attempts 1 through 5.

## Priority
- High

## Status
- **TBD**
