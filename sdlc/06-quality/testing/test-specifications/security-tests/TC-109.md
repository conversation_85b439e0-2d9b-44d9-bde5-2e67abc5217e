# Test Case TC-109: Prevent Privilege Escalation

## Objective
Verify that the system prevents users from escalating their privileges.

## Requirement
- **FR-106**: System shall prevent privilege escalation through API manipulation.

## Preconditions
- A user account with a "User" role exists.

## Test Steps
1. As the "User", obtain an authentication token.
2. Attempt to call an API endpoint to change the user's role to "Admin", even if such an endpoint is not publicly documented.
3. Attempt to use the modified token (if any) to access an admin-only endpoint.

## Expected Results
- The API call to change the user's role shall fail with a 403 Forbidden error.
- The user's role shall remain "User".
- The user shall not be able to access the admin-only endpoint.

## Status
- **TBD**
