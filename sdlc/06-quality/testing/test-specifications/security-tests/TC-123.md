# Test Case TC-123: Session Timeout for Standard Users

## Objective
Verify that the system enforces a session timeout of 30 minutes for standard users.

## Requirement
- **FR-188**: System shall enforce session timeout of 30 minutes for standard users.

## Preconditions
- A standard user account exists.

## Test Steps
1. Log in as the standard user.
2. Perform some actions within the application.
3. Leave the session idle for 31 minutes.
4. Attempt to perform another action that requires authentication.

## Expected Results
- After 31 minutes of inactivity, the user's session shall be invalidated.
- The attempt to perform an action after the timeout shall fail, and the user shall be prompted to log in again.

## Priority
- High

## Status
- **TBD**
