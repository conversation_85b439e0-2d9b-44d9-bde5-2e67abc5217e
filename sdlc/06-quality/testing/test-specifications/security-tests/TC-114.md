# Test Case TC-114: Transaction Access Rules

## Objective
Verify that the system restricts transaction access to participants and authorized financial roles.

## Requirement
- **FR-144**: System shall restrict transaction access to participants and authorized financial roles.

## Preconditions
- A transaction exists between "UserA" and "AgentA".
- User accounts exist for "User<PERSON>", "AgentA", "<PERSON>r<PERSON>" (another user), and "ComplianceOfficer".

## Test Steps
1. As "UserA" (a participant), attempt to view the transaction.
2. As "AgentA" (a participant), attempt to view the transaction.
3. As "ComplianceOfficer", attempt to view the transaction.
4. As "User<PERSON>" (an unrelated user), attempt to view the transaction.

## Expected Results
- "UserA" shall be able to successfully view the transaction.
- "AgentA" shall be able to successfully view the transaction.
- "ComplianceOfficer" shall be able to successfully view the transaction.
- "UserB" shall receive a 403 Forbidden or 404 Not Found error.

## Status
- **TBD**
