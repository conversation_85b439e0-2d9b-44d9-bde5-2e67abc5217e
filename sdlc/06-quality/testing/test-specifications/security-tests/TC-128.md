# Test Case TC-128: Authorization Failure Logging

## Objective
Verify that the system logs all authorization failures with full context.

## Requirement
- **FR-193**: System shall log all authorization failures with full context.

## Preconditions
- A user account with a "User" role exists.
- An API endpoint that requires "Admin" permissions is defined.
- The logging service is running.

## Test Steps
1. As the "User", attempt to access the admin-only API endpoint.
2. Check the security logs.

## Expected Results
- A log entry shall be created for the authorization failure.
- The log entry shall contain the user ID, the resource that was accessed, the operation that was attempted, the timestamp, and the reason for the failure.

## Priority
- High

## Status
- **TBD**
