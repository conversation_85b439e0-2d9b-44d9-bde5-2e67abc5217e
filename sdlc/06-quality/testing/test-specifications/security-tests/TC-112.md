# Test Case TC-112: API Endpoint Authorization Middleware

## Objective
Verify that the API endpoint authorization middleware checks both role and resource access.

## Requirement
- **FR-142**: System shall implement API endpoint authorization middleware checking both role and resource access.

## Preconditions
- Two user accounts, UserA and UserB, exist.
- UserA has created a task.
- An API endpoint exists to view a task by its ID.

## Test Steps
1. As UserA, access the endpoint to view the task created by UserA.
2. As UserB, attempt to access the same endpoint to view the task created by UserA.

## Expected Results
- UserA shall be able to successfully view the task.
- UserB shall receive a 403 Forbidden or 404 Not Found error, as they are not the owner of the task.

## Status
- **TBD**
