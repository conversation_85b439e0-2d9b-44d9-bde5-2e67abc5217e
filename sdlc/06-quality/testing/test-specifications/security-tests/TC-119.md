# Test Case TC-119: MFA for Administrative Roles

## Objective
Verify that MFA is required for all administrative role operations.

## Requirement
- **FR-184**: System shall require MFA for all administrative role operations.

## Preconditions
- An administrator account exists and is enrolled in MFA.
- An administrative operation is defined (e.g., accessing a system configuration panel).

## Test Steps
1. The administrator logs in with their email and password.
2. The system prompts for and verifies the MFA code.
3. The administrator attempts to access the administrative operation.
4. In a separate session, simulate a scenario where an administrator's session is hijacked after the initial login but before performing the sensitive operation. Attempt to perform the admin operation without a fresh MFA check.

## Expected Results
- The administrator shall be required to re-enter their MFA code or perform a step-up authentication before being granted access to the administrative operation.
- The hijacked session shall be blocked from performing the administrative operation without re-authentication.

## Priority
- High

## Status
- **TBD**
