# Test Case TC-129: Real-time Security Log Forwarding

## Objective
Verify that the system forwards security logs to a centralized SIEM in real-time.

## Requirement
- **FR-194**: System shall forward security logs to SIEM in real-time.

## Preconditions
- The system is configured to forward logs to a SIEM.
- A security event is generated (e.g., a failed login attempt).

## Test Steps
1. Trigger a security event (e.g., a failed login).
2. Check the SIEM for the corresponding log entry.

## Expected Results
- The security log entry shall appear in the SIEM within 5 seconds of the event.
- The log entry in the SIEM shall be in JSON format and transmitted over a TLS-encrypted connection.

## Priority
- High

## Status
- **TBD**
