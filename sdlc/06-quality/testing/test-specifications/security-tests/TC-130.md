# Test Case TC-130: Security Log Retention

## Objective
Verify that the system retains security logs for a minimum of 1 year.

## Requirement
- **FR-195**: System shall retain security logs for minimum 1 year.

## Preconditions
- The system has been in operation for at least 13 months.
- Security logs have been generated throughout this period.

## Test Steps
1. Attempt to retrieve security logs from 13 months ago.
2. Attempt to retrieve security logs from 11 months ago.

## Expected Results
- The attempt to retrieve logs from 13 months ago shall fail or return no results.
- The attempt to retrieve logs from 11 months ago shall succeed, and the logs shall be available in a compressed, searchable format.

## Priority
- High

## Status
- **TBD**
