# Test Case TC-121: Account Lockout Mechanism

## Objective
Verify that the system locks user accounts after 5 consecutive failed login attempts.

## Requirement
- **FR-186**: System shall lock user accounts after 5 consecutive failed login attempts.

## Preconditions
- A user account exists.

## Test Steps
1. Attempt to log in to the user account with an incorrect password 5 times in a row.
2. After the 5th failed attempt, try to log in with the correct password.
3. Wait for the specified lockout duration (e.g., 30 minutes) and attempt to log in again with the correct password.

## Expected Results
- After the 5th failed login attempt, the account shall be locked.
- The login attempt with the correct password immediately after the 5th failure shall be denied with a message indicating the account is locked.
- After the lockout duration has passed, the user shall be able to log in successfully with the correct password.

## Priority
- High

## Status
- **TBD**
