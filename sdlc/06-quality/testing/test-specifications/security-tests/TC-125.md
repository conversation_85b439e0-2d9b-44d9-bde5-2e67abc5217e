# Test Case TC-125: Password History

## Objective
Verify that the system maintains a password history of the last 5 passwords.

## Requirement
- **FR-190**: System shall maintain password history of last 5 passwords.

## Preconditions
- A user account exists.

## Test Steps
1. The user changes their password to "NewPassword1!".
2. The user changes their password 4 more times to different passwords.
3. The user attempts to change their password back to "NewPassword1!".

## Expected Results
- The system shall prevent the user from reusing any of their last 5 passwords.
- The attempt to change the password to "NewPassword1!" shall fail with an appropriate error message.

## Priority
- High

## Status
- **TBD**
