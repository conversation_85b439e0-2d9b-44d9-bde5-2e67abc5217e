# Test Case TC-117: TOTP-Based Multi-Factor Authentication (MFA)

## Objective
Verify that the system implements TOTP-based multi-factor authentication.

## Requirement
- **FR-182**: System shall implement TOTP-based multi-factor authentication.

## Preconditions
- A user account exists.
- The user has enrolled in MFA and has a TOTP app (e.g., Google Authenticator) configured.

## Test Steps
1. The user logs in with their email and password.
2. The system prompts the user for their MFA code.
3. The user enters a valid TOTP code from their authenticator app.
4. The user attempts to log in again and enters an invalid or expired TOTP code.

## Expected Results
- After entering a valid TOTP code, the user shall be successfully authenticated and logged in.
- When an invalid or expired code is entered, the login attempt shall fail with an appropriate error message.

## Priority
- High

## Status
- **TBD**
