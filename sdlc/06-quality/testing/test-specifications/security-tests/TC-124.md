# Test Case TC-124: Session Timeout for Administrative Users

## Objective
Verify that the system enforces a session timeout of 8 hours for administrative users.

## Requirement
- **FR-189**: System shall enforce session timeout of 8 hours for administrative users.

## Preconditions
- An administrative user account exists.

## Test Steps
1. Log in as the administrative user.
2. Perform some actions within the application.
3. Leave the session idle for 8 hours and 1 minute.
4. Attempt to perform another action that requires authentication.

## Expected Results
- After 8 hours and 1 minute of inactivity, the user's session shall be invalidated.
- The attempt to perform an action after the timeout shall fail, and the user shall be prompted to log in again.

## Priority
- High

## Status
- **TBD**
