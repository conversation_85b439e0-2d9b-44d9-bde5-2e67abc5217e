# Test Case TC-111: Enforce Role Hierarchy with Inheritance

## Objective
Verify that the system enforces the role hierarchy with permission inheritance.

## Requirement
- **FR-108**: System shall enforce role hierarchy with permission inheritance (Admin > Fraud Manager > Fraud Analyst).

## Preconditions
- User accounts with the roles "Ad<PERSON>", "Fraud Manager", and "Fraud Analyst" exist.
- An API endpoint that requires "Fraud Analyst" permissions is defined.

## Test Steps
1. As the "Fraud Analyst", access the endpoint.
2. As the "Fraud Manager", access the same endpoint.
3. As the "Admin", access the same endpoint.

## Expected Results
- The "Fraud Analyst" shall be able to access the endpoint.
- The "Fraud Manager" shall be able to access the endpoint, inheriting the permissions of the "Fraud Analyst".
- The "Admin" shall be able to access the endpoint, inheriting the permissions of the "Fraud Manager".

## Status
- **TBD**
