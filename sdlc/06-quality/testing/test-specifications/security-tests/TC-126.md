# Test Case TC-126: Password Expiration

## Objective
Verify that the system expires user passwords after 90 days.

## Requirement
- **FR-191**: System shall expire user passwords after 90 days.

## Preconditions
- A user account exists with a password that was set 91 days ago.

## Test Steps
1. The user attempts to log in with their 91-day-old password.

## Expected Results
- The user shall be prompted to change their password upon login.
- The user shall not be able to access the application's main functionality until the password has been changed.

## Priority
- Medium

## Status
- **TBD**
