# Test Case TC-116: Emergency Access Procedures

## Objective
Verify that the system supports emergency access procedures with admin override capabilities.

## Requirement
- **FR-146**: System shall support emergency access procedures with admin override capabilities.

## Preconditions
- A critical incident has been declared.
- An administrator account with emergency access privileges exists.
- A standard user account is locked or inaccessible.

## Test Steps
1. As the administrator, activate the emergency access procedure for the locked user account.
2. Attempt to access the user's account or data.
3. Deactivate the emergency access procedure.

## Expected Results
- The administrator shall be able to gain temporary access to the user's account or data.
- All actions taken during the emergency access session shall be logged in a high-priority audit trail.
- Once deactivated, the administrator's access shall be revoked.

## Priority
- Medium

## Status
- **TBD**
