# Test Case TC-120: MFA Enrollment During Onboarding

## Objective
Verify that the system supports MFA enrollment during user onboarding.

## Requirement
- **FR-185**: System shall support MFA enrollment during user onboarding.

## Preconditions
- A new user is registering for an account.

## Test Steps
1. The user completes the initial registration steps (email, password).
2. The system presents the user with the option to enroll in MFA.
3. The user selects to enroll and is presented with a QR code.
4. The user scans the QR code with their authenticator app and enters the generated TOTP code to verify.

## Expected Results
- The user shall be able to seamlessly enroll in MFA as part of the onboarding process.
- Upon successful verification, the user's account shall be configured to require MFA for future logins.
- The user should also be prompted to save their backup codes.

## Priority
- High

## Status
- **TBD**
