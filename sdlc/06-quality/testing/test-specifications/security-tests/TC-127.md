# Test Case TC-127: Security Logging and Monitoring

## Objective
Verify that the system logs all authentication attempts with outcome and metadata.

## Requirement
- **FR-192**: System shall log all authentication attempts with outcome and metadata.

## Preconditions
- A user account exists.
- The logging service is running.

## Test Steps
1. Attempt to log in with a correct password.
2. Attempt to log in with an incorrect password.
3. Check the security logs.

## Expected Results
- A log entry shall be created for both the successful and failed login attempts.
- Each log entry shall contain the timestamp, user ID, IP address, user agent, and the outcome of the authentication attempt (success/failure).

## Priority
- High

## Status
- **TBD**
