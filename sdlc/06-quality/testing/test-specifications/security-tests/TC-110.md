# Test Case TC-110: Granular Permission Matrix

## Objective
Verify that the system implements a granular permission matrix.

## Requirement
- **FR-107**: System shall implement granular permission matrix with 9 roles and 40+ permission types.

## Preconditions
- User accounts for each of the 9 roles exist.
- API endpoints that exercise a variety of the 40+ permissions are defined.

## Test Steps
1. For each role, attempt to access an API endpoint that the role *should* have permission for.
2. For each role, attempt to access an API endpoint that the role *should not* have permission for.

## Expected Results
- All attempts to access permitted endpoints shall succeed.
- All attempts to access forbidden endpoints shall fail with a 403 Forbidden error.
- This should hold true for all 9 roles and a representative sample of the 40+ permissions.

## Status
- **TBD**
